# Sponsors Management System

## Overview
The Sponsors Management system provides a complete CRUD interface for managing sponsors in the PadelRating application. It follows the same patterns and UI design as the existing Commercials and Clubs management systems.

## Features

### Database Structure
- **Table**: `sponsors`
- **Primary Key**: `sponsor_id` (6-character unique alphanumeric ID)
- **Foreign Key**: `commercial_id` (references commercials table)
- **Fields**:
  - `sponsor_id` - Unique 6-character ID (auto-generated)
  - `company_name` - Company name (max 50 chars)
  - `contact` - Contact person name (max 50 chars)
  - `email` - Email address (max 30 chars, unique)
  - `phone` - Phone number (max 15 chars)
  - `tax_id` - Tax ID (max 12 chars)
  - `postal_code` - Postal code (max 10 chars)
  - `country` - ISO2 country code (2 chars)
  - `commercial_id` - Foreign key to commercials table
  - `status` - Status (1=Active, 0=Inactive)
  - `logo` - Logo image path
  - `created_at` / `updated_at` - Timestamps

### Model Features
- **Unique ID Generation**: Automatic 6-character alphanumeric ID generation
- **Relationships**: 
  - `belongsTo` Commercial
  - `belongsTo` Country (via ISO2 code)
- **Accessors**: 
  - `logo_url` - Full URL to logo image
  - `status_badge` - Human-readable status
- **Events**: Automatic stats updates on create/delete

### Admin Interface

#### Navigation
- Added to admin sidebar under "Sponsors Management"
- Uses star icon for visual identification
- Route: `/sponsors`

#### Sponsors Listing (`/sponsors`)
- **Search**: Search by company name, sponsor ID, email, phone, tax ID, contact, or commercial name
- **Filtering**: Filter by status (Active/Inactive)
- **Pagination**: 10 records per page
- **Actions**: View, Edit, Delete
- **Display**: Shows company logo, name, contact, commercial, and status

#### Add/Edit Sponsor (`/sponsors/create`, `/sponsors/{id}/edit`)
- **Form Sections**:
  - Basic Information: Company name, email, phone, contact, tax ID, commercial selection, status
  - Additional Information: Postal code, country (searchable dropdown), logo upload
- **Validation**: Email uniqueness, required fields, file upload validation
- **Features**:
  - Country search with dropdown (limit 10 results)
  - Logo upload with preview
  - Commercial selection dropdown

### File Upload
- **Storage**: `storage/app/public/sponsors/logos/`
- **Validation**: Image files only, max 2MB
- **Features**: Preview on upload, existing logo display on edit

### Routes
```php
// Admin routes (protected by auth:admin middleware)
Route::get('sponsors', SponsorsManagement::class)->name('sponsors.index');
Route::get('sponsors/create', SponsorForm::class)->name('sponsors.create');
Route::get('sponsors/{id}/edit', SponsorForm::class)->name('sponsors.edit');
```

### Livewire Components

#### SponsorsManagement
- **Location**: `app/Livewire/Sponsors/SponsorsManagement.php`
- **Features**: Search, filter, pagination, delete confirmation
- **Methods**: `confirmDelete()`, `deleteSponsor()`

#### SponsorForm
- **Location**: `app/Livewire/Sponsors/SponsorForm.php`
- **Features**: Create/edit forms, file upload, country search
- **Methods**: `save()`, `loadCountries()`, `selectCountry()`

### Views
- **Management**: `resources/views/livewire/sponsors/sponsors-management.blade.php`
- **Form**: `resources/views/livewire/sponsors/sponsor-form.blade.php`

## Usage

### Creating a Sponsor
1. Navigate to Sponsors Management
2. Click "Add Sponsor"
3. Fill in required fields (company name, email, commercial)
4. Optionally add contact info, logo, and address details
5. Save

### Editing a Sponsor
1. Navigate to Sponsors Management
2. Click edit icon for desired sponsor
3. Modify fields as needed
4. Save changes

### Deleting a Sponsor
1. Navigate to Sponsors Management
2. Click delete icon for desired sponsor
3. Confirm deletion in modal
4. Logo file is automatically deleted from storage

### Searching/Filtering
- Use search box to find sponsors by various criteria
- Use status dropdown to filter by Active/Inactive
- Results update in real-time

## Technical Notes

### Stats Integration
- Sponsor count is automatically tracked in the `stats` table
- `nbr_sponsors` field is incremented on create and updated on delete

### File Storage
- Logo files are stored in `storage/app/public/sponsors/logos/`
- Storage link must be created: `php artisan storage:link`
- Files are automatically deleted when sponsor is deleted

### Country Integration
- Uses existing `countries` table for country selection
- Stores ISO2 country codes (2 characters)
- Provides searchable dropdown with country names

### Validation Rules
- Company name: required, max 50 characters
- Email: required, unique, max 30 characters
- Phone: optional, max 15 characters
- Contact: optional, max 50 characters
- Tax ID: optional, max 12 characters
- Postal code: optional, max 10 characters
- Country: optional, must be valid ISO2 code
- Commercial: required, must exist in commercials table
- Status: required, must be 0 or 1
- Logo: optional, must be image, max 2MB

## Installation

The system is ready to use after running:
```bash
php artisan migrate
php artisan storage:link
```

The migration creates the sponsors table with proper foreign key constraints and indexes.
