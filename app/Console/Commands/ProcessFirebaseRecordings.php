<?php

namespace App\Console\Commands;

use App\Models\Recording;
use App\Services\FirebaseDatabaseService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ProcessFirebaseRecordings extends Command
{
    protected $firebaseService;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'recordings:process-expired';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process expired recordings by updating status to completed and clearing Firebase data';

    /**
     * Create a new command instance.
     */
    public function __construct(FirebaseDatabaseService $firebaseService)
    {
        parent::__construct();
        $this->firebaseService = $firebaseService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to process expired recordings...');

        try {
            // Get current date and time
            $today = Carbon::today()->format('Y-m-d');
            $currentTime = Carbon::now()->format('H:i:s');

            // Find recordings that are not completed and have expired
            $expiredRecordings = Recording::where('status', '!=', 'completed')
                ->where('date', $today)
                ->where('end_time', '<', $currentTime)
                ->get();

            if ($expiredRecordings->isEmpty()) {
                $this->info('No expired recordings found to process.');
                return 0;
            }

            $this->info("Found {$expiredRecordings->count()} expired recordings to process.");

            $processedCount = 0;
            $errorCount = 0;

            foreach ($expiredRecordings as $recording) {
                try {
                    // Update recording status to completed
                    $recording->status = 'completed';
                    $recording->save();

                    // Clear the current recording in Firebase
                    $this->firebaseService->clearCurrentRecording($recording->player_id);

                    $processedCount++;

                    $this->line("✓ Processed recording ID: {$recording->id} for player: {$recording->player_id}");

                } catch (\Exception $e) {
                    $errorCount++;

                    Log::error('Failed to process expired recording', [
                        'recording_id' => $recording->id,
                        'player_id' => $recording->player_id,
                        'error' => $e->getMessage()
                    ]);

                    $this->error("✗ Failed to process recording ID: {$recording->id} - {$e->getMessage()}");
                }
            }

            $this->info("Processing completed. Processed: {$processedCount}, Errors: {$errorCount}");

            return 0;

        } catch (\Exception $e) {
            $this->error("Command failed: {$e->getMessage()}");

            Log::error('ProcessFirebaseRecordings command failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return 1;
        }
    }
}
