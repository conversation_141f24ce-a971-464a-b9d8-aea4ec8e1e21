<?php

namespace App\Console\Commands;

use App\Models\Camera;
use App\Models\CoachComment;
use App\Models\Game;
use App\Models\Player;
use App\Models\PlayerComment;
use App\Models\PlayerStat;
use App\Models\StatsParameter;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ProcessFtpStats extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stats:process-ftp';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process stats and coach comments from FTP server';

    /**
     * Execute the console command.
     *
     * Client clarification: Process CSV files from /OUT/ directory
     * Multiple CSV files for same camera/day should be counted as one game's stats
     * Empty USER_ID = game-level stats (shown to all players)
     */
    public function handle()
    {
        $this->info('Starting FTP stats processing...');

        try {
            $ftpDisk = Storage::disk('ftp');

            $files = $ftpDisk->files();

            if (empty($files)) {
                $this->info('No files found.');
                return 0;
            }
            $this->info('Found ' . count($files) . ' files.');

            // Create backup directory if it doesn't exist
            $backupDir = 'backup';
            if (!$ftpDisk->exists($backupDir)) {
                $ftpDisk->makeDirectory($backupDir, 0755, true);
            }

            // Group files by date and camera for combined processing
            $fileGroups = $this->groupFilesByDateAndCamera($files, $ftpDisk);

            foreach ($fileGroups as $groupKey => $groupFiles) {
                $this->info("Processing file group: {$groupKey}");

                // Process each group as one game session
                $this->processFileGroup($groupFiles, $ftpDisk, $backupDir);
            }

            $this->info('FTP stats processing completed successfully.');
            return 0;
        } catch (\Exception $e) {
            $this->error('Error processing FTP stats: ' . $e->getMessage());
            Log::error('FTP stats processing error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
    }

    /**
     * Group files by date and camera ID for combined processing
     * Client clarification: Multiple CSV files for same camera and game session (same day) = one game
     */
    private function groupFilesByDateAndCamera(array $files, $ftpDisk): array
    {
        $groups = [];

        foreach ($files as $file) {
            // Skip if not a CSV file
            if (!str_ends_with(strtolower($file), '.csv')) {
                continue;
            }

            $fileName = basename($file);
            $gameDetails = $this->parseFileName($fileName);

            if (!$gameDetails) {
                $this->warn("Could not parse filename {$fileName}. Skipping.");
                continue;
            }

            // Group by date and camera_id (same day = same game)
            $groupKey = $gameDetails['date'] . '_' . $gameDetails['camera_id'];

            if (!isset($groups[$groupKey])) {
                $groups[$groupKey] = [
                    'game_details' => $gameDetails,
                    'stats_files' => [],
                    'coach_files' => []
                ];
            }

            $fileType = $this->determineFileType($fileName);
            $fileInfo = [
                'path' => $file,
                'name' => $fileName,
                'content' => $ftpDisk->get($file)
            ];

            if ($fileType === 'C') {
                $groups[$groupKey]['coach_files'][] = $fileInfo;
            } else {
                $groups[$groupKey]['stats_files'][] = $fileInfo;
            }
        }

        return $groups;
    }

    /**
     * Process a group of files (same date/camera) as one game session
     */
    private function processFileGroup(array $groupData, $ftpDisk, string $backupDir): void
    {
        $gameDetails = $groupData['game_details'];
        $statsFiles = $groupData['stats_files'];
        $coachFiles = $groupData['coach_files'];

        // Create a representative filename for the game
        $gameFileName = $gameDetails['date'] . '_' . $gameDetails['camera_id'] . '_' .
            str_replace(':', '', $gameDetails['start_time']) . '_' .
            str_replace(':', '', $gameDetails['end_time']) . '.csv';

        // Find or create game
        $game = Game::where('file_name', $gameFileName)->first();

        if (!$game) {
            $this->info("Creating new game for group: {$gameFileName}");

            // Check if camera exists
            if (!Camera::where('camera_id', $gameDetails['camera_id'])->exists()) {
                $this->warn("Camera with ID {$gameDetails['camera_id']} not found. Skipping group.");
                return;
            }

            $game = Game::create([
                'file_name' => $gameFileName,
                'date' => $gameDetails['date'],
                'start_time' => $gameDetails['start_time'],
                'end_time' => $gameDetails['end_time'],
                'camera_id' => $gameDetails['camera_id']
            ]);
        } else {
            $this->info("Found existing game for group: {$gameFileName}");
        }

        // Process all stats files for this game (combine them)
        foreach ($statsFiles as $fileInfo) {
            $data = array_map('str_getcsv', explode("\n", $fileInfo['content']));
            $this->processStatsFile($game, $data, $fileInfo['name']);
        }

        // Process coach files (link to date of processing as per client)
        foreach ($coachFiles as $fileInfo) {
            $data = array_map('str_getcsv', explode("\n", $fileInfo['content']));
            $this->processCoachFile($game, $data, $fileInfo['name']);
        }

        // Move all files to backup
        foreach (array_merge($statsFiles, $coachFiles) as $fileInfo) {
            $backupFilePath = $backupDir . '/' . $fileInfo['name'];
            $ftpDisk->put($backupFilePath, $fileInfo['content']);
            $ftpDisk->delete($fileInfo['path']);
            $this->info("File {$fileInfo['name']} moved to backup.");
        }
    }




    /**
     * Determine the type of file based on filename
     */
    private function determineFileType(string $filename): string
    {
        // Coach comment file ends with C.CSV
        if (str_ends_with(strtoupper($filename), 'C.CSV')) {
            return 'C';
        }

        // Default to Shot tracking file
        return 'S';
    }

    /**
     * Parse filename to extract game details
     * Format: DDMMYY+CAM_ID+start time+end time+(B/S/P/G/C)
     * Example: 100525GHY6FD11001130.csv
     */
    private function parseFileName(string $filename): ?array
    {
        // Remove .CSV extension and convert to uppercase
        $filename = strtoupper(str_replace(['.CSV', '.csv'], '', $filename));

        // Remove 'C' suffix if it's a coach file
        $filename = rtrim($filename, 'C');

        // Check if filename has minimum required length (6 for date + camera_id + times)
        if (strlen($filename) < 12) {
            return null;
        }

        // Extract date (first 6 characters: DDMMYY)
        $dateStr = substr($filename, 0, 6);
        $day = substr($dateStr, 0, 2);
        $month = substr($dateStr, 2, 2);
        $year = '20' . substr($dateStr, 4, 2);

        // Validate date
        if (!checkdate((int)$month, (int)$day, (int)$year)) {
            return null;
        }

        $date = "{$year}-{$month}-{$day}";

        // Extract remaining part (camera_id + times)
        $remaining = substr($filename, 6);

        // Find where the numeric time part starts (should be last 8 characters)
        if (strlen($remaining) < 8) {
            return null;
        }

        // Extract times (last 8 characters: 4 for start time + 4 for end time)
        $timesPart = substr($remaining, -8);
        $startTimeStr = substr($timesPart, 0, 4);
        $endTimeStr = substr($timesPart, 4, 4);

        // Extract camera_id (everything except the last 8 characters)
        $cameraId = substr($remaining, 0, -8);

        // Convert times to HH:MM format
        $startTime = substr($startTimeStr, 0, 2) . ':' . substr($startTimeStr, 2, 2) . ':00';
        $endTime = substr($endTimeStr, 0, 2) . ':' . substr($endTimeStr, 2, 2) . ':00';

        // Validate times
        if (
            !preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/', $startTime) ||
            !preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/', $endTime)
        ) {
            return null;
        }

        return [
            'date' => $date,
            'start_time' => $startTime,
            'end_time' => $endTime,
            'camera_id' => $cameraId
        ];
    }

    /**
     * Process a coach comment file
     * Client clarification: Link coach files to date of processing (not game session)
     */
    private function processCoachFile(Game $game, array $data, string $fileName = ''): void
    {
        $this->info("Processing coach comment file: {$fileName}");

        // Expected headers: date,hour,player,mes_id
        if (count($data) <= 1) {
            $this->warn('Coach file is empty or has no data rows.');
            return;
        }

        $headers = array_map('trim', $data[0]);
        $dateIndex = array_search('date', array_map('strtolower', $headers));
        $hourIndex = array_search('hour', array_map('strtolower', $headers));
        $playerIndex = array_search('player', array_map('strtolower', $headers));
        $messageIdIndex = array_search('mes_id', array_map('strtolower', $headers));

        if ($dateIndex === false || $hourIndex === false || $playerIndex === false || $messageIdIndex === false) {
            $this->warn('Invalid coach file format. Expected headers: date,hour,player,mes_id');
            return;
        }

        // Process each row
        foreach (array_slice($data, 1) as $row) {
            if (count($row) < 4) continue;

            $dateValue = trim($row[$dateIndex]);
            $playerId = trim($row[$playerIndex], '"');
            $messageId = (int) $row[$messageIdIndex];
            $hour = trim($row[$hourIndex]);

            // Set player ID to null if empty or invalid
            if (empty($playerId) || $playerId === '""') {
                $playerId = null;
            } else {
                // Find the player
                $player = Player::where('player_id', $playerId)->first();
                if (!$player) {
                    $this->warn("Player with ID {$playerId} not found. Skipping.");
                    continue;
                }
            }

            // Find the coach comment
            $coachComment = CoachComment::where('id', $messageId)->first();
            if (!$coachComment) {
                $this->warn("Coach comment with message ID {$messageId} not found. Skipping.");
                continue;
            }

            // Parse date and time from CSV data
            try {
                // Parse date from dd/mm/yyyy format
                $formattedDate = null;
                if (preg_match('/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/', $dateValue, $matches)) {
                    $day = str_pad($matches[1], 2, '0', STR_PAD_LEFT);
                    $month = str_pad($matches[2], 2, '0', STR_PAD_LEFT);
                    $year = $matches[3];
                    $formattedDate = "{$year}-{$month}-{$day}";
                } else {
                    $this->warn("Invalid date format: {$dateValue}. Expected dd/mm/yyyy format.");
                    continue;
                }

                // Parse hour as decimal (e.g., 22.504 = 22 hours and 30.24 minutes)
                $formattedTime = null;
                if (is_numeric($hour)) {
                    $decimalHour = (float) $hour;
                    $hours = floor($decimalHour);
                    $minutes = ($decimalHour - $hours) * 60;
                    $seconds = ($minutes - floor($minutes)) * 60;

                    $formattedTime = sprintf('%02d:%02d:%02d',
                        $hours,
                        floor($minutes),
                        floor($seconds)
                    );
                } else {
                    $this->warn("Invalid hour format: {$hour}. Expected decimal number.");
                    continue;
                }

                $this->line("Parsed date: {$formattedDate}, time: {$formattedTime} from {$dateValue}, {$hour}");

            } catch (\Exception $e) {
                $this->warn("Error parsing date/time: {$e->getMessage()}. Skipping row.");
                continue;
            }

            // Create player comment with parsed date and time
            PlayerComment::create([
                'game_id' => $game->id,
                'player_id' => $playerId,
                'coach_comment_id' => $coachComment->id,
                'date' => $formattedDate,
                'time' => $formattedTime
            ]);

            $this->line("Added coach comment {$messageId} for player " . ($playerId ?: 'ALL') . " on {$formattedDate} at {$formattedTime}");
        }
    }

    /**
     * Process a stats file - Combine multiple CSV files for same game
     * Client clarification: Multiple CSV files for same camera/day = one game's combined stats
     * Empty USER_ID = game-level stats (shown to all players)
     */
    private function processStatsFile(Game $game, array $data, string $fileName = ''): void
    {
        $this->info("Processing stats file: {$fileName}");

        if (count($data) <= 1) {
            $this->warn('Stats file is empty or has no data rows.');
            return;
        }

        $headers = array_map('trim', $data[0]);
        $userIdIndex = array_search('user_id', array_map('strtolower', $headers));
        $typeIndex = array_search('type', array_map('strtolower', $headers));
        $nbrIndex = array_search('nbr', array_map('strtolower', $headers));
        $successIndex = array_search('success', array_map('strtolower', $headers));
        $winnerIndex = array_search('winner', array_map('strtolower', $headers));
        $lossIndex = array_search('loss', array_map('strtolower', $headers));

        if (
            $userIdIndex === false || $typeIndex === false || $nbrIndex === false ||
            $successIndex === false || $winnerIndex === false || $lossIndex === false
        ) {
            $this->warn('Invalid stats file format. Expected headers: user_id,type,nbr,success,winner,loss');
            return;
        }

        // Process each row - combine with existing stats if they exist
        foreach (array_slice($data, 1) as $row) {
            if (count($row) < 6) continue;

            $userId = trim($row[$userIdIndex], '"');
            if ($userId === '""' || $userId === '') {
                $userId = null; // Empty user_id = game stats for all players (as per client)
            } else {
                // Find the player
                $player = Player::where('player_id', $userId)->first();
                if (!$player) {
                    $this->warn("Player with ID {$userId} not found. Skipping.");
                    continue;
                }
            }

            $typeId = (int) $row[$typeIndex];
            $nbr = (float) $row[$nbrIndex];
            $success = (float) $row[$successIndex];
            $winner = (float) $row[$winnerIndex];
            $loss = (float) $row[$lossIndex];

            // Find the stats parameter
            $statsParam = StatsParameter::find($typeId);
            if (!$statsParam) {
                $this->warn("Stats parameter with ID {$typeId} not found. Skipping.");
                continue;
            }

            // Check if stat already exists for this game/player/type combination
            $existingStat = PlayerStat::where('game_id', $game->id)
                ->where('player_id', $userId)
                ->where('stat_param_id', $statsParam->id)
                ->first();

            if ($existingStat) {
                // Combine stats: sum values for type IDs 1-18, average for type IDs 19-23
                if ($statsParam->sum_avg == 1) {
                    // Sum the values
                    $existingStat->update([
                        'total' => $existingStat->total + $nbr,
                        'success' => $existingStat->success + $success,
                        'winner' => $existingStat->winner + $winner,
                        'loss' => $existingStat->loss + $loss
                    ]);
                    $this->line("Updated (summed) stat {$statsParam->label} for player " . ($userId ?: 'ALL'));
                } else {
                    // Calculate average (for rating and percentage stats)
                    $existingStat->update([
                        'total' => ($existingStat->total + $nbr) / 2,
                        'success' => ($existingStat->success + $success) / 2,
                        'winner' => ($existingStat->winner + $winner) / 2,
                        'loss' => ($existingStat->loss + $loss) / 2
                    ]);
                    $this->line("Updated (averaged) stat {$statsParam->label} for player " . ($userId ?: 'ALL'));
                }
            } else {
                // Create new player stat
                PlayerStat::create([
                    'game_id' => $game->id,
                    'player_id' => $userId,
                    'stat_param_id' => $statsParam->id,
                    'total' => $nbr,
                    'success' => $success,
                    'winner' => $winner,
                    'loss' => $loss
                ]);
                $this->line("Added new stat {$statsParam->label} for player " . ($userId ?: 'ALL') . " in game {$game->file_name}");
            }
        }
    }
}
