<?php

use Intervention\Image\Drivers\Gd\Driver;
use Intervention\Image\Drivers\Gd\Encoders\JpegEncoder;
use Intervention\Image\Drivers\Gd\Encoders\PngEncoder;
use Intervention\Image\Drivers\Gd\Encoders\WebpEncoder;
use Intervention\Image\ImageManager;
use Illuminate\Support\Facades\Storage;

// json response helper
function jsonResponse($status, $data = [], $statusCode = 200)
{
    return response()->json(array_merge([
        'status' => $status,
    ], $data), $statusCode);
}

// validation error response helper
function validationError($errors = [], $message = 'Validation Error', $statusCode = 200)
{
    // If errors are provided, get the first error message.
    if (!empty($errors) && is_object($errors) && $errors->first()) {
        $message = $errors->first(); // Set the message to the first error
    }

    return response()->json([
        'status' => false,
        'message' => $message,
        'errors' => $errors
    ], $statusCode);
}




function uploadFile($file, $path = 'uploads', $quality = 60, $oldFile = null)
{
    try {
        if (!$file) {
            return null;
        }

        // Create storage path if it doesn't exist
        $storagePath = storage_path("app/public/{$path}");
        if (!file_exists($storagePath)) {
            mkdir($storagePath, 0755, true);
        }

        // Get file info
        $originalExtension = strtolower($file->getClientOriginalExtension());
        $filename = uniqid() . '_' . time();

        // List of image extensions we'll process
        $imageExtensions = ['jpg', 'jpeg', 'png', 'webp'];

        // If it's an image (except GIF), process it
        if (in_array($originalExtension, $imageExtensions)) {
            $manager = new ImageManager(
                Driver::class,
                autoOrientation: true,
            );
            $image = $manager->read($file->getRealPath());

            // Default to WebP for better compression
            $extension = 'webp';
            $encoder = new WebpEncoder(quality: $quality);

            // Use original format if specifically needed
            if ($originalExtension === 'png' && strpos($file->getMimeType(), 'png') !== false) {
                $extension = 'png';
                $encoder = new PngEncoder();
            } elseif (in_array($originalExtension, ['jpg', 'jpeg'])) {
                $extension = 'jpg';
                $encoder = new JpegEncoder(quality: $quality);
            }

            $filename = "{$filename}.{$extension}";
            $fullPath = "{$path}/{$filename}";

            // Encode and save the image
            $processedImage = $image->encode($encoder);
            Storage::disk('public')->put($fullPath, $processedImage);
        } else {
            // For GIFs and non-image files, store directly
            $extension = $originalExtension;
            $filename = "{$filename}.{$extension}";
            $fullPath = "{$path}/{$filename}";

            Storage::disk('public')->putFileAs(
                $path,
                $file,
                $filename
            );
        }

        // Delete old file if exists
        if ($oldFile) {
            Storage::disk('public')->delete($oldFile);
        }

        return $fullPath;
    } catch (\Exception $e) {
        \Log::error('File upload failed: ' . $e->getMessage());
        return null;
    }
}
