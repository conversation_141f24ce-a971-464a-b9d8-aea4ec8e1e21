<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SubscriptionPlan;
use App\Services\StripeSubscriptionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class SubscriptionPlanController extends Controller
{
    protected $stripeService;

    public function __construct(StripeSubscriptionService $stripeService)
    {
        $this->stripeService = $stripeService;
    }

    /**
     * Display a listing of subscription plans
     */
    public function index()
    {
        $plans = SubscriptionPlan::orderBy('country')->orderBy('price')->get();
        return view('admin.subscription-plans.index', compact('plans'));
    }

    /**
     * Show the form for creating a new subscription plan
     */
    public function create()
    {
        return view('admin.subscription-plans.create');
    }

    /**
     * Store a newly created subscription plan
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'country' => 'required|string|max:2',
                'price' => 'required|numeric|min:0',
                'currency' => 'required|string|size:3',
                'description' => 'nullable|string',
                'is_active' => 'boolean',
            ]);

            if ($validator->fails()) {
                return back()->withErrors($validator)->withInput();
            }

            $plan = SubscriptionPlan::create([
                'name' => $request->name,
                'country' => strtoupper($request->country),
                'price' => $request->price,
                'currency' => strtoupper($request->currency),
                'description' => $request->description,
                'is_active' => $request->has('is_active'),
            ]);

            // Create Stripe price if plan is active
            if ($plan->is_active) {
                $this->stripeService->getOrCreatePrice($plan);
            }

            return redirect()->route('admin.subscription-plans.index')
                ->with('success', 'Subscription plan created successfully');
        } catch (\Exception $e) {
            Log::error('Failed to create subscription plan: ' . $e->getMessage());
            return back()->with('error', 'Failed to create subscription plan: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Show the form for editing a subscription plan
     */
    public function edit(SubscriptionPlan $subscriptionPlan)
    {
        return view('admin.subscription-plans.edit', compact('subscriptionPlan'));
    }

    /**
     * Update the specified subscription plan
     */
    public function update(Request $request, SubscriptionPlan $subscriptionPlan)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'country' => 'required|string|max:2',
                'price' => 'required|numeric|min:0',
                'currency' => 'required|string|size:3',
                'description' => 'nullable|string',
                'is_active' => 'boolean',
            ]);

            if ($validator->fails()) {
                return back()->withErrors($validator)->withInput();
            }

            $wasActive = $subscriptionPlan->is_active;
            $priceChanged = $subscriptionPlan->price != $request->price || 
                            $subscriptionPlan->currency != strtoupper($request->currency);

            $subscriptionPlan->update([
                'name' => $request->name,
                'country' => strtoupper($request->country),
                'price' => $request->price,
                'currency' => strtoupper($request->currency),
                'description' => $request->description,
                'is_active' => $request->has('is_active'),
            ]);

            // If price changed or plan became active, create new Stripe price
            if (($priceChanged || (!$wasActive && $subscriptionPlan->is_active)) && $subscriptionPlan->is_active) {
                // Clear existing price ID to force creation of a new one
                $subscriptionPlan->update(['stripe_price_id' => null]);
                $this->stripeService->getOrCreatePrice($subscriptionPlan);
            }

            return redirect()->route('admin.subscription-plans.index')
                ->with('success', 'Subscription plan updated successfully');
        } catch (\Exception $e) {
            Log::error('Failed to update subscription plan: ' . $e->getMessage());
            return back()->with('error', 'Failed to update subscription plan: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified subscription plan
     */
    public function destroy(SubscriptionPlan $subscriptionPlan)
    {
        try {
            // Check if plan has any subscriptions
            if ($subscriptionPlan->subscriptions()->exists()) {
                return back()->with('error', 'Cannot delete plan with active subscriptions');
            }

            $subscriptionPlan->delete();

            return redirect()->route('admin.subscription-plans.index')
                ->with('success', 'Subscription plan deleted successfully');
        } catch (\Exception $e) {
            Log::error('Failed to delete subscription plan: ' . $e->getMessage());
            return back()->with('error', 'Failed to delete subscription plan: ' . $e->getMessage());
        }
    }
}
