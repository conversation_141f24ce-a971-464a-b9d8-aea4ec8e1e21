<?php

namespace App\Http\Controllers\Api\Club;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Tymon\JWTAuth\Exceptions\TokenInvalidException;
use Tymon\JWTAuth\Exceptions\TokenExpiredException;
use Illuminate\Support\Facades\Log;

class AuthController extends Controller
{
    public function login(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'email' => 'required|email',
                'password' => 'required|min:6'
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            $credentials = $request->only('email', 'password');

            if (!$token = Auth::guard('club')->attempt($credentials)) {
                return jsonResponse(false, ['message' => __('Incorrect email or password')]);
            }

            if (auth('club')->user()->status !== 'Active') {
                Auth::guard('club')->logout();
                return jsonResponse(false, ['message' => __('Account is not active')]);
            }

            return jsonResponse(true, [
                'message' => __('Login successful'),
                'token' => $token,
                'club' => auth('club')->user()
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to login: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to login due to ') . $e->getMessage()]);
        }
    }

    public function changePassword(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'current_password' => 'required|string|min:6',
                'new_password' => 'required|string|min:6',
                'new_password_confirmation' => 'required|same:new_password'
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            $user = auth('club')->user();

            // Verify current password
            if (!Hash::check($request->current_password, $user->password)) {
                return jsonResponse(false, ['message' => __('Current password is incorrect')]);
            }

            // Update password
            $user->update([
                'password' => Hash::make($request->new_password)
            ]);

            return jsonResponse(true, [
                'message' => __('Password changed successfully')
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to change password: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to change password due to ') . $e->getMessage()]);
        }
    }

    public function logout()
    {
        try {
            auth('club')->logout();

            return jsonResponse(true, [
                'message' => __('Successfully logged out')
            ]);
        } catch (TokenExpiredException $e) {
            return jsonResponse(false, [
                'message' => __('Login session has expired')
            ]);
        } catch (TokenInvalidException $e) {
            return jsonResponse(false, [
                'message' => __('Login session is invalid')
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to logout: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => __('Failed to logout due to ') . $e->getMessage()
            ]);
        }
    }

    public function profile()
    {
        try {
            $club = Auth::guard('club')->user();
            return jsonResponse(true, ['club' => $club]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch profile: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to fetch profile ') . $e->getMessage()]);
        }
    }

    public function updateFcmToken(Request $request)
    {
        try {
            $club = Auth::guard('club')->user();
            $club->update(['fcm_token' => $request->fcm_token]);
            return jsonResponse(true, ['message' => __('FCM token updated successfully')]);
        } catch (\Exception $e) {
            Log::error('Failed to update FCM token: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to update FCM token ') . $e->getMessage()]);
        }
    }

    public function unreadNotificationsCount()
    {
        try {
            $club = Auth::guard('club')->user()->club_id;
            $count = Notification::where('club_id', $club)->where('is_read', false)->count();
            return jsonResponse(true, ['count' => $count]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch notifications: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to fetch notifications ') . $e->getMessage()]);
        }
    }

    public function notifications()
    {
        try {
            $clubId = Auth::guard('club')->user()->club_id;

            // Get unread notifications
            $unreadNotifications = Notification::where('club_id', $clubId)
                ->where('is_read', false)
                ->get();

            // Mark each notification as read individually to trigger model events
            foreach ($unreadNotifications as $notification) {
                $notification->is_read = true;
                $notification->save(); // This will trigger the updated event
            }

            // If there are unread notifications, manually update Firebase counts
            if ($unreadNotifications->count() > 0) {
                // Get the first notification and call updateFirebaseCounts
                // This will update the count to 0 since we've marked all as read
                if ($unreadNotifications->first()) {
                    $unreadNotifications->first()->updateFirebaseCounts();
                }
            }

            // Get all notifications for pagination
            $notifications = Notification::where('club_id', $clubId)
                ->latest()
                ->paginate(10);

            return jsonResponse(true, [
                'notifications' => $notifications->items(),
                'pagination' => [
                    'total' => $notifications->total(),
                    'per_page' => $notifications->perPage(),
                    'current_page' => $notifications->currentPage(),
                    'last_page' => $notifications->lastPage(),
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch notifications: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to fetch notifications ') . $e->getMessage()]);
        }
    }

    public function updateLanguage(Request $request)
    {
        try {
            $club = Auth::guard('club')->user();
            $club->update(['language' => $request->language]);
            return jsonResponse(true, ['message' => __('Language updated successfully', [], $request->language ?? 'en')]);
        } catch (\Exception $e) {
            Log::error('Failed to update language: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to update language ') . $e->getMessage()]);
        }
    }
}
