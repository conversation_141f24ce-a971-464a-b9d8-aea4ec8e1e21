<?php

namespace App\Http\Controllers\Api\Club;

use App\Http\Controllers\Controller;
use App\Models\Camera;
use Illuminate\Http\Request;

class CameraController extends Controller
{
    public function index()
    {
        $cameras = auth('club')->user()->cameras()->latest()->get();

        return jsonResponse(true, [
            'cameras' => $cameras
        ]);
    }

    public function storeOrUpdate(Request $request)
    {
        try {
            $validator = validator($request->all(), [
                'camera_id' => 'nullable|string|max:100',
                'court_number' => 'nullable|numeric|max:100',
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            $club = auth('club')->user();

            if ($request->camera_id) {
                $camera = Camera::where('camera_id', $request->camera_id)
                    ->where('club_id', $club->club_id)
                    ->first();

                if (!$camera) {
                    return jsonResponse(false, ['message' => __('Camera not found')]);
                }

                $camera->update([
                    'court_number' => $request->court_number,
                ]);

                return jsonResponse(true, [
                    'message' => __('Camera updated successfully'),
                    'camera' => $camera
                ]);
            }

            if ($club->cameras()->count() >= 4) {
                return jsonResponse(false, ['message' => __('You can only add 4 cameras')]);
            }

            $camera = Camera::create([
                'camera_id' => Camera::generateCameraId(),
                'club_id' => $club->club_id,
                'court_number' => $request->court_number,
            ]);

            return jsonResponse(true, [
                'message' => __('Camera added successfully'),
                'camera' => $camera
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => __('Failed to add camera ') . $e->getMessage()
            ]);
        }
    }
}
