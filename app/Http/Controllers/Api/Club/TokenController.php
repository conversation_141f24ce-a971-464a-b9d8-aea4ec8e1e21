<?php

namespace App\Http\Controllers\Api\Club;

use App\Http\Controllers\Controller;
use App\Mail\AccountSuspendedNotification;
use App\Models\Notification;
use App\Models\Player;
use App\Models\RejectedCounter;
use App\Models\Token;
use App\Services\FCMService;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class TokenController extends Controller
{
    public function index(Request $request)
    {
        try {
            $query = Token::query()
                ->with('tokenType:id,value,minutes', 'player:player_id,first_name,last_name')->where('club_id', auth('club')->user()->club_id)
                ->where('status', '!=', 'Requested');

            // Date range filter

            if ($request->from_date || $request->to_date) {
                $fromDate = $request->from_date ? Carbon::parse($request->from_date)->startOfDay() : null;
                $toDate = $request->to_date ? Carbon::parse($request->to_date)->endOfDay() : null;

                $query->where(function ($q) use ($fromDate, $toDate) {
                    if ($fromDate && $toDate) {
                        $q->whereBetween('created_at', [
                            $fromDate,
                            $toDate
                        ]);
                    } elseif ($fromDate) {
                        $q->where('created_at', '>=', $fromDate);
                    } elseif ($toDate) {
                        $q->where('created_at', '<=', $toDate);
                    }
                });
            }
            // Search by token_id
            if ($request->search) {
                $query->where('token_id', 'like', '%' . $request->search . '%');
            }

            if ($request->status) {
                $query->where('status', $request->status);
            }

            $tokens = $query->latest()->paginate(10);

            return jsonResponse(true, [
                'data' => $tokens->items(),
                'pagination' => [
                    'total' => $tokens->total(),
                    'per_page' => $tokens->perPage(),
                    'current_page' => $tokens->currentPage(),
                    'last_page' => $tokens->lastPage(),
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch tokens: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => __('Failed to fetch tokens ') . $e->getMessage()
            ]);
        }
    }

    public function requested(Request $request)
    {
        Token::where('status', 'Requested')
            ->where('requested_at', '<', Carbon::now()->subMinutes(30)->format('d-m-Y H:i:s'))
            ->delete();

        try {
            $query = Token::query()
                ->with('player:player_id,first_name,last_name', 'tokenType:id,value,minutes')->where('club_id', auth('club')->user()->club_id)
                ->where('status', 'Requested');

            // Date range filter
            if ($request->from_date && $request->to_date) {
                $fromDate = $request->from_date;
                $toDate = $request->to_date;
                $query->whereBetween('created_at', [$fromDate, $toDate])
                    ->orWhereDate('created_at', [$fromDate, $toDate]);
            }

            // Search by token_id
            if ($request->search) {
                $query->where('token_id', 'like', '%' . $request->search . '%');
            }

            $tokens = $query->latest()->paginate(10);

            return jsonResponse(true, [
                'data' => $tokens->items(),
                'pagination' => [
                    'total' => $tokens->total(),
                    'per_page' => $tokens->perPage(),
                    'current_page' => $tokens->currentPage(),
                    'last_page' => $tokens->lastPage(),
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch tokens: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => __('Failed to fetch tokens ') . $e->getMessage()
            ]);
        }
    }

    public function redeemTokensRequests(Request $request)
    {
        Token::where('status', 'Redeem-Requested')
            ->where('is_requested_for_redeem', true)
            ->where('redeemed_requested_at', '<', Carbon::now()->subMinutes(30)->format('d-m-Y H:i:s'))
            ->update(['is_requested_for_redeem' => false, 'status' => 'Generated']);

        try {
            $query = Token::query()
                ->with('player:player_id,first_name,last_name', 'tokenType:id,value,minutes')->where('club_id', auth('club')->user()->club_id)
                ->where('status', 'Redeem-Requested')
                ->where('is_requested_for_redeem', true);

            // Date range filter
            if ($request->from_date && $request->to_date) {
                $fromDate = $request->from_date;
                $toDate = $request->to_date;
                $query->whereBetween('created_at', [$fromDate, $toDate])
                    ->orWhereDate('created_at', [$fromDate, $toDate]);
            }

            // Search by token_id
            if ($request->search) {
                $query->where('token_id', 'like', '%' . $request->search . '%');
            }

            $tokens = $query->latest()->paginate(10);

            return jsonResponse(true, [
                'data' => $tokens->items(),
                'pagination' => [
                    'total' => $tokens->total(),
                    'per_page' => $tokens->perPage(),
                    'current_page' => $tokens->currentPage(),
                    'last_page' => $tokens->lastPage(),
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch tokens: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => __('Failed to fetch tokens ') . $e->getMessage()
            ]);
        }
    }

    public function store(Request $request)
    {
        try {
            // Validate that payload is an array
            if (!is_array($request->all())) {
                return jsonResponse(false, [
                    'message' => __('Payload must be an array')
                ]);
            }

            $tokens = [];
            $errors = [];

            $club = auth('club')->user();

            foreach ($request->all() as $index => $tokenData) {
                $validator = validator($tokenData, [
                    'value' => 'required|numeric',
                    'minutes' => 'required|numeric',
                    'token_type_id' => 'required'
                ]);

                if ($validator->fails()) {
                    $errors[] = [
                        'index' => $index,
                        'errors' => $validator->errors()
                    ];
                    continue;
                }

                // Generate unique token_id
                $tokenId = $this->generateUniqueToken();

                try {
                    $token = Token::create([
                        'token_id' => $tokenId,
                        'token_type_id' => $tokenData['token_type_id'],
                        'club_id' => $club->club_id,
                        'status' => "Generated",
                        // Optional fields left empty as requested
                        'commercial_id' => null,
                        'player_id' => null,
                        'purchased_at' => null,
                        'requested_at' => null,
                        'generated_at' => null,
                        'used_at' => null,
                        'redeemed_at' => null,
                        'invoiced_at' => null,
                    ]);
                    $tokens[] = $token;
                } catch (\Exception $e) {
                    $errors[] = [
                        'index' => $index,
                        'message' => __('Failed to create token'),
                        'error' => $e->getMessage()
                    ];
                }
            }

            return jsonResponse(true, [
                'message' => empty($errors) ? __('All tokens created successfully') : __('Some tokens failed to create'),
                'data' => [
                    'tokens' => $tokens,
                    'errors' => $errors
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to process tokens: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => __('Failed to process tokens ') . $e->getMessage()
            ]);
        }
    }

    private function generateUniqueToken()
    {
        do {
            $letters = Str::random(3);
            $numbers = str_pad(mt_rand(0, 99999), 5, '0', STR_PAD_LEFT);
            $tokenId = strtoupper($letters . $numbers);
        } while (Token::where('token_id', $tokenId)->exists());

        return $tokenId;
    }

    public function redeem(Request $request)
    {
        try {
            $validator = validator($request->all(), [
                'token_id' => 'required|string|size:8',
                'type' => 'required|in:accept,reject',
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            $token = Token::with('player:player_id,fcm_token')->where('token_id', $request->token_id)
                ->where('club_id', auth('club')->user()->club_id)
                ->first();

            if (!$token) {
                return jsonResponse(false, ['message' => __('Token not found or does not belong to your club')]);
            }

            // Check if token is already redeemed
            if ($token->status === 'Redeemed') {
                return jsonResponse(false, ['message' => __('Token already redeemed')]);
            }

            // Check if token is in valid status for redemption
            if (!in_array($token->status, ['Redeem-Requested', 'Generated', 'Used'])) {
                return jsonResponse(false, ['message' => __('Token is not in valid status')]);
            }

            if ($request->type === 'accept') {
                // Update token status and redeemed_at timestamp
                $token->update([
                    'status' => 'Redeemed',
                    'redeemed_at' => Carbon::now()->format('d-m-Y H:i:s'),
                    'is_requested_for_redeem' => false
                ]);
                if ($token->player) {
                    if ($token->player->fcm_token) {
                        FCMService::sendNotification($token->player->fcm_token, __('Token Redeemed'), __('Your token ') . $token->token_id . __(' has been redeemed by ') . $token->club->club_name);
                    }

                    Notification::create([
                        'title' => 'Token Redeemed',
                        'body' => 'Your token ' . $token->token_id . ' has been redeemed by ' . $token->club->club_name,
                        'player_id' => $token->player->player_id,
                        'is_read' => false,
                    ]);
                }
            } else {
                // Update token status and redeemed_at timestamp
                $token->update([
                    'redeemed_at' => null,
                    'is_requested_for_redeem' => false,
                    'status' => 'Generated'
                ]);

                if ($token->player) {
                    if ($token->player->fcm_token) {
                        FCMService::sendNotification($token->player->fcm_token, __('Token Redeemed Rejected'), __('Your token ') . $token->token_id . __(' redeem request has been rejected by ') . $token->club->club_name);
                    }

                    Notification::create([
                        'title' => 'Token Redeem Rejected',
                        'body' => 'Your token ' . $token->token_id . ' redeem request has been rejected by ' . $token->club->club_name,
                        'player_id' => $token->player->player_id,
                        'is_read' => false,
                    ]);
                }
            }


            return jsonResponse(true, [
                'message' => __('Token redeemed successfully'),
                'token' => $token
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to redeem token: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to redeem token ') . $e->getMessage()]);
        }
    }

    public function acceptToken(Request $request)
    {
        try {
            $validator = validator($request->all(), [
                'token_id' => 'required|string|max:20',
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }
            $club = auth('club')->user();

            $token = Token::with('player:player_id,fcm_token')
                ->where('token_id', $request->token_id)
                ->where('club_id', $club->club_id)
                ->first();

            if (!$token) {
                return jsonResponse(false, ['message' => __('Token not found or does not belong to your club')]);
            }

            // Update token status
            $token->update([
                'status' => 'Generated',
                'generated_at' => Carbon::now()->format('d-m-Y H:i:s'),
            ]);

            if ($token->player && $token->player->fcm_token) {
                FCMService::sendNotification($token->player->fcm_token, __('Token Accepted'), __('Your token ') . $token->token_id . __(' has been accepted by ') . $club->club_name);
            }

            Notification::create([
                'title' => 'Token Accepted',
                'body' => 'Your token ' . $token->token_id . ' has been accepted by ' . $club->club_name,
                'player_id' => $token->player->player_id,
                'is_read' => false,
            ]);

            return jsonResponse(true, [
                'message' => __('Token accepted successfully'),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update token status: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to update token status ') . $e->getMessage()]);
        }
    }

    public function rejectToken(Request $request)
    {
        try {
            $validator = validator($request->all(), [
                'token_id' => 'required|string|max:20',
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            $club = auth('club')->user();

            $token = Token::with('player:player_id,fcm_token,status')->where('token_id', $request->token_id)
                ->where('club_id', $club->club_id)
                ->first();

            if (!$token) {
                return jsonResponse(false, ['message' => __('Token not found or does not belong to your club')]);
            }


            // Delete token
            $token->delete();

            // Track rejection in RejectedCounter table if player exists
            if ($token->player) {
                // Create a new rejection record
                RejectedCounter::create([
                    'player_id' => $token->player->player_id,
                    'club_id' => $club->club_id,
                    'token_id' => $token->token_id,
                    'rejected_at' => now()
                ]);

                // Check if player has reached 5 rejections in the last 30 days
                $rejectionsCount = RejectedCounter::getRejectionsCountInLast30Days($token->player->player_id);

                // If player has 5 or more rejections, suspend their account
                if ($rejectionsCount >= 5 && $token->player->status === 'Active') {
                    // Update player status to Suspended
                    Player::where('player_id', $token->player->player_id)
                        ->update(['status' => 'Suspended']);

                    // Send email notification about account suspension
                    try {
                        // Get the full player object with all details
                        $playerFull = Player::find($token->player->player_id);
                        if ($playerFull && $playerFull->email) {
                            Mail::to($playerFull->email)->send(new AccountSuspendedNotification($playerFull));
                        }
                    } catch (\Exception $e) {
                        Log::error('Failed to send account suspension email: ' . $e->getMessage());
                    }

                    // Send push notification about account suspension
                    if ($token->player->fcm_token) {
                        FCMService::sendNotification(
                            $token->player->fcm_token,
                            __('Account Suspended'),
                            __('Your account has been suspended due to multiple token rejections in the last 30 days.')
                        );
                    }

                    Notification::create([
                        'title' => 'Account Suspended',
                        'body' => 'Your account has been suspended due to multiple token rejections in the last 30 days.',
                        'player_id' => $token->player->player_id,
                        'is_read' => false,
                    ]);
                }

                // Send token rejection notification
                if ($token->player->fcm_token) {
                    FCMService::sendNotification(
                        $token->player->fcm_token,
                        __('Token Rejected'),
                        __('Your token ') . $token->token_id . __(' has been rejected by ') . $club->club_name
                    );
                }

                Notification::create([
                    'title' => 'Token Rejected',
                    'body' => 'Your token ' . $token->token_id . ' has been rejected by ' . $club->club_name,
                    'player_id' => $token->player->player_id,
                    'is_read' => false,
                ]);
            }





            return jsonResponse(true, [
                'message' => __('Token rejected successfully'),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to delete token: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to delete token ') . $e->getMessage()]);
        }
    }

    public function tokenRequestsCount()
    {
        try {
            $club = auth('club')->user()->club_id;
            $requestedTokens = Token::where('club_id', $club)->where('status', 'Requested')->count();

            $redeemRequests = Token::where('club_id', $club)->where('status', 'Redeem-Requested')->where('is_requested_for_redeem', true)->count();

            return jsonResponse(true, [
                'requested_tokens' => $requestedTokens,
                'redeem_requests' => $redeemRequests
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch notifications: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to fetch notifications ') . $e->getMessage()]);
        }
    }
}
