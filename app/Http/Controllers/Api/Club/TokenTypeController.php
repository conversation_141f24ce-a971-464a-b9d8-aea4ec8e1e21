<?php

namespace App\Http\Controllers\Api\Club;

use App\Http\Controllers\Controller;
use App\Models\TokenType;
use App\Services\CurrencyService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TokenTypeController extends Controller
{
    public function index($clubId = null)
    {
        try {
            $tokenTypes = TokenType::when($clubId, function ($query) use ($clubId) {
                $query->where('club_id', $clubId);
            })->latest()->get();

            return jsonResponse(true, [
                'token_types' => $tokenTypes
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch token types: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => __('Failed to fetch token types ') .  $e->getMessage()
            ]);
        }
    }

    public function clubTokenTypes()
    {
        try {
            $club = auth('club')->user();
            $tokenTypes = TokenType::where('club_id', $club->club_id)->latest()->get();

            return jsonResponse(true, [
                'token_types' => $tokenTypes
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch token types: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => __('Failed to fetch token types ') . $e->getMessage()
            ]);
        }
    }

    public function store(Request $request)
    {
        try {
            $validator = validator($request->all(), [
                'id' => 'nullable',
                'value' => 'required|numeric|max:99999.99',
                'minutes' => 'required|in:30,60',
                'currency' => 'required'
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            $club = auth('club')->user();

            // Create currency service
            $currencyService = new CurrencyService();

            // Convert value to EUR
            $valueInEur = $currencyService->convertToEur($request->value, $request->currency) ?? $request->value;

            if ($request->id) {
                // Update existing token type
                $tokenType = TokenType::where('_id', $request->id)
                    ->where('club_id', $club->club_id)
                    ->first();

                if (!$tokenType) {
                    return jsonResponse(false, ['message' => __('Token type not found')]);
                }

                $tokenType->update([
                    'value' => $request->value,
                    'value_in_eur' => $valueInEur,
                    'minutes' => $request->minutes,
                ]);

                return jsonResponse(true, [
                    'message' => __('Token type updated successfully'),
                    'token_type' => $tokenType
                ]);
            } else {
                // Create new token type
                $tokenType = TokenType::create([
                    'club_id' => $club->club_id,
                    'value' => $request->value,
                    'value_in_eur' => $valueInEur,
                    'minutes' => $request->minutes,
                ]);

                return jsonResponse(true, [
                    'message' => __('Token type created successfully'),
                    'token_type' => $tokenType
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to process token type: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => __('Failed to process token type ') . $e->getMessage()
            ]);
        }
    }
}
