<?php

namespace App\Http\Controllers\Api\Player;

use App\Http\Controllers\Controller;
use App\Mail\VerificationEmail;
use App\Models\Notification;
use App\Models\Player;
use App\Models\PlayerCredit;
use App\Models\PlayerOtpVerification;
use App\Models\Stat;
use App\Services\TwilioService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Tymon\JWTAuth\Exceptions\TokenInvalidException;
use Tymon\JWTAuth\Exceptions\TokenExpiredException;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class AuthController extends Controller
{
    public function register(Request $request)
    {
        try {
            $validator = Validator::make(
                $request->all(),
                [
                    'first_name' => 'required|string|max:30',
                    'last_name' => 'required|string|max:30',
                    'email' => 'required|email|max:30|unique:players,email',
                    'password' => 'required|min:6|confirmed',
                    'phone' => 'required|string|max:15',
                    'sex' => 'required|in:Male,Female,Other',
                    'postal_code' => 'nullable|string|max:10',
                    'country' => 'required|string|max:255',
                    'country_code' => 'required|string|max:255',
                    'photo' => 'nullable|image|mimes:jpg,jpeg,png,webp|max:10240',
                ],
                [
                    'email.unique' => __('Email already exists')
                ]
            );

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            if (Player::where('country_code', $request->country_code)->where('phone', $request->phone)->where('is_phone_verified', 1)->exists()) {
                return jsonResponse(false, ['message' => __('The phone number is already registered')]);
            }

            $filePath = null;

            if ($request->hasFile('photo')) {
                $filePath = uploadFile($request->file('photo'), 'players');
            }


            $player = Player::create([
                'player_id' => Player::generatePlayerId(),
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'phone' => $request->phone,
                'country_code' => $request->country_code,
                'sex' => $request->sex,
                'postal_code' => $request->postal_code,
                'country' => $request->country,
                'subscribed' => false,
                'started' => null,
                'status' => 'Active',
                'credits' => 0,
                'photo' => $filePath,
                'is_verified' => false,
                'is_email_verified' => false,
                'is_phone_verified' => false,
                'fcm_token' => null,
            ]);

            $this->sendVerificationOtp($player);

            Stat::incrementUsers();

            return jsonResponse(true, [
                'message' => __('Account created successfully. Please verify your account.'),
                'player' => $player
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to register: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to register due to ') . $e->getMessage()]);
        }
    }

    public function login(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'email' => 'required|email|exists:players,email',
                'password' => 'required|min:6'
            ],[
                'email.exists' => __('Email does not exist')
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            $credentials = $request->only('email', 'password');

            if (!$token = Auth::guard('player')->attempt($credentials)) {
                return jsonResponse(false, ['message' => __('Incorrect email or password')]);
            }

            $player = Auth::guard('player')->user();

            if ($player->status !== 'Active') {
                Auth::guard('player')->logout();
                return jsonResponse(false, ['message' => __('Account is not active')]);
            }

            // Check if email is verified
            if (!$player->is_email_verified) {
                Auth::guard('player')->logout();

                // Generate and send new OTP
                $this->sendVerificationOtp($player);

                return jsonResponse(true, [
                    'message' => __('Account is not verified. Please verify your account. OTP sent to your email.'),
                    'player' => $player
                ]);
            }

            if (!$player->is_phone_verified) {
                $this->sendMobileOtpPendingVerification($player);

                return jsonResponse(true, [
                    'message' => __('OTP sent successfully'),
                    'player' => $player
                ]);
            }

            return jsonResponse(true, [
                'message' => __('Login successful'),
                'token' => $token,
                'player' => $player
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to login: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to login due to ') . $e->getMessage()]);
        }
    }

    private function generateOtp(): string
    {
        return str_pad((string)mt_rand(0, 999999), 6, '0', STR_PAD_LEFT);
    }

    private function sendVerificationOtp(Player $player)
    {
        try {
            // Generate OTP
            $otp = $this->generateOtp();

            // Delete any existing OTP for this player
            PlayerOtpVerification::where('player_id', $player->player_id)->delete();

            // Create new OTP record
            PlayerOtpVerification::create([
                'player_id' => $player->player_id,
                'otp' => $otp,
                'is_phone_otp' => false,
                'expires_at' => Carbon::now()->addMinutes(30)
            ]);

            // Send email
           Mail::to($player->email)->send(new VerificationEmail($player, $otp));
        } catch (\Exception $e) {
            Log::error('Failed to send OTP: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to send OTP due to ') . $e->getMessage()]);
        }
    }

    public function resendOtp(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'player_id' => 'required|string'
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            $player = Player::where('player_id', $request->player_id)->first();

            if (!$player) {
                return jsonResponse(false, ['message' => __('Player not found')]);
            }

            if ($player->is_email_verified) {
                return jsonResponse(false, ['message' => __('Account is already verified. Please login.')]);
            }

            $this->sendVerificationOtp($player);

            return jsonResponse(true, ['message' => __('OTP sent successfully')]);
        } catch (\Exception $e) {
            Log::error('Failed to resend OTP: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to resend OTP due to ') . $e->getMessage()]);
        }
    }

    public function verifyOtp(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'player_id' => 'required|string',
                'otp' => 'required|string|size:6'
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            $verification = PlayerOtpVerification::where('player_id', $request->player_id)
                ->where('otp', $request->otp)
                ->where('is_phone_otp', false)
                ->first();

            if (!$verification) {
                return jsonResponse(false, ['message' => __('Invalid OTP')]);
            }

            if (!$verification->isValid()) {
                return jsonResponse(false, ['message' => __('OTP has expired')]);
            }

            $player = Player::where('player_id', $request->player_id)->first();

            if (!$player) {
                return jsonResponse(false, ['message' => __('Player not found')]);
            }

            // Mark player as verified
            $player->update(['is_email_verified' => true, 'is_verified' => $player->is_phone_verified ? true : false]);

            // Delete the OTP
            $verification->delete();

            if ($player->is_phone_verified) {
                // Generate token for auto-login
                $token = Auth::guard('player')->login($player);

                return jsonResponse(true, [
                    'message' => __('Account verified successfully'),
                    'token' => $token,
                    'player' => $player
                ]);
            } else {
                $this->sendMobileOtpPendingVerification($player);


                return jsonResponse(true, [
                    'message' => __('OTP sent successfully'),
                    'player' => $player
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to verify OTP: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to verify OTP due to ') . $e->getMessage()]);
        }
    }

    public function sendMobileOtpPendingVerification($player)
    {
        try {

            // Generate OTP
            $otp = $this->generateOtp();

            // Delete any existing OTP for this player
            PlayerOtpVerification::where('player_id', $player->player_id)->delete();

            // Create new OTP record
            PlayerOtpVerification::create([
                'player_id' => $player->player_id,
                'otp' => $otp,
                'is_phone_otp' => true,
                'expires_at' => Carbon::now()->addMinutes(30)
            ]);

            $message = __('Your Padel Rating APP verification OTP is ') . $otp . __('. This code will expire in 30 minutes.');

            TwilioService::sendSMS($player->country_code . $player->phone, $message);
        } catch (\Exception $e) {
            Log::error('Failed to send OTP: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to send OTP due to ') . $e->getMessage()]);
        }
    }

    public function sendMobileOtp(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'player_id' => 'required|string'
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            $player = Player::where('player_id', $request->player_id)
                ->first();

            if (!$player) {
                return jsonResponse(false, ['message' => __('Player not found')]);
            }

            // Generate OTP
            $otp = $this->generateOtp();

            // Delete any existing OTP for this player
            PlayerOtpVerification::where('player_id', $player->player_id)->delete();

            // Create new OTP record
            PlayerOtpVerification::create([
                'player_id' => $player->player_id,
                'otp' => $otp,
                'is_phone_otp' => true,
                'expires_at' => Carbon::now()->addMinutes(30)
            ]);

            $message = __('Your Padel Rating APP verification OTP is ') . $otp . __('. This code will expire in 30 minutes.');

            TwilioService::sendSMS($player->country_code . $player->phone, $message);

            return jsonResponse(true, [
                'message' => __('OTP sent successfully'),
                'player' => $player,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send OTP: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to send OTP due to ') . $e->getMessage()]);
        }
    }

    public function verifyMobileOtp(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'player_id' => 'required|string',
                'otp' => 'required|string|size:6'
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            $verification = PlayerOtpVerification::where('player_id', $request->player_id)
                ->where('otp', $request->otp)
                ->where('is_phone_otp', true)
                ->first();

            if (!$verification) {
                return jsonResponse(false, ['message' => __('Invalid OTP')]);
            }

            if (!$verification->isValid()) {
                return jsonResponse(false, ['message' => __('OTP has expired')]);
            }

            $player = Player::where('player_id', $request->player_id)->first();

            if (!$player) {
                return jsonResponse(false, ['message' => __('Player not found')]);
            }

            // Mark player as verified
            $player->update(['is_phone_verified' => true, 'is_verified' => $player->is_email_verified ? true : false]);

            // Delete the OTP
            $verification->delete();

            $player->update([
                'trial_credits' => 30
            ]);

            // Generate token for auto-login
            $token = Auth::guard('player')->login($player);

            return jsonResponse(true, [
                'message' => __('Phone number verified successfully'),
                'token' => $token,
                'player' => $player
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to verify OTP: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to verify OTP due to ') . $e->getMessage()]);
        }
    }

    public function logout()
    {
        try {
            Auth::guard('player')->logout();

            return jsonResponse(true, ['message' => __('Successfully logged out')]);
        } catch (TokenExpiredException $e) {
            return jsonResponse(false, [
                'message' => __('Login session has expired')
            ]);
        } catch (TokenInvalidException $e) {
            return jsonResponse(false, [
                'message' => __('Login session is invalid')
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to logout: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => __('Failed to logout due to ') . $e->getMessage()
            ]);
        }
    }

    public function changePassword(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'current_password' => 'required|string|min:6',
                'new_password' => 'required|string|min:6|confirmed'
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            $player = Auth::guard('player')->user();

            if (!Hash::check($request->current_password, $player->password)) {
                return jsonResponse(false, ['message' => __('Current password is incorrect')]);
            }

            $player->update([
                'password' => Hash::make($request->new_password)
            ]);

            return jsonResponse(true, [
                'message' => __('Password changed successfully')
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to change password: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => __('Failed to change password ') . $e->getMessage()
            ]);
        }
    }

    public function profile()
    {
        try {
            $player = Auth::guard('player')->user();
            return jsonResponse(true, ['player' => $player]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch profile: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to fetch profile ') . $e->getMessage()]);
        }
    }



    public function forgotPassword(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'email' => 'required|email'
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            $player = Player::where('email', $request->email)->first();

            if (!$player) {
                return jsonResponse(true, [
                    'message' => __('If an account exists with this email, you will receive a verification code.')
                ]);
            }

            $this->sendVerificationOtp($player);

            return jsonResponse(true, [
                'message' => __('Password reset code has been sent to your email'),
                'player_id' => $player->player_id
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send verification code: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to send verification code due to ') . $e->getMessage()]);
        }
    }

    public function verifyForgotPasswordOtp(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'player_id' => 'required|string',
                'otp' => 'required|string|size:6'
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            $verification = PlayerOtpVerification::where('player_id', $request->player_id)
                ->where('otp', $request->otp)
                ->first();

            if (!$verification) {
                return jsonResponse(false, ['message' => __('Invalid verification code')]);
            }

            if (!$verification->isValid()) {
                return jsonResponse(false, ['message' => __('Verification code has expired')]);
            }

            $player = Player::where('player_id', $request->player_id)->first();

            if (!$player) {
                return jsonResponse(false, ['message' => __('Player not found')]);
            }

            // Delete the OTP after successful verification
            $verification->delete();

            // Generate a temporary token for password reset
            $resetToken = Str::random(60);

            // Store the reset token with an expiration
            Cache::put(
                'password_reset_' . $player->player_id,
                $resetToken,
                Carbon::now()->addMinutes(30)
            );

            return jsonResponse(true, [
                'message' => __('Verification successful'),
                'reset_token' => $resetToken,
                'player' => $player
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to verify OTP: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to verify OTP due to ') . $e->getMessage()]);
        }
    }

    // TODO: Do this more securely using reset token check
    public function resetPassword(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'player_id' => 'required|string',
                // 'reset_token' => 'required|string',
                'password' => 'required|string|min:6|confirmed'
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            $player = Player::where('player_id', $request->player_id)->first();

            if (!$player) {
                return jsonResponse(false, ['message' => __('Player not found')]);
            }

            $storedToken = Cache::get('password_reset_' . $player->player_id);

            // if (!$storedToken || $storedToken !== $request->reset_token) {
            //     return jsonResponse(false, ['message' => __('Invalid or expired reset token')]);
            // }

            // Update password
            $player->update([
                'password' => Hash::make($request->password)
            ]);

            // Delete the reset token
            Cache::forget('password_reset_' . $player->player_id);

            // Generate new auth token
            $token = Auth::guard('player')->login($player);

            return jsonResponse(true, [
                'message' => __('Password has been reset successfully'),
                'token' => $token,
                'player' => $player
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to reset password: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to reset password due to ') . $e->getMessage()]);
        }
    }

    public function updateFcmToken(Request $request)
    {
        try {
            $player = Auth::guard('player')->user();
            $player->update(['fcm_token' => $request->fcm_token]);
            return jsonResponse(true, ['message' => __('FCM token updated successfully')]);
        } catch (\Exception $e) {
            Log::error('Failed to update FCM token: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to update FCM token ') . $e->getMessage()]);
        }
    }

    public function unreadNotificationsCount()
    {
        try {
            $player = Auth::guard('player')->user()->player_id;
            $count = Notification::where('player_id', $player)->where('is_read', false)->count();
            return jsonResponse(true, ['count' => $count]);
        } catch (\Exception $e) {
            return jsonResponse(false, ['message' => __('Failed to fetch notifications ') . $e->getMessage()]);
        }
    }

    public function notifications()
    {
        try {
            $playerId = Auth::guard('player')->user()->player_id;

            // Get unread notifications
            $unreadNotifications = Notification::where('player_id', $playerId)
                ->where('is_read', false)
                ->get();

            // Mark each notification as read individually to trigger model events
            foreach ($unreadNotifications as $notification) {
                $notification->is_read = true;
                $notification->save(); // This will trigger the updated event
            }

            // If there are unread notifications, manually update Firebase counts
            if ($unreadNotifications->count() > 0) {
                // Get the first notification and call updateFirebaseCounts
                // This will update the count to 0 since we've marked all as read
                if ($unreadNotifications->first()) {
                    $unreadNotifications->first()->updateFirebaseCounts();
                }
            }

            // Get all notifications for pagination
            $notifications = Notification::where('player_id', $playerId)
                ->latest()
                ->paginate(10);

            return jsonResponse(true, [
                'notifications' => $notifications->items(),
                'pagination' => [
                    'total' => $notifications->total(),
                    'per_page' => $notifications->perPage(),
                    'current_page' => $notifications->currentPage(),
                    'last_page' => $notifications->lastPage(),
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch notifications: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to fetch notifications ') . $e->getMessage()]);
        }
    }

    public function updateProfile(Request $request)
    {
        try {
            $player = Auth::guard('player')->user();

            $validator = Validator::make($request->all(), [
                'first_name' => 'nullable|string|max:30',
                'last_name' => 'nullable|string|max:30',
                'phone' => 'nullable|string|max:15',
                'country_code' => 'nullable|string|max:255',
                'postal_code' => 'nullable|string|max:10',
                'country' => 'nullable|string|max:255',
                'sex' => 'nullable|in:Male,Female,Other',
                'rating_type' => 'nullable',
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            $updateData = [
                'first_name' => $request->first_name ?? $player->first_name,
                'last_name' => $request->last_name ?? $player->last_name,
                'phone' => $request->phone ?? $player->phone,
                'country_code' => $request->country_code ?? $player->country_code,
                'postal_code' => $request->postal_code ?? $player->postal_code,
                'country' => $request->country ?? $player->country,
                'sex' => $request->sex ?? $player->sex,
                'rating_type' => $request->rating_type ?? $player->rating_type,
            ];


            $player->update($updateData);

            // Refresh player data
            $player = $player->fresh();

            return jsonResponse(true, [
                'message' => __('Profile updated successfully'),
                'player' => $player
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update profile: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => __('Failed to update profile ') . $e->getMessage()
            ]);
        }
    }

    public function updateImage(Request $request)
    {
        try {
            $player = Auth::guard('player')->user();

            $validator = Validator::make($request->all(), [
                'photo' => 'required|image|mimes:jpg,jpeg,png,webp|max:10240',
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            $filePath = uploadFile($request->file('photo'), 'players', 50, $player->photo);

            $player->update(['photo' => $filePath]);

            // Refresh player data
            $player = $player->fresh();

            return jsonResponse(true, [
                'message' => __('Profile image updated successfully'),
                'player' => $player
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update profile image: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => __('Failed to update profile image ') . $e->getMessage()
            ]);
        }
    }

    public function updateLanguage(Request $request)
    {
        try {
            $player = Auth::guard('player')->user();
            $player->update(['language' => $request->language]);
            return jsonResponse(true, ['message' => __('Language updated successfully', [], $request->language ?? 'en')]);
        } catch (\Exception $e) {
            Log::error('Failed to update language: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to update language ') . $e->getMessage()]);
        }
    }

    public function deleteAccount()
    {
        try {
            $player = Auth::guard('player')->user();
            $player->delete();
            return jsonResponse(true, ['message' => __('Account deleted successfully')]);
        } catch (\Exception $e) {
            Log::error('Failed to delete account: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to delete account ') . $e->getMessage()]);
        }
    }
}
