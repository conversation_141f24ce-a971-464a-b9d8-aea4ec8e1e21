<?php

namespace App\Http\Controllers\Api\Player;

use App\Http\Controllers\Controller;
use App\Models\Game;
use App\Models\PlayerComment;
use App\Models\PlayerStat;
use App\Services\TimezoneService;
use Illuminate\Support\Facades\Auth;

class CoachCommentsController extends Controller
{
    protected $timezoneService;

    public function __construct(TimezoneService $timezoneService)
    {
        $this->timezoneService = $timezoneService;
    }
    /**
     * Get coach comments for the authenticated player's last 7 games
     * Client clarification: Last 7 games (not 7 days), same logic as stats
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCommentsByGameIds()
    {
        $player = Auth::guard('player')->user();

        if (!$player) {
            return jsonResponse(false, ['message' => 'Player not authenticated']);
        }

        // Get user timezone based on IP
        $userIP = $this->timezoneService->getUserIP();
        $userTimezone = $this->timezoneService->getUserTimezoneByIP($userIP);

        // Get all games for this player, ordered by date/time (most recent first)
        // Using PlayerStat to find games where player has data (same logic as StatsController)
        $playerGameIds = PlayerStat::where('player_id', $player->player_id)
            ->distinct()
            ->pluck('game_id')
            ->toArray();

        if (empty($playerGameIds)) {
            return jsonResponse(true, [
                'comments' => [],
                'total_games' => 0
            ]);
        }

        $games = Game::whereIn('id', $playerGameIds)
            ->orderBy('date', 'desc')
            ->orderBy('end_time', 'desc')
            ->get();

        // Filter ended games and take only last 7 (or less if player has fewer than 7 games)
        $endedGameIds = [];
        foreach ($games as $game) {
            if ($this->timezoneService->hasGameEnded($game, $userTimezone)) {
                $endedGameIds[] = $game->id;
                if (count($endedGameIds) >= 7) {
                    break; // Only take last 7 games
                }
            }
        }

        if (empty($endedGameIds)) {
            return jsonResponse(true, [
                'comments' => [],
                'total_games' => count($playerGameIds)
            ]);
        }

        // Get comments for last 7 ended games only
        $comments = PlayerComment::with(['coachComment', 'game'])
            ->where('player_id', $player->player_id)
            ->whereIn('game_id', $endedGameIds)
            // ->orderBy('date', 'desc')
            // ->orderBy('time', 'desc')
            ->get();

        // Format comments for response
        $formattedComments = [];
        foreach ($comments as $comment) {
            $formattedComments[] = [
                'id' => $comment->id,
                'game_id' => $comment->game_id,
                'message' => $comment->coachComment->getLocalizedMessageAttribute(),
                'date' => $comment->date,
                'time' => $comment->time,
                'game_date' => $comment->game->date,
                'camera_id' => $comment->game->camera_id
            ];
        }

        return jsonResponse(true, [
            'comments' => $formattedComments,
            'total_games' => count($playerGameIds),
            'games_shown' => count($endedGameIds)
        ]);
    }

    /**
     * Get coach comments for a specific game
     * Client clarification: Show last game's comments, similar to last game stats logic
     *
     * @param int $gameId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getGameComments($gameId)
    {
        $player = Auth::guard('player')->user();

        if (!$player) {
            return jsonResponse(false, ['message' => 'Player not authenticated']);
        }

        // Get user timezone based on IP
        $userIP = $this->timezoneService->getUserIP();
        $userTimezone = $this->timezoneService->getUserTimezoneByIP($userIP);

        // Check if the game exists
        $game = Game::find($gameId);
        if (!$game) {
            return jsonResponse(false, ['message' => 'Game not found', 'comments' => []]);
        }

        if (!$this->timezoneService->hasGameEnded($game, $userTimezone)) {
            return jsonResponse(false, ['message' => 'Game has not ended yet', 'comments' => []]);
        }

        // Get comments for this game for this player
        $playerComments = PlayerComment::with(['coachComment'])
            ->where('game_id', $gameId)
            ->whereNull('player_id') // Only games comments
            ->orderBy('date', 'desc')
            ->orderBy('time', 'desc')
            ->get();

        // Format player's comments for response
        $formattedPlayerComments = [];
        foreach ($playerComments as $comment) {
            $formattedPlayerComments[] = [
                'id' => $comment->id,
                'message' => $comment->coachComment->getLocalizedMessageAttribute(),
                'message_id' => $comment->coach_comment_id,
                'date' => $comment->date,
                'time' => $comment->time
            ];
        }

        return jsonResponse(true, [
            'game_id' => $game->id,
            'game_date' => $game->date,
            'camera_id' => $game->camera_id,
            'comments' => $formattedPlayerComments,
        ]);
    }

    /**
     * Get latest coach comments for the authenticated player from their last game
     * Client clarification: Show latest comments from last game, similar to getLastGameStats
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getLatestComments()
    {
        $player = Auth::guard('player')->user();

        if (!$player) {
            return jsonResponse(false, ['message' => 'Player not authenticated']);
        }

        // Get user timezone based on IP
        $userIP = $this->timezoneService->getUserIP();
        $userTimezone = $this->timezoneService->getUserTimezoneByIP($userIP);

        // Get all games for this player and filter ended games (same logic as StatsController)
        $playerGameIds = PlayerStat::where('player_id', $player->player_id)
            ->distinct()
            ->pluck('game_id');

        $games = Game::whereIn('id', $playerGameIds)->orderBy('date', 'desc')->orderBy('end_time', 'desc')->get();

        // Find the latest ended game
        $latestGame = null;
        foreach ($games as $game) {
            if ($this->timezoneService->hasGameEnded($game, $userTimezone)) {
                $latestGame = $game;
                break;
            }
        }

        if (!$latestGame) {
            return jsonResponse(true, [
                'comments' => [],
                'message' => 'No completed games found'
            ]);
        }

        // Get comments for the latest game for this player
        $latestGameComments = PlayerComment::with(['coachComment', 'game'])
            ->where('player_id', $player->player_id)
            ->where('game_id', $latestGame->id)
            ->orderBy('date', 'desc')
            ->orderBy('time', 'desc')
            ->get();

        // Format comments for response
        $formattedComments = [];
        foreach ($latestGameComments as $comment) {
            $formattedComments[] = [
                'id' => $comment->id,
                'game_id' => $comment->game_id,
                'message' => $comment->coachComment->getLocalizedMessageAttribute(),
                'message_id' => $comment->coach_comment_id,
                'date' => $comment->date,
                'time' => $comment->time,
                'game_date' => $comment->game->date,
                'camera_id' => $comment->game->camera_id
            ];
        }

        return jsonResponse(true, [
            'comments' => $formattedComments,
            'game_id' => $latestGame->id,
            'game_date' => $latestGame->date,
            'camera_id' => $latestGame->camera_id,
            'message' => 'Latest comments from last completed game'
        ]);
    }

    /**
     * Get latest game comments for all players (similar to getLatestGameStats)
     * Client clarification: Show latest combined comments for all players registered in the app
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getLatestGameComments()
    {
        // Get user timezone based on IP
        $userIP = $this->timezoneService->getUserIP();
        $userTimezone = $this->timezoneService->getUserTimezoneByIP($userIP);

        // Get latest game that has comments
        $latestGameWithComments = PlayerComment::with('game')
            ->whereNull('player_id') // Only game-level comments
            ->orderBy('date', 'desc')
            ->orderBy('time', 'desc')
            ->first();

        if (!$latestGameWithComments) {
            return jsonResponse(true, [
                'comments' => [],
                'message' => 'No game comments available'
            ]);
        }

        $game = $latestGameWithComments->game;

        // Check if game has ended
        if (!$this->timezoneService->hasGameEnded($game, $userTimezone)) {
            return jsonResponse(false, ['message' => 'Game has not ended yet']);
        }

        // Get all comments for this game (for all players)
        $gameComments = PlayerComment::with(['coachComment'])
            ->where('game_id', $game->id)
            ->whereNull('player_id') // Only game-level comments
            ->orderBy('date', 'desc')
            ->orderBy('time', 'desc')
            ->get();

        // Format comments for response
        $formattedComments = [];
        foreach ($gameComments as $comment) {
            $formattedComments[] = [
                'id' => $comment->id,
                'message' => $comment->coachComment->getLocalizedMessageAttribute(),
                'message_id' => $comment->coach_comment_id,
                'date' => $comment->date,
                'time' => $comment->time
            ];
        }

        return jsonResponse(true, [
            'game_id' => $game->id,
            'game_date' => $game->date,
            'camera_id' => $game->camera_id,
            'start_time' => $game->start_time,
            'end_time' => $game->end_time,
            'comments' => $formattedComments,
            'message' => 'Latest game comments for all players'
        ]);
    }
}
