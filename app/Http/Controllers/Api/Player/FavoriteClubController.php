<?php

namespace App\Http\Controllers\Api\Player;

use App\Http\Controllers\Controller;
use App\Models\Club;
use App\Models\FavoriteClub;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class FavoriteClubController extends Controller
{
    public function toggle(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'club_id' => 'required|string|max:100',
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            // Check if club exists
            $club = Club::where('club_id', $request->club_id)->first();
            if (!$club) {
                return jsonResponse(false, ['message' => __('Club not found')]);
            }

            $player = Auth::guard('player')->user();
            $isFavorited = FavoriteClub::toggle($player->player_id, $request->club_id);

            return jsonResponse(true, [
                'message' => $isFavorited ? __('Club added to favorites') : __('Club removed from favorites'),
                'is_favorited' => $isFavorited
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update favorite status: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to update favorite status ') . $e->getMessage()]);
        }
    }

    public function index(Request $request)
    {
        try {
            $player = Auth::guard('player')->user();

            $query = Club::query()
                ->select(['club_id', 'club_name', 'country', 'city'])
                ->where('status', 'Active')
                ->whereHas('favoriteClubs', function ($q) use ($player) {
                    $q->where('player_id', $player->player_id);
                });

            // Search functionality
            if ($request->search) {
                $query->where(function ($q) use ($request) {
                    $q->where('club_name', 'like', '%' . $request->search . '%')
                        ->orWhere('country', 'like', '%' . $request->search . '%')
                        ->orWhere('city', 'like', '%' . $request->search . '%')
                        ->orWhere('club_id', 'like', '%' . $request->search . '%');
                });
            }

            $favorites = $query->latest()
                ->paginate(10);

            return jsonResponse(true, [
                'favorites' => $favorites->items(),
                'pagination' => [
                    'total' => $favorites->total(),
                    'per_page' => $favorites->perPage(),
                    'current_page' => $favorites->currentPage(),
                    'last_page' => $favorites->lastPage(),
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch favorite clubs: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to fetch favorite clubs ') . $e->getMessage()]);
        }
    }
}
