<?php

namespace App\Http\Controllers\Api\Player;

use App\Http\Controllers\Controller;
use App\Models\Camera;
use App\Models\Player;
use App\Models\PlayerCredit;
use App\Models\Recording;
use App\Services\FirebaseDatabaseService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class RecordingController extends Controller
{
    protected $firebaseService;

    public function __construct(FirebaseDatabaseService $firebaseService)
    {
        $this->firebaseService = $firebaseService;
    }

    public function index()
    {
        $player = auth('player')->user();
        $playerId = $player->player_id;

        $recordings = Recording::where('player_id', $playerId)->latest()->paginate(10);

        // Check if all recordings are expired
        $allExpired = true;

        // is_expired check
        $recordings->map(function ($recording) use (&$allExpired) {
            $isExpired = Carbon::parse($recording->date . ' ' . $recording->end_time)->isPast();
            $recording->is_expired = $isExpired;
            $recording->time_left = abs(round(Carbon::parse($recording->date . ' ' . $recording->end_time)->diffInMinutes(now())));

            // If any recording is not expired, set allExpired to false
            if (!$isExpired) {
                $allExpired = false;
            }

            if ($isExpired && $recording->status !== 'completed') {
                $recording->status = 'completed';
                $recording->save();
            }

            return $recording;
        });

        // If player has recordings and all are expired, clear the current_recording in Firebase
        if ($recordings->count() > 0 && $allExpired) {
            try {
                $this->firebaseService->clearCurrentRecording($playerId);
            } catch (\Exception $e) {
                Log::error('Failed to clear current recording in Firebase', [
                    'player_id' => $playerId,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return jsonResponse(true, [
            'recordings' => $recordings->items(),
            'pagination' => [
                'total' => $recordings->total(),
                'per_page' => $recordings->perPage(),
                'current_page' => $recordings->currentPage(),
                'last_page' => $recordings->lastPage(),
            ]
        ]);
    }

    public function storeOrUpdate(Request $request)
    {
        try {
            $validator = validator($request->all(), [
                'camera_id' => 'required|string|max:100',
                'club_id' => 'required|string|max:100',
                'warm_area' => 'nullable|numeric|max:100',
                'start_time' => 'nullable',
                'end_time' => 'nullable',
                'credits' => 'required|numeric|min:60',
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }


            // check if camera is already in use where end time is not reached
            $recording = Recording::where('camera_id', $request->camera_id)->where('warm_area', $request->warm_area)->where('date', now()->format('Y-m-d'))->where('end_time', '>', now()->format('H:i:s'))->first();

            if ($recording) {
                return jsonResponse(false, [
                    'message' => __('This camera is already in use. Please try again later.')
                ]);
            }

            $player = auth('player')->user();
            $requiredCredits = (int) $request->credits;

            // check credits are available
            $playerCredits = PlayerCredit::where('player_id', $player->player_id)
                ->where('club_id', $request->club_id)
                ->first();

            if (!$playerCredits || $playerCredits?->credits < $requiredCredits) {
                $totalCredits = $playerCredits?->credits + $player->trial_credits;
                if($totalCredits < $requiredCredits){
                    return jsonResponse(false, [
                        'message' => __('You do not have enough credits to start recording. Please purchase more credits.')
                    ]);
                }
            }

            $recording = Recording::create([
                'camera_id' => $request->camera_id,
                'player_id' => $player->player_id,
                'date' => now()->format('Y-m-d'),
                'start_time' => Carbon::parse($request->start_time)->format('H:i:s'),
                'end_time' => Carbon::parse($request->end_time)->addMinutes($requiredCredits)->format('H:i:s'),
                'warm_area' => $request->warm_area,
                'sex' => $player->sex,
            ]);

            // Deduct credits from player's club-specific credits & trial credits
            $playerCredits->credits -= max(0, $requiredCredits);
            $player->trial_credits = 0;
            $player->save();

            // Update recording in Firebase for real-time tracking
            try {
                $this->firebaseService->updatePlayerRecording(
                    $player->player_id,
                    $recording->id,
                    Carbon::parse($recording->start_time)->format('H:i:s'),
                    Carbon::parse($recording->end_time)->format('H:i:s'),
                    $requiredCredits
                );
            } catch (\Exception $e) {
                // Log the error but don't fail the request
                Log::error('Failed to update recording in Firebase: ' . $e->getMessage(), [
                    'player_id' => $player->player_id,
                    'recording_id' => $recording->id
                ]);
            }

            $now = Carbon::now();
            $today = $now->format('Y-m-d');
            $yesterday = $now->copy()->subDay()->format('Y-m-d');
            $currentTime = $now->format('H:i:s');
            
            $isFirstRecording = Recording::where('camera_id', $request->camera_id)
                ->where(function ($query) use ($today, $yesterday, $currentTime) {
                    $query->where(function ($q) use ($today, $currentTime) {
                        // Recordings started today
                        $q->where('date', $today)
                          ->where('start_time', '<=', $currentTime)
                          ->where('end_time', '>=', $currentTime);
                    })->orWhere(function ($q) use ($yesterday, $currentTime) {
                        // Recordings started yesterday but end time is after current time (still ongoing)
                        $q->where('date', $yesterday)
                          ->where('end_time', '>=', $currentTime);
                    });
                })
                ->count() == 1;

            return jsonResponse(true, [
                'message' => __('Recording added successfully'),
                'recording' => $recording,
                'is_first_recording' => $isFirstRecording
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => __('Failed to add recording ') . $e->getMessage()
            ]);
        }
    }

    public function checkCameraAvailability(Request $request)
    {
        try {
            $validator = validator($request->all(), [
                'camera_id' => 'required|string|max:100',
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            // check if camera exists
            $camera  = Camera::where('camera_id', $request->camera_id)->first();

            if (!$camera) {
                return jsonResponse(false, ['message' => __('Camera not found')]);
            }

            $player = auth('player')->user();

            // check if player has credits of this camera club
            $credits = PlayerCredit::where('player_id', $player->player_id)->where('club_id', $camera->club_id)->with('club:club_id,club_name')->first();

            if (!$credits || $credits?->credits < 60) {
                $totalCredits = $credits?->credits + $player->trial_credits;
                if($totalCredits < 60){
                    return jsonResponse(false, [
                        'message' => __('You do not have enough credits for this club. Please purchase more credits.')
                    ]);
                }
            }

            // check if camera is already in use where end time is not reached
            // $recording = Recording::where('camera_id', $request->camera_id)->where('end_time', '>', now()->format('H:i:s'))->first();

            // if ($recording) {
            //     return jsonResponse(false, [
            //         'message' => __('This camera is already in use. Please try again later.')
            //     ]);
            // }

            return jsonResponse(true, [
                'camera' => $camera,
                'credits' => $credits
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => __('Failed to check camera ') . $e->getMessage()
            ]);
        }
    }

    public function checkRecordingStatus(Request $request)
    {
        try {
            $validator = validator($request->all(), [
                'recording_id' => 'required|string|max:100',
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            $recording = Recording::where('id', $request->recording_id)->first();

            if (!$recording) {
                return jsonResponse(false, ['message' => __('Recording not found')]);
            }

            $recording->is_expired = Carbon::parse($recording->date . ' ' . $recording->end_time)->isPast();
            $recording->time_left = abs(round(Carbon::parse($recording->date . ' ' . $recording->end_time)->diffInMinutes(now())));

            // Check if recording is expired
            if ($recording->is_expired) {
                $recording->status = 'completed';
                $recording->save();

                // If expired, clear the current recording in Firebase
                try {
                    $this->firebaseService->clearCurrentRecording($recording->player_id);
                    Log::info('Cleared expired recording in Firebase during status check', [
                        'player_id' => $recording->player_id,
                        'recording_id' => $recording->id
                    ]);
                } catch (\Exception $e) {
                    // Log the error but don't fail the request
                    Log::error('Failed to clear expired recording in Firebase during status check: ' . $e->getMessage(), [
                        'player_id' => $recording->player_id,
                        'recording_id' => $recording->id
                    ]);
                }
            } else {
                // If not expired, update the current recording in Firebase
                try {
                    // Calculate credits from the time difference
                    $startTime = Carbon::parse($recording->start_time);
                    $endTime = Carbon::parse($recording->end_time);
                    $credits = $endTime->diffInMinutes($startTime);

                    $this->firebaseService->updatePlayerRecording(
                        $recording->player_id,
                        $recording->id,
                        $startTime->format('H:i:s'),
                        $endTime->format('H:i:s'),
                        $credits
                    );
                } catch (\Exception $e) {
                    // Log the error but don't fail the request
                    Log::error('Failed to update recording in Firebase during status check: ' . $e->getMessage(), [
                        'player_id' => $recording->player_id,
                        'recording_id' => $recording->id
                    ]);
                }
            }

            return jsonResponse(true, [
                'recording' => $recording
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => __('Failed to check recording ') . $e->getMessage()
            ]);
        }
    }

    public function deleteRecording(Request $request)
    {
        try {
            $validator = validator($request->all(), [
                'recording_id' => 'required',
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            $recording = Recording::where('id', $request->recording_id)->first();

            if (!$recording) {
                return jsonResponse(false, ['message' => __('Recording not found')]);
            }

            $recording->delete();

            return jsonResponse(true, [
                'message' => __('Recording deleted successfully')
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => __('Failed to delete recording ') . $e->getMessage()
            ]);
        }
    }
}
