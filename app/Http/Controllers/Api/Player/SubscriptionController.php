<?php

namespace App\Http\Controllers\Api\Player;

use App\Http\Controllers\Controller;
use App\Models\Player;
use App\Models\PlayerSubscription;
use App\Models\Stat;
use App\Models\SubscriptionPlan;
use App\Models\SubscriptionTransaction;
use App\Services\StripeSubscriptionService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class SubscriptionController extends Controller
{
    protected $stripeService;

    public function __construct(StripeSubscriptionService $stripeService)
    {
        $this->stripeService = $stripeService;
    }

    /**
     * Get available subscription plans for the player's country
     */
    public function getPlans()
    {
        try {
            $player = Auth::guard('player')->user();
            $country = $player->country;

            if (!$country) {
                return jsonResponse(false, [
                    'message' => __('Please update your profile with your country information')
                ]);
            }

            // Get plans for the player's country
            $plans = SubscriptionPlan::getActivePlansForCountry($country);

            // If no plans exist for this country, create a default one
            if ($plans->isEmpty()) {
                $plan = SubscriptionPlan::getOrCreateDefaultPlan($country);
                $plans = collect([$plan]);
            }

            return jsonResponse(true, [
                'plans' => $plans
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get subscription plans: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => __('Failed to get subscription plans: ') . $e->getMessage()
            ]);
        }
    }

    /**
     * Get the player's current subscription
     */
    public function getCurrentSubscription()
    {
        try {
            $player = Auth::guard('player')->user();
            $subscription = $player->activeSubscription();

            if (!$subscription) {
                return jsonResponse(true, [
                    'has_subscription' => false,
                    'message' => __('You do not have an active subscription')
                ]);
            }

            // Load the plan
            $subscription->load('plan');

            return jsonResponse(true, [
                'has_subscription' => true,
                'subscription' => $subscription
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get current subscription: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => __('Failed to get current subscription: ') . $e->getMessage()
            ]);
        }
    }

    /**
     * Create a checkout session for subscription
     */
    public function createCheckoutSession(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'plan_id' => 'required|exists:subscription_plans,id',
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            $player = Auth::guard('player')->user();
            $plan = SubscriptionPlan::findOrFail($request->plan_id);

            // Check if player already has an active subscription
            if ($player->hasActiveSubscription()) {
                return jsonResponse(false, [
                    'message' => __('You already have an active subscription')
                ]);
            }

            // Create checkout session
            $successUrl = route('subscription.success');
            $cancelUrl = route('subscription.cancel');

            $session = $this->stripeService->createCheckoutSession(
                $player,
                $plan,
                $successUrl,
                $cancelUrl
            );

            return jsonResponse(true, [
                'checkout_url' => $session->url
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to create checkout session: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => __('Failed to create checkout session: ') . $e->getMessage()
            ]);
        }
    }

    /**
     * Cancel the player's subscription
     */
    public function cancelSubscription(Request $request)
    {
        try {
            $player = Auth::guard('player')->user();
            $subscription = $player->activeSubscription();

            if (!$subscription) {
                return jsonResponse(false, [
                    'message' => __('You do not have an active subscription to cancel')
                ]);
            }

            // Cancel at period end by default
            $cancelAtPeriodEnd = $request->input('cancel_at_period_end', false);

            $this->stripeService->cancelSubscription($subscription, $cancelAtPeriodEnd);

            // Update player subscription status
            $player->updateSubscriptionStatus();

            return jsonResponse(true, [
                'message' => $cancelAtPeriodEnd
                    ? __('Your subscription will be canceled at the end of the current billing period')
                    : __('Your subscription has been canceled immediately'),
                'subscription' => $subscription->fresh()
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to cancel subscription: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => __('Failed to cancel subscription: ') . $e->getMessage()
            ]);
        }
    }

    /**
     * Update payment method for subscription
     */
    public function updatePaymentMethod(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'payment_method_id' => 'required|string',
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            $player = Auth::guard('player')->user();
            $subscription = $player->activeSubscription();

            if (!$subscription) {
                return jsonResponse(false, [
                    'message' => __('You do not have an active subscription')
                ]);
            }

            $this->stripeService->updatePaymentMethod(
                $subscription,
                $request->payment_method_id
            );

            return jsonResponse(true, [
                'message' => __('Payment method updated successfully')
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update payment method: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => __('Failed to update payment method: ') . $e->getMessage()
            ]);
        }
    }

    /**
     * Get subscription transaction history
     */
    public function getTransactionHistory()
    {
        try {
            $player = Auth::guard('player')->user();

            $transactions = SubscriptionTransaction::where('player_id', $player->player_id)
                ->with('subscription.plan')
                ->orderBy('created_at', 'desc')
                ->get();

            return jsonResponse(true, [
                'transactions' => $transactions
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get transaction history: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => __('Failed to get transaction history: ') . $e->getMessage()
            ]);
        }
    }

    /**
     * Handle successful subscription checkout
     */
    public function success(Request $request)
    {
        try {
            $sessionId = $request->query('session_id');

            if (!$sessionId) {
                return jsonResponse(false, [
                    'message' => __('Invalid request')
                ]);
            }

            // Retrieve the session from Stripe
            \Stripe\Stripe::setApiKey(config('services.stripe.secret'));
            $session = \Stripe\Checkout\Session::retrieve([
                'id' => $sessionId,
                'expand' => ['subscription', 'customer']
            ]);

            if (!$session) {
                return jsonResponse(false, [
                    'message' => __('Session not found')
                ]);
            }

            // Get player from metadata
            $playerId = $session->metadata->player_id ?? null;
            $planId = $session->metadata->plan_id ?? null;

            if (!$playerId || !$planId) {
                return jsonResponse(false, [
                    'message' => __('Invalid session metadata')
                ]);
            }

            $player = Player::find($playerId);
            $plan = SubscriptionPlan::find($planId);

            if (!$player || !$plan) {
                return jsonResponse(false, [
                    'message' => __('Player or plan not found')
                ]);
            }

            // Check if subscription already exists
            $existingSubscription = PlayerSubscription::where('stripe_subscription_id', $session->subscription->id)->first();

            if (!$existingSubscription) {
                // Create subscription record
                $subscription = PlayerSubscription::create([
                    'player_id' => $player->player_id,
                    'subscription_plan_id' => $plan->id,
                    'stripe_subscription_id' => $session->subscription->id,
                    'stripe_customer_id' => $session->customer->id,
                    'status' => $session->subscription->status,
                    'current_period_starts_at' => $session->subscription->current_period_start ? Carbon::createFromTimestamp($session->subscription->current_period_start) : null,
                    'current_period_ends_at' => $session->subscription->current_period_end ? Carbon::createFromTimestamp($session->subscription->current_period_end) : null,
                ]);

                // Create transaction record
                if ($session->subscription->latest_invoice) {
                    SubscriptionTransaction::create([
                        'player_id' => $player->player_id,
                        'player_subscription_id' => $subscription->id,
                        'amount' => $plan->price,
                        'currency' => $plan->currency,
                        'stripe_invoice_id' => $session->subscription->latest_invoice,
                        'status' => 'paid',
                        'paid_at' => Carbon::now(),
                    ]);
                }

                // Update player subscription status
                $player->update(['subscribed' => true]);
                Stat::incrementSubscribed();
            }

            return jsonResponse(true, [
                'message' => __('Subscription created successfully')
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to process subscription success: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => __('Failed to process subscription: ') . $e->getMessage()
            ]);
        }
    }

    /**
     * Handle canceled subscription checkout
     */
    public function cancel()
    {
        return jsonResponse(false, [
            'message' => __('Subscription checkout was canceled')
        ]);
    }
}
