<?php

namespace App\Http\Controllers\Api\Player;

use App\Http\Controllers\Controller;
use App\Models\Club;
use App\Models\FavoriteClub;
use App\Models\Notification;
use App\Models\PlayerCredit;
use App\Models\Token;
use App\Models\TokenOrder;
use App\Services\FCMService;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;


class TokenController extends Controller
{
    public function clubs(Request $request)
    {
        try {
            $player = Auth::guard('player')->user();

            $query = Club::query()
                ->select([
                    'club_id',
                    'club_name',
                    'country',
                    'city',
                    'sell_tokens',
                    'currency'
                ])
                ->where('status', 'Active');

            // Search by club name, country, club_id
            if ($request->search) {
                $query->where(function ($q) use ($request) {
                    $q->where('club_name', 'like', '%' . $request->search . '%')
                        ->orWhere('country', 'like', '%' . $request->search . '%')
                        ->orWhere('city', 'like', '%' . $request->search . '%')
                        ->orWhere('club_id', 'like', '%' . $request->search . '%');
                });
            }

            $clubs = $query->latest()->paginate(10);

            // Add is_favorited field and sort
            $clubs->map(function ($club) use ($player) {
                $is_favorited = FavoriteClub::where('club_id', $club->club_id)
                    ->where('player_id', $player->player_id)
                    ->exists();

                $club->is_favorited = $is_favorited;
                return $club;
            })
                ->sortByDesc('is_favorited')
                ->values();

            return jsonResponse(true, [
                'data' => $clubs->items(),
                'pagination' => [
                    'total' => $clubs->total(),
                    'per_page' => $clubs->perPage(),
                    'current_page' => $clubs->currentPage(),
                    'last_page' => $clubs->lastPage(),
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch clubs: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => __('Failed to fetch clubs ') . $e->getMessage()
            ]);
        }
    }

    public function requestTokens(Request $request)
    {
        try {
            // Validate club exists and is active
            $club = Club::where('club_id', $request->club_id)
                ->where('status', 'Active')
                ->first();

            if (!$club) {
                return jsonResponse(false, [
                    'message' => __('Club not found')
                ]);
            }

            // Validate that payload is an array
            if (!is_array($request->tokens)) {
                return jsonResponse(false, [
                    'message' => __('Tokens payload must be an array')
                ]);
            }

            // Check if tokens are empty
            if (empty($request->tokens)) {
                return jsonResponse(false, [
                    'message' => __('Tokens must not be empty')
                ]);
            }

            $player = Auth::guard('player')->user();
            
            if (!$club->sell_tokens) {
                $token = Token::where('player_id', $player->player_id)
                    ->where('status', 'Requested')->first();

                if ($token) {
                    return jsonResponse(false, [
                        'message' => __("You're unable to create Tokens while you have other requests are outstanding")
                    ]);
                }
            }

            $tokens = [];
            $errors = [];
            $totalAmount = 0;


            foreach ($request->tokens as $index => $tokenData) {
                $validator = validator($tokenData, [
                    'token_type_id' => 'required'
                ]);

                if ($validator->fails()) {
                    $errors[] = [
                        'index' => $index,
                        'errors' => $validator->errors()
                    ];
                    continue;
                }

                $token = $club->tokenTypes()->where('id', $tokenData['token_type_id'])->first();
                $totalAmount = $totalAmount + $token->value;

                if (!$club->sell_tokens) {
                    // Generate unique token_id
                    $tokenId = $this->generateUniqueToken();

                    try {
                        $token = Token::create([
                            'token_id' => $tokenId,
                            'token_type_id' => $tokenData['token_type_id'],
                            'club_id' => $request->club_id,
                            'player_id' => $player->player_id,
                            'status' => 'Requested',
                            'requested_at' => Carbon::now()->format('d-m-Y H:i:s'),
                            'commercial_id' => null,
                            'purchased_at' => null,
                            'generated_at' => null,
                            'used_at' => null,
                            'redeemed_at' => null,
                            'invoiced_at' => null,
                        ]);
                        $tokens[] = $token;
                    } catch (\Exception $e) {
                        $errors[] = [
                            'index' => $index,
                            'message' => __('Failed to send token request') . ' ' . $e->getMessage(),
                        ];
                    }
                }
            }

            // If club sells tokens, create Stripe checkout session
            if ($club->sell_tokens) {
                try {
                    // Initialize Stripe
                    \Stripe\Stripe::setApiKey(config('services.stripe.secret'));

                    // Create order record
                    $order = TokenOrder::create([
                        'player_id' => $player->player_id,
                        'club_id' => $club->club_id,
                        'total_amount' => $totalAmount,
                        'status' => 'pending',
                        'tokens_data' => $request->tokens
                    ]);

                    // Create Stripe checkout session
                    $session = \Stripe\Checkout\Session::create([
                        'payment_method_types' => ['card'],
                        'line_items' => [[
                            'price_data' => [
                                'currency' => $club->currency,
                                'product_data' => [
                                    'name' => 'Token Purchase',
                                    'description' => __('Purchase of ') . count($request->tokens) . __(' tokens'),
                                ],
                                'unit_amount' => $totalAmount * 100,
                            ],
                            'quantity' => 1,
                        ]],
                        'mode' => 'payment',
                        'success_url' => route('stripe.success') . '?session_id={CHECKOUT_SESSION_ID}',
                        'cancel_url' => route('stripe.cancel'),
                        'metadata' => [
                            'order_id' => $order->id
                        ]
                    ]);

                    $order->update(['stripe_session_id' => $session->id]);

                    return jsonResponse(true, [
                        'payment_required' => true,
                        'checkout_url' => $session->url
                    ]);
                } catch (\Exception $e) {
                    Log::error('Stripe session creation failed: ' . $e->getMessage());
                    return jsonResponse(false, [
                        'message' => __('Failed to create payment session due to ') . $e->getMessage()
                    ]);
                }
            }



            // Send notification to club for free tokens
            if (!empty($tokens) && $club->fcm_token) {
                FCMService::sendNotification($club->fcm_token, __('New Token Request'), count($tokens) . __(' token request has been made by ') . $player->first_name . ' ' . $player->last_name);
            }

            Notification::create([
                'title' => 'New Token Request',
                'body' => count($tokens) . ' token request has been made by ' . $player->first_name . ' ' . $player->last_name,
                'club_id' => $club->club_id,
                'is_read' => false,
            ]);

            return jsonResponse(true, [
                'payment_required' => false,
                'message' => empty($errors) ? __('Token requests sent successfully') : __('Some token requests failed to send.'),
                'data' => [
                    'tokens' => $tokens,
                    'errors' => $errors
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to process token requests: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => __('Failed to process token requests ') . $e->getMessage()
            ]);
        }
    }

    public function index(Request $request)
    {
        Token::where('status', 'Requested')
            ->where('is_requested_for_redeem', false)
            ->where('requested_at', '<', Carbon::now()->subMinutes(30)->format('d-m-Y H:i:s'))
            ->delete();

        Token::where('status', 'Redeem-Requested')
            ->where('is_requested_for_redeem', true)
            ->where('redeemed_requested_at', '<', Carbon::now()->subMinutes(30)->format('d-m-Y H:i:s'))
            ->update(['is_requested_for_redeem' => false, 'status' => 'Generated']);


        try {
            $query = Token::query()
                ->where('player_id', Auth::guard('player')->user()->player_id);

            // Status filter
            if ($request->status) {
                $query->where('status', $request->status);
            }

            // Date range filter
            if ($request->from_date || $request->to_date) {
                $fromDate = $request->from_date ? Carbon::parse($request->from_date)->startOfDay() : null;
                $toDate = $request->to_date ? Carbon::parse($request->to_date)->endOfDay() : null;

                $query->where(function ($q) use ($fromDate, $toDate) {
                    if ($fromDate && $toDate) {
                        $q->whereBetween('created_at', [
                            $fromDate,
                            $toDate
                        ]);
                    } elseif ($fromDate) {
                        $q->where('created_at', '>=', $fromDate);
                    } elseif ($toDate) {
                        $q->where('created_at', '<=', $toDate);
                    }
                });
            }
            // Search by token_id
            if ($request->search) {
                $query->where('token_id', 'like', '%' . $request->search . '%');
            }

            $tokens = $query->with('club:club_id,club_name,sell_tokens,currency', 'tokenType:id,value,minutes')
                ->latest()
                ->paginate(10);

            return jsonResponse(true, [
                'data' => $tokens->items(),
                'pagination' => [
                    'total' => $tokens->total(),
                    'per_page' => $tokens->perPage(),
                    'current_page' => $tokens->currentPage(),
                    'last_page' => $tokens->lastPage(),
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch tokens: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => __('Failed to fetch tokens ') . $e->getMessage()
            ]);
        }
    }

    private function generateUniqueToken()
    {
        do {
            $letters = Str::random(3);
            $numbers = str_pad(mt_rand(0, 99999), 5, '0', STR_PAD_LEFT);
            $tokenId = strtoupper($letters . $numbers);
        } while (Token::where('token_id', $tokenId)->exists());

        return $tokenId;
    }

    public function redeem(Request $request)
    {
        $validator = Validator::make($request->all(), ([
            'token_id' => 'required|string'
        ]));

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        try {
            $token = Token::with('club:club_id,fcm_token,sell_tokens', 'player:player_id,first_name,last_name')->where('token_id', $request->token_id)->first();

            if (!$token) {
                return jsonResponse(false, [
                    'message' => __('Token not found.')
                ]);
            }

            $playerId = Auth::guard('player')->user()->player_id;

            // Check ownership
            if ($token->player_id !== $playerId) {
                return jsonResponse(false, [
                    'message' => __('Unauthorized. This token does not belong to you.')
                ]);
            }

            // Already used?
            if ($token->status === 'Used') {
                return jsonResponse(false, [
                    'message' => __('This token has already been used.')
                ]);
            }

            // Already redeemed?
            if ($token->status === 'Redeemed') {
                return jsonResponse(false, [
                    'message' => __('This token has already been redeemed.')
                ]);
            }

            // Token bought from another club?
            // $activeClubId = $request->club_id;
            // if ($token->club_id !== $activeClubId) {
            //     return jsonResponse(false, [
            //         'message' => 'Token was purchased from another club.'
            //     ]);
            // }

            // request for redeem token
            $token->status = 'Redeem-Requested';
            $token->is_requested_for_redeem = true;
            $token->redeemed_requested_at = Carbon::now()->format('d-m-Y H:i:s');
            $token->save();

            if ($token->club) {
                if ($token->club->fcm_token) {
                    FCMService::sendNotification($token->club->fcm_token, __('Token Redeem Request'), __('New token redeem request has been made by ') . $token->player->first_name . ' ' . $token->player->last_name);
                }
                Notification::create([
                    'title' => 'Token Redeem Request',
                    'body' => 'New token redeem request has been made by ' . $token->player->first_name . ' ' . $token->player->last_name,
                    'club_id' => $token->club->club_id,
                    'is_read' => false,
                ]);
            }

            return jsonResponse(true, [
                'message' => __('Token redeemed requested successfully'),
                'token' => $token
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to redeem token: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => __('Failed to redeem token ') . $e->getMessage()
            ]);
        }
    }


    public function useToken(Request $request)
    {
        try {
            $validator = validator($request->all(), [
                'token_id' => 'required|string|max:20',
            ]);

            if ($validator->fails()) {
                return validationError($validator->errors());
            }

            $token = Token::with('club:club_id,fcm_token,sell_tokens', 'player:player_id,first_name,last_name', 'tokenType:id,value,minutes')->where('token_id', $request->token_id)->first();

            if (!$token) {
                return jsonResponse(false, [
                    'message' => __('Token not found.')
                ]);
            }

            $player = Auth::guard('player')->user();

            // Check ownership
            if ($token->player_id !== $player->player_id) {
                return jsonResponse(false, [
                    'message' => __('Unauthorized. This token does not belong to you.')
                ]);
            }

            // Already used?
            if ($token->status === 'Used') {
                return jsonResponse(false, [
                    'message' => __('This token has already been used.')
                ]);
            }

            // Already redeemed?
            if ($token->status === 'Redeemed') {
                return jsonResponse(false, [
                    'message' => __('This token has already been redeemed.')
                ]);
            }
            // Update token status and used_at timestamp
            $token->update([
                'status' => 'Used',
                'used_at' => Carbon::now()->format('d-m-Y H:i:s'),
            ]);

            if ($token) {
                $player->credits = $token->tokenType?->minutes + $player->credits;
                $player->save();

                // Find existing credit record or create new one
                $playerCredit = PlayerCredit::where('player_id', $player->player_id)
                    ->where('club_id', $token->club_id)
                    ->first();

                if ($playerCredit) {
                    $playerCredit->credits = $playerCredit->credits + $token->tokenType?->minutes;
                    $playerCredit->save();
                } else {
                    PlayerCredit::create([
                        'player_id' => $player->player_id,
                        'club_id' => $token->club_id,
                        'credits' => $token->tokenType?->minutes ?? 0,
                    ]);
                }
            }

            return jsonResponse(true, [
                'message' => __('Token used successfully'),
                'token' => $token
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to use token: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => __('Failed to use token ') . $e->getMessage()
            ]);
        }
    }

    public function credits(Request $request)
    {
        try {
            $player = Auth::guard('player')->user();
            $query = PlayerCredit::with(['club:club_id,club_name,country,city,currency'])
                ->where('player_id', $player->player_id);

            // Search functionality
            if ($request->search) {
                $query->whereHas('club', function ($q) use ($request) {
                    $q->where('club_name', 'like', '%' . $request->search . '%')
                        ->orWhere('country', 'like', '%' . $request->search . '%')
                        ->orWhere('city', 'like', '%' . $request->search . '%');
                });
            }

            $credits = $query->paginate(10);

            return jsonResponse(true, [
                'credits' => $credits->items(),
                'pagination' => [
                    'total' => $credits->total(),
                    'per_page' => $credits->perPage(),
                    'current_page' => $credits->currentPage(),
                    'last_page' => $credits->lastPage(),
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to fetch credits: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => __('Failed to fetch credits ') . $e->getMessage()
            ]);
        }
    }
}
