<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use App\Models\Player;
use App\Models\PlayerSubscription;
use App\Models\SubscriptionPlan;
use App\Models\SubscriptionTransaction;
use App\Models\Token;
use App\Models\TokenOrder;
use App\Services\FCMService;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Stripe\Webhook;
use Stripe\Exception\SignatureVerificationException;

class StripeWebhookController extends Controller
{
    public function handleWebhook(Request $request)
    {
        \Stripe\Stripe::setApiKey(config('services.stripe.secret'));

        $payload = $request->getContent();
        $sig_header = $request->header('Stripe-Signature');
        $webhook_secret = config('services.stripe.webhook_secret');

        try {
            $event = Webhook::constructEvent(
                $payload,
                $sig_header,
                $webhook_secret
            );
        } catch (\UnexpectedValueException $e) {
            Log::error('Webhook error: Invalid payload - ' . $e->getMessage());
            return response()->json(['message' => 'Invalid payload'], 400);
        } catch (SignatureVerificationException $e) {
            Log::error('Webhook error: Invalid signature - ' . $e->getMessage());
            return response()->json(['message' => 'Invalid signature'], 400);
        } catch (\Exception $e) {
            Log::error('Webhook error: ' . $e->getMessage());
            return response()->json(['message' => 'Webhook error: ' . $e->getMessage()], 400);
        }

        // Handle different event types
        switch ($event->type) {
            case 'checkout.session.completed':
                return $this->handleCheckoutSessionCompleted($event->data->object);

            case 'invoice.payment_succeeded':
                return $this->handleInvoicePaymentSucceeded($event->data->object);

            case 'invoice.payment_failed':
                return $this->handleInvoicePaymentFailed($event->data->object);

            case 'customer.subscription.updated':
                return $this->handleSubscriptionUpdated($event->data->object);

            case 'customer.subscription.deleted':
                return $this->handleSubscriptionDeleted($event->data->object);

            default:
                return response()->json([
                    'status' => true,
                    'message' => 'Webhook received but not handled: ' . $event->type
                ]);
        }
    }

    /**
     * Handle checkout.session.completed event
     */
    private function handleCheckoutSessionCompleted($session)
    {
        // Check if this is a token order
        $order = TokenOrder::with('player:player_id,fcm_token')
            ->where('stripe_session_id', $session->id)
            ->first();

        if ($order) {
            return $this->handleTokenOrderCheckout($order);
        }

        // Check if this is a subscription checkout
        if (isset($session->metadata->player_id) && isset($session->metadata->plan_id)) {
            return $this->handleSubscriptionCheckout($session);
        }

        Log::error('Webhook error: Unknown checkout session type for session ' . $session->id);
        return response()->json(['message' => 'Unknown checkout session type'], 400);
    }

    /**
     * Handle token order checkout
     */
    private function handleTokenOrderCheckout($order)
    {
        if ($order->status !== 'pending') {
            Log::info('Order ' . $order->id . ' already processed');
            return response()->json(['message' => 'Order already processed']);
        }

        try {
            // Update order status
            $order->status = 'completed';
            $order->save();

            // Generate tokens
            foreach ($order->tokens_data as $tokenData) {
                $tokenId = $this->generateUniqueToken();

                Token::create([
                    'token_id' => $tokenId,
                    'token_type_id' => $tokenData['token_type_id'],
                    'club_id' => $order->club_id,
                    'player_id' => $order->player_id,
                    'status' => 'Generated',
                    'generated_at' => Carbon::now()->format('d-m-Y H:i:s'),
                    'commercial_id' => null,
                    'purchased_at' => Carbon::now()->format('d-m-Y H:i:s'),
                    'requested_at' => null,
                    'used_at' => null,
                    'redeemed_at' => null,
                    'invoiced_at' => null,
                ]);
            }

            if ($order->player?->fcm_token) {
                FCMService::sendNotification(
                    $order->player->fcm_token,
                    __('Token Order Completed'),
                    __('Your token order has been completed')
                );
            }

            Notification::create([
                'title' => __('Token Order Completed'),
                'body' => __('Your token order has been completed'),
                'player_id' => $order->player?->player_id,
                'is_read' => false,
            ]);

            return response()->json([
                'status' => true,
                'message' => __('Token order processed successfully')
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to process order: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Failed to process order: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle subscription checkout
     */
    private function handleSubscriptionCheckout($session)
    {
        try {
            $playerId = $session->metadata->player_id;
            $planId = $session->metadata->plan_id;

            $player = Player::find($playerId);
            $plan = SubscriptionPlan::find($planId);

            if (!$player || !$plan) {
                Log::error('Webhook error: Player or plan not found for session ' . $session->id);
                return response()->json(['message' => 'Player or plan not found'], 404);
            }

            // Check if subscription already exists
            $existingSubscription = PlayerSubscription::where('stripe_subscription_id', $session->subscription)
                ->first();

            if ($existingSubscription) {
                Log::info('Subscription ' . $session->subscription . ' already processed');
                return response()->json(['message' => 'Subscription already processed']);
            }

            // Retrieve subscription details from Stripe
            $subscription = \Stripe\Subscription::retrieve($session->subscription);

            // Create subscription record
            $playerSubscription = PlayerSubscription::create([
                'player_id' => $player->player_id,
                'subscription_plan_id' => $plan->id,
                'stripe_subscription_id' => $subscription->id,
                'stripe_customer_id' => $subscription->customer,
                'status' => $subscription->status,
                'current_period_starts_at' => Carbon::createFromTimestamp($subscription->current_period_start),
                'current_period_ends_at' => Carbon::createFromTimestamp($subscription->current_period_end),
            ]);

            // Create transaction record if there's an invoice
            if ($subscription->latest_invoice) {
                $invoice = \Stripe\Invoice::retrieve($subscription->latest_invoice);

                SubscriptionTransaction::create([
                    'player_id' => $player->player_id,
                    'player_subscription_id' => $playerSubscription->id,
                    'amount' => $plan->price,
                    'currency' => $plan->currency,
                    'stripe_invoice_id' => $invoice->id,
                    'status' => $invoice->status,
                    'paid_at' => $invoice->status === 'paid' ? Carbon::now() : null,
                ]);
            }

            // Update player subscription status
            $player->update(['subscribed' => true]);

            // Send notification
            if ($player->fcm_token) {
                FCMService::sendNotification(
                    $player->fcm_token,
                    __('Subscription Activated'),
                    __('Your subscription has been activated successfully')
                );
            }

            Notification::create([
                'title' => __('Subscription Activated'),
                'body' => __('Your subscription has been activated successfully'),
                'player_id' => $player->player_id,
                'is_read' => false,
            ]);

            return response()->json([
                'status' => true,
                'message' => __('Subscription processed successfully')
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to process subscription: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Failed to process subscription: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle invoice.payment_succeeded event
     */
    private function handleInvoicePaymentSucceeded($invoice)
    {
        try {
            // Check if this is a subscription invoice
            if (!isset($invoice->subscription)) {
                return response()->json([
                    'status' => true,
                    'message' => 'Non-subscription invoice ignored'
                ]);
            }

            $subscription = PlayerSubscription::where('stripe_subscription_id', $invoice->subscription)
                ->first();

            if (!$subscription) {
                Log::error('Webhook error: Subscription not found for invoice ' . $invoice->id);
                return response()->json(['message' => 'Subscription not found'], 404);
            }

            // Create transaction record
            SubscriptionTransaction::create([
                'player_id' => $subscription->player_id,
                'player_subscription_id' => $subscription->id,
                'amount' => $invoice->amount_paid / 100, // Convert from cents
                'currency' => strtoupper($invoice->currency),
                'stripe_invoice_id' => $invoice->id,
                'status' => $invoice->status,
                'paid_at' => Carbon::now(),
            ]);

            // Update subscription status if needed
            if ($subscription->status !== 'active') {
                $subscription->update([
                    'status' => 'active',
                    'current_period_starts_at' => Carbon::createFromTimestamp($invoice->period_start),
                    'current_period_ends_at' => Carbon::createFromTimestamp($invoice->period_end),
                ]);

                // Update player subscription status
                $player = Player::find($subscription->player_id);
                if ($player) {
                    $player->updateSubscriptionStatus();

                    // Send notification
                    if ($player->fcm_token) {
                        FCMService::sendNotification(
                            $player->fcm_token,
                            __('Subscription Renewed'),
                            __('Your subscription has been renewed successfully')
                        );
                    }

                    Notification::create([
                        'title' => __('Subscription Renewed'),
                        'body' => __('Your subscription has been renewed successfully'),
                        'player_id' => $player->player_id,
                        'is_read' => false,
                    ]);
                }
            }

            return response()->json([
                'status' => true,
                'message' => __('Invoice payment processed successfully')
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to process invoice payment: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Failed to process invoice payment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle invoice.payment_failed event
     */
    private function handleInvoicePaymentFailed($invoice)
    {
        try {
            // Check if this is a subscription invoice
            if (!isset($invoice->subscription)) {
                return response()->json([
                    'status' => true,
                    'message' => 'Non-subscription invoice ignored'
                ]);
            }

            $subscription = PlayerSubscription::where('stripe_subscription_id', $invoice->subscription)
                ->first();

            if (!$subscription) {
                Log::error('Webhook error: Subscription not found for invoice ' . $invoice->id);
                return response()->json(['message' => 'Subscription not found'], 404);
            }

            // Create transaction record
            SubscriptionTransaction::create([
                'player_id' => $subscription->player_id,
                'player_subscription_id' => $subscription->id,
                'amount' => $invoice->amount_due / 100, // Convert from cents
                'currency' => strtoupper($invoice->currency),
                'stripe_invoice_id' => $invoice->id,
                'status' => 'failed',
                'paid_at' => null,
            ]);

            // Send notification to player
            $player = Player::find($subscription->player_id);
            if ($player && $player->fcm_token) {
                FCMService::sendNotification(
                    $player->fcm_token,
                    __('Payment Failed'),
                    __('Your subscription payment has failed. Please update your payment method.')
                );
            }

            Notification::create([
                'title' => __('Payment Failed'),
                'body' => __('Your subscription payment has failed. Please update your payment method.'),
                'player_id' => $subscription->player_id,
                'is_read' => false,
            ]);

            return response()->json([
                'status' => true,
                'message' => __('Invoice payment failure processed')
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to process invoice payment failure: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Failed to process invoice payment failure: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle customer.subscription.updated event
     */
    private function handleSubscriptionUpdated($stripeSubscription)
    {
        try {
            $subscription = PlayerSubscription::where('stripe_subscription_id', $stripeSubscription->id)
                ->first();

            if (!$subscription) {
                Log::error('Webhook error: Subscription not found for ID ' . $stripeSubscription->id);
                return response()->json(['message' => 'Subscription not found'], 404);
            }

            // Update subscription details
            $subscription->update([
                'status' => $stripeSubscription->status,
                'current_period_starts_at' => Carbon::createFromTimestamp($stripeSubscription->current_period_start),
                'current_period_ends_at' => Carbon::createFromTimestamp($stripeSubscription->current_period_end),
                'canceled_at' => $stripeSubscription->canceled_at ? Carbon::createFromTimestamp($stripeSubscription->canceled_at) : null,
                'ends_at' => $stripeSubscription->cancel_at ? Carbon::createFromTimestamp($stripeSubscription->cancel_at) : null,
            ]);

            // Update player subscription status
            $player = Player::find($subscription->player_id);
            if ($player) {
                $player->updateSubscriptionStatus();

                // If subscription was canceled, notify the player
                if ($stripeSubscription->canceled_at && !$subscription->getOriginal('canceled_at')) {
                    if ($player->fcm_token) {
                        FCMService::sendNotification(
                            $player->fcm_token,
                            __('Subscription Canceled'),
                            __('Your subscription has been canceled and will end on ') .
                            $subscription->ends_at->format('Y-m-d')
                        );
                    }

                    Notification::create([
                        'title' => __('Subscription Canceled'),
                        'body' => __('Your subscription has been canceled and will end on ') .
                                $subscription->ends_at->format('Y-m-d'),
                        'player_id' => $player->player_id,
                        'is_read' => false,
                    ]);
                }
            }

            return response()->json([
                'status' => true,
                'message' => __('Subscription updated successfully')
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update subscription: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Failed to update subscription: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle customer.subscription.deleted event
     */
    private function handleSubscriptionDeleted($stripeSubscription)
    {
        try {
            $subscription = PlayerSubscription::where('stripe_subscription_id', $stripeSubscription->id)
                ->first();

            if (!$subscription) {
                Log::error('Webhook error: Subscription not found for ID ' . $stripeSubscription->id);
                return response()->json(['message' => 'Subscription not found'], 404);
            }

            // Update subscription details
            $subscription->update([
                'status' => 'canceled',
                'canceled_at' => Carbon::now(),
                'ends_at' => Carbon::now(),
            ]);

            // Update player subscription status
            $player = Player::find($subscription->player_id);
            if ($player) {
                $player->updateSubscriptionStatus();

                // Notify the player
                if ($player->fcm_token) {
                    FCMService::sendNotification(
                        $player->fcm_token,
                        __('Subscription Ended'),
                        __('Your subscription has ended')
                    );
                }

                Notification::create([
                    'title' => __('Subscription Ended'),
                    'body' => __('Your subscription has ended'),
                    'player_id' => $player->player_id,
                    'is_read' => false,
                ]);
            }

            return response()->json([
                'status' => true,
                'message' => __('Subscription deleted successfully')
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to delete subscription: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Failed to delete subscription: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate a unique token ID
     */
    private function generateUniqueToken()
    {
        do {
            $letters = Str::random(3);
            $numbers = str_pad(mt_rand(0, 99999), 5, '0', STR_PAD_LEFT);
            $tokenId = strtoupper($letters . $numbers);
        } while (Token::where('token_id', $tokenId)->exists());

        return $tokenId;
    }

    public function success(Request $request)
    {
        try {
            // Get the session_id from the query parameters
            $sessionId = $request->query('session_id');

            if (!$sessionId) {
                return jsonResponse(false, ['message' => __('Invalid request')]);
            }

            // Find the order
            $order = TokenOrder::where('stripe_session_id', $sessionId)->first();

            if (!$order) {
                return jsonResponse(false, ['message' => __('Order not found')]);
            }

            // Check order status
            switch ($order->status) {
                case 'completed':
                    return jsonResponse(true, ['message' => __('Payment successful'), 'payment_status' => 'completed']);
                case 'pending':
                    // Payment is still processing
                    return jsonResponse(true, [
                        'message' => __('Payment is being processed'),
                        'payment_status' => 'pending'
                    ]);
                case 'failed':
                    return jsonResponse(false, ['message' => __('Payment failed'), 'payment_status' => 'failed']);
                default:
                    return jsonResponse(false, ['message' => __('Invalid order status'), 'payment_status' => 'unknown']);
            }
        } catch (\Exception $e) {
            Log::error('Payment success check failed: ' . $e->getMessage());
            return jsonResponse(false, ['message' => __('Failed to check payment status')]);
        }
    }

    public function cancel()
    {
        return jsonResponse(false, ['message' => __('Payment cancelled')]);
    }
}
