<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckPlayerStatus
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if player is authenticated
        if (Auth::guard('player')->check()) {
            $player = Auth::guard('player')->user();

            // If player status is Suspended, force logout
            if ($player->status === 'Suspended') {
                Auth::guard('player')->logout();

                return response()->json([
                    'success' => false,
                    'message' => __('Your account has been suspended due to multiple token rejections. Please contact support for assistance.', [], $player->language)
                ], 401);
            }
        }

        return $next($request);
    }
}
