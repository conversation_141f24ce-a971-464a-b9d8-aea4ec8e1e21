<?php

namespace App\Livewire\AccountDeletion;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use App\Mail\AccountDeletionRequest;
use Livewire\Component;
use Livewire\Attributes\Layout;

#[Layout('layouts.legal', ['title' => 'Account Deletion'])]
class AccountDeletionForm extends Component
{
    public $name;
    public $email;
    public $reason;
    public $additional_feedback;

    // Deletion reasons
    public $reasons = [
        'not_useful' => 'App is not useful for me',
        'found_alternative' => 'Found a better alternative',
        'too_expensive' => 'Subscription is too expensive',
        'technical_issues' => 'Technical issues or bugs',
        'privacy_concerns' => 'Privacy concerns',
        'temporary_break' => 'Taking a temporary break',
        'other' => 'Other reason'
    ];

    protected $rules = [
        'name' => 'required|string|max:100',
        'email' => 'required|email|max:100',
        'reason' => 'required|string',
        'additional_feedback' => 'nullable|string|max:1000',
    ];

    public function mount()
    {
        // Pre-fill user information if authenticated
        if (Auth::guard('player')->check()) {
            $player = Auth::guard('player')->user();
            $this->name = $player->first_name . ' ' . $player->last_name;
            $this->email = $player->email;
        }
    }

    public function submitDeletionRequest()
    {
        $this->validate();

        try {
            // Get admin email from environment
            $adminEmail = env('ADMIN_EMAIL', '<EMAIL>');

            // Send email to admin
            Mail::to($adminEmail)->send(new AccountDeletionRequest(
                $this->name,
                $this->email,
                $this->reason,
                $this->reasons[$this->reason] ?? $this->reason,
                $this->additional_feedback
            ));

            // Reset form
            $this->reset(['reason', 'additional_feedback', 'name', 'email']);

            // Show success message
            session()->flash('message', 'Your account deletion request has been submitted. Our team will process it shortly.');

        } catch (\Exception $e) {
            Log::error('Failed to send account deletion request: ' . $e->getMessage());
            session()->flash('error', 'Failed to submit your request. Please try again later.');
        }
    }

    public function render()
    {
        return view('livewire.account-deletion.account-deletion-form');
    }
}
