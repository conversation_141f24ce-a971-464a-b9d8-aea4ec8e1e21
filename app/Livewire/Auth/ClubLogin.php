<?php

namespace App\Livewire\Auth;

use App\Models\Club;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Rule;
use Livewire\Component;

class ClubLogin extends Component
{
    #[Rule('required|email')]
    public string $email = '';

    #[Rule('required')]
    public string $password = '';

    public bool $remember = false;

    public function login()
    {
        $this->validate();

        // Find club by email
        $club = Club::where('email', $this->email)->first();

        if (!$club) {
            $this->addError('email', __('These credentials do not match our records.'));
            return;
        }

        // Check if club status is active
        if ($club->status !== 'Active') {
            $this->addError('email', __('Your club account is not active. Please contact support.'));
            return;
        }

        // Verify password
        if (!Hash::check($this->password, $club->password)) {
            $this->addError('email', __('These credentials do not match our records.'));
            return;
        }

        // Login the club user
        Auth::guard('club_web')->login($club, $this->remember);

        session()->regenerate();

        // Redirect to club balance details
        return $this->redirect(route('club.balance-details'), navigate: true);
    }

    #[Layout('components.layouts.auth')]
    public function render()
    {
        return view('livewire.auth.club-login');
    }
}
