<?php

namespace App\Livewire\Auth;

use App\Models\Club;
use Illuminate\Auth\Events\Lockout;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Validate;
use Livewire\Component;

#[Layout('components.layouts.auth')]
class Login extends Component
{
    #[Validate('required|string|email')]
    public string $email = '';

    #[Validate('required|string')]
    public string $password = '';

    public bool $remember = false;

    public function mount(){
        if(Auth::guard('admin')->check()){
            $this->redirect(route('dashboard'));
        } elseif(Auth::guard('club_web')->check()){
            $this->redirect(route('club.balance-details'));
        }
    }

    /**
     * Handle an incoming authentication request.
     */
    public function login(): void
    {
        $this->validate();

        $this->ensureIsNotRateLimited();

        if (! Auth::guard('admin')->attempt(['email' => $this->email, 'password' => $this->password], $this->remember)) {
            // Find club by email
            $club = Club::where('email', $this->email)->first();

            if (!$club) {
                throw ValidationException::withMessages([
                    'email' => __('auth.failed'),
                ]);
                return;
            }

            // Check if club status is active
            if ($club->status !== 'Active') {
                throw ValidationException::withMessages([
                    'email' => __('auth.failed'),
                ]);
                return;
            }

            // Verify password
            if (!Hash::check($this->password, $club->password)) {
                throw ValidationException::withMessages([
                    'email' => __('auth.failed'),
                ]);
                return;
            }

            // Login the club user
            Auth::guard('club_web')->login($club, $this->remember);

            if (Auth::guard('admin')->check()) {
                Auth::guard('admin')->logout();
            }

            session()->regenerate();

            // Redirect to club balance details
            $this->redirectIntended(route('club.balance-details'));

            RateLimiter::hit($this->throttleKey());

            throw ValidationException::withMessages([
                'email' => __('auth.failed'),
            ]);
        }

        if (Auth::guard('club_web')->check()) {
            Auth::guard('club_web')->logout();
        }

        RateLimiter::clear($this->throttleKey());
        Session::regenerate();

        $this->redirectIntended(default: route('dashboard'));
    }

    /**
     * Ensure the authentication request is not rate limited.
     */
    protected function ensureIsNotRateLimited(): void
    {
        if (! RateLimiter::tooManyAttempts($this->throttleKey(), 5)) {
            return;
        }

        event(new Lockout(request()));

        $seconds = RateLimiter::availableIn($this->throttleKey());

        throw ValidationException::withMessages([
            'email' => __('auth.throttle', [
                'seconds' => $seconds,
                'minutes' => ceil($seconds / 60),
            ]),
        ]);
    }

    /**
     * Get the authentication rate limiting throttle key.
     */
    protected function throttleKey(): string
    {
        return Str::transliterate(Str::lower($this->email) . '|' . request()->ip());
    }
}
