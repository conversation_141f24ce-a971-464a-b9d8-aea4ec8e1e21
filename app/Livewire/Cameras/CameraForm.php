<?php

namespace App\Livewire\Cameras;

use App\Models\Camera;
use App\Models\Club;
use Livewire\Component;

class CameraForm extends Component
{
    public $camera_id;
    public $club_id;
    public $court_number;
    public $ip;
    public $port;

    public $isEdit = false;
    public $cameraId = null;

    // For searchable club select
    public $search = '';
    public $selectedClub = null;
    public $clubs = [];
    public $showDropdown = false;

    protected $rules = [
        'club_id' => 'required|exists:clubs,club_id',
        'court_number' => 'required|integer|min:1',
        'ip' => 'required|ip',
        'port' => 'required|integer|min:1|max:65535',
    ];

    public function mount($id = null)
    {
        if ($id) {
            $this->isEdit = true;
            $this->cameraId = $id;
            $camera = Camera::with('club')->findOrFail($id);

            $this->camera_id = $camera->camera_id;
            $this->club_id = $camera->club_id;
            $this->court_number = $camera->court_number;
            $this->ip = $camera->ip;
            $this->port = $camera->port;

            // Set the selected club
            $this->selectedClub = $camera->club;
            $this->search = $camera->club?->club_name;
        }

        // Initial clubs load
        $this->loadClubs();
    }

    public function loadClubs()
    {
        $query = Club::query()
            ->where('status', 'Active')
            ->orderBy('club_name');

        if ($this->search) {
            $query->where(function ($q) {
                $q->where('club_name', 'like', '%' . $this->search . '%')
                    ->orWhere('club_id', 'like', '%' . $this->search . '%');
            });
        }

        $this->clubs = $query->limit(10)->get();
    }

    public function updatedSearch()
    {
        $this->loadClubs();
        $this->showDropdown = true;
    }

    public function selectClub($clubId, $clubName)
    {
        $this->club_id = $clubId;
        $this->selectedClub = Club::find($clubId);
        $this->search = $clubName;
        $this->showDropdown = false;
    }

    public function save()
    {
        $this->validate();

        if ($this->isEdit) {
            $camera = Camera::findOrFail($this->cameraId);

            $camera->club_id = $this->club_id;
            $camera->court_number = $this->court_number;
            $camera->ip = $this->ip;
            $camera->port = $this->port;

            $camera->save();

            session()->flash('message', 'Camera updated successfully.');
        } else {
            // Generate a unique camera ID
            $camera_id = Camera::generateCameraId();

            $camera = new Camera();
            $camera->camera_id = $camera_id;
            $camera->club_id = $this->club_id;
            $camera->court_number = $this->court_number;
            $camera->ip = $this->ip;
            $camera->port = $this->port;

            $camera->save();

            session()->flash('message', 'Camera created successfully.');
        }

        return redirect()->route('cameras.index');
    }

    public function render()
    {
        return view('livewire.cameras.camera-form');
    }
}
