<?php

namespace App\Livewire\Cameras;

use App\Models\Camera;
use App\Models\Club;
use Livewire\Component;
use Livewire\WithPagination;

class CamerasManagement extends Component
{
    use WithPagination;

    public $search = '';
    public $clubFilter = '';
    public $deleteId;
    
    protected $queryString = [
        'search' => ['except' => ''],
        'clubFilter' => ['except' => ''],
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingClubFilter()
    {
        $this->resetPage();
    }

    public function render()
    {
        $query = Camera::query()->with('club');

        if ($this->search) {
            $query->where(function ($q) {
                $q->where('camera_id', 'like', '%' . $this->search . '%')
                  ->orWhere('court_number', 'like', '%' . $this->search . '%')
                  ->orWhere('ip', 'like', '%' . $this->search . '%')
                  ->orWhereHas('club', function ($clubQuery) {
                      $clubQuery->where('club_name', 'like', '%' . $this->search . '%');
                  });
            });
        }

        if ($this->clubFilter) {
            $query->where('club_id', $this->clubFilter);
        }

        $cameras = $query->latest()->paginate(10);
        $clubs = Club::orderBy('club_name')->get();

        return view('livewire.cameras.cameras-management', [
            'cameras' => $cameras,
            'clubs' => $clubs
        ]);
    }

    public function confirmDelete($cameraId)
    {
        $this->deleteId = $cameraId;
        $this->modal('confirm-camera-deletion')->show();
    }

    public function deleteCamera()
    {
        $camera = Camera::findOrFail($this->deleteId);
        $camera->delete();

        $this->modal('confirm-camera-deletion')->close();
    }
}
