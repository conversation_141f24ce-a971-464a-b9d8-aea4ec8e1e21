<?php

namespace App\Livewire\Clubs;

use App\Models\Club;
use App\Models\Token;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class ClubBalanceDetails extends Component
{
    use WithPagination;

    public $clubId;
    public $club;
    public $startDate;
    public $endDate;
    public $search = '';
    public $statusFilter = '';

    protected $queryString = [
        'search' => ['except' => ''],
        'statusFilter' => ['except' => ''],
        'startDate' => ['except' => ''],
        'endDate' => ['except' => ''],
    ];

    public function mount($clubId = null)
    {
        // If clubId is provided (admin view), load that club
        if ($clubId) {
            $this->clubId = $clubId;
            $this->club = Club::findOrFail($clubId);
            if(!Auth::guard('admin')->check()) {
                Auth::guard('club_web')->logout();
                return redirect()->route('login');
            }
        } else {
            // Club user viewing their own balance
            $this->club = Auth::guard('club_web')->user();
            $this->clubId = $this->club->club_id;
        }

        // Set default date range (last 30 days)
        // $this->endDate = Carbon::now()->format('Y-m-d');
        // $this->startDate = Carbon::now()->subDays(30)->format('Y-m-d');
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatusFilter()
    {
        $this->resetPage();
    }

    public function updatingStartDate()
    {
        $this->resetPage();
    }

    public function updatingEndDate()
    {
        $this->resetPage();
    }

    public function applyDateFilter()
    {
        $this->resetPage();
        $this->dispatch('notify', variant: 'success', title: 'Filter Applied', message: 'Date range filter has been applied successfully.');
    }

    public function clearFilters()
    {
        $this->search = '';
        $this->statusFilter = '';
        // $this->startDate = Carbon::now()->subDays(30)->format('Y-m-d');
        // $this->endDate = Carbon::now()->format('Y-m-d');
        $this->startDate = '';
        $this->endDate = '';
        $this->resetPage();
        $this->dispatch('notify', variant: 'info', title: 'Filters Cleared', message: 'All filters have been cleared.');
    }

    public function getTokensProperty()
    {
        $query = Token::with(['tokenType', 'player', 'invoice'])
            ->where('club_id', $this->clubId);

        // Apply date range filter
        if ($this->startDate && $this->endDate) {
            $startDate = Carbon::parse($this->startDate)->startOfDay()->format('Y-m-d H:i:s');
            $endDate = Carbon::parse($this->endDate)->endOfDay()->format('Y-m-d H:i:s');

            $query->where(function ($q) use ($startDate, $endDate) {
                $q->where(function ($subQ) use ($startDate, $endDate) {
                    $subQ->whereNotNull('purchased_at')
                         ->whereBetween('purchased_at', [$startDate, $endDate]);
                })
                ->orWhere(function ($subQ) use ($startDate, $endDate) {
                    $subQ->whereNotNull('redeemed_at')
                         ->whereBetween('redeemed_at', [$startDate, $endDate]);
                })
                ->orWhere(function ($subQ) use ($startDate, $endDate) {
                    $subQ->whereNotNull('paid_at')
                         ->whereBetween('paid_at', [$startDate, $endDate]);
                });
            });
        }

        // Apply search filter
        if ($this->search) {
            $query->where(function ($q) {
                $q->where('token_id', 'like', '%' . $this->search . '%')
                  ->orWhere('invoice_id', 'like', '%' . $this->search . '%')
                  ->orWhere('status', 'like', '%' . $this->search . '%')
                  ->orWhereHas('player', function ($playerQuery) {
                      $playerQuery->where('first_name', 'like', '%' . $this->search . '%')
                                  ->orWhere('last_name', 'like', '%' . $this->search . '%');
                  });
            });
        }

        // Apply status filter
        // if ($this->statusFilter) {
        //     $query->where('status', $this->statusFilter);
        // }

        // Only show tokens that have been purchased, redeemed, or paid
        $query->where(function ($q) {
            $q->whereNotNull('purchased_at')
              ->orWhereNotNull('redeemed_at')
              ->orWhereNotNull('paid_at');
        });

        return $query->orderBy('purchased_at', 'desc')
                    ->orderBy('created_at', 'desc')
                    ->paginate(15);
    }

    public function render()
    {
        return view('livewire.clubs.club-balance-details', [
            'tokens' => $this->tokens,
        ]);
    }
}
