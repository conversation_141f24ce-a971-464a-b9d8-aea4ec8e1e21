<?php

namespace App\Livewire\Clubs;

use App\Models\Club;
use App\Models\Commercial;
use App\Models\Stat;
use App\Models\TokenType;
use App\Services\CurrencyService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Livewire\Component;

class ClubForm extends Component
{
    public $club_id;
    public $club_name;
    public $email;
    public $phone;
    public $country_code;
    public $contact;
    public $postal_code;
    public $country;
    public $city;
    public $tax_id;
    public $commission = 0;
    public $commercial_commission = 1.5;
    public $commercial_id;
    public $sell_tokens = false;
    public $status = 'Active';
    public $suspension_reason = '';
    public $currency = 'USD';
    public $password;
    public $password_confirmation;

    public $isEdit = false;
    public $clubId = null;

    // For searchable country select
    public $countrySearch = '';
    public $selectedCountry = null;
    public $countries = [];
    public $showCountryDropdown = false;

    // For searchable city select
    public $citySearch = '';
    public $selectedCity = null;
    public $cities = [];
    public $showCityDropdown = false;

    // For searchable commercial select
    public $commercialSearch = '';
    public $selectedCommercial = null;
    public $commercials = [];
    public $showCommercialDropdown = false;

    protected $rules = [
        'club_name' => 'required|string|max:255',
        'email' => 'required|email|max:255',
        'phone' => 'nullable|string|max:20',
        'country_code' => 'nullable|string|max:5',
        'contact' => 'nullable|string|max:255',
        'postal_code' => 'nullable|string|max:20',
        'country' => 'nullable|string|max:100',
        'city' => 'nullable|string|max:100',
        'tax_id' => 'nullable|string|max:50',
        'commission' => 'nullable|numeric|min:0|max:100',
        'commercial_commission' => 'nullable|numeric|min:1|max:3',
        'commercial_id' => 'nullable|string|max:50',
        'sell_tokens' => 'boolean',
        'status' => 'required|in:Active,Suspended',
        'suspension_reason' => 'nullable|string|max:254',
        'currency' => 'nullable|string|size:3',
    ];

    public function mount($id = null)
    {
        if ($id) {
            $this->isEdit = true;
            $this->clubId = $id;
            $club = Club::findOrFail($id);

            $this->club_id = $club->club_id;
            $this->club_name = $club->club_name;
            $this->email = $club->email;
            $this->phone = $club->phone;
            $this->country_code = $club->country_code;
            $this->contact = $club->contact;
            $this->postal_code = $club->postal_code;
            $this->country = $club->country;
            $this->city = $club->city;
            $this->tax_id = $club->tax_id;
            $this->commission = $club->commission;
            $this->commercial_commission = $club->commercial_commission;
            $this->commercial_id = $club->commercial_id;
            $this->sell_tokens = $club->sell_tokens;
            $this->status = $club->status;
            $this->suspension_reason = $club->suspension_reason ?? '';
            $this->currency = $club->currency;

            // Set the search fields to match the current values
            $this->countrySearch = $club->country;
            $this->citySearch = $club->city;

            // Set commercial search if a commercial is assigned
            if ($club->commercial_id) {
                $commercial = Commercial::find($club->commercial_id);
                if ($commercial) {
                    $this->commercialSearch = $commercial->name;
                    $this->selectedCommercial = $commercial;
                }
            }

            // Add password validation only for new clubs
            $this->rules['password'] = 'nullable|min:6|confirmed';
        } else {
            // Add password validation for new clubs
            $this->rules['password'] = 'required|min:6|confirmed';
            $this->rules['email'] = 'required|email|max:255|unique:clubs,email';
        }

        // Initial load of countries and commercials
        $this->loadCountries();
        $this->loadCommercials();
    }

    /**
     * Load countries based on search term
     */
    public function loadCountries()
    {
        $query = DB::table('countries')
            ->select(['id', 'name'])
            ->orderBy('name');

        if ($this->countrySearch) {
            $query->where('name', 'like', '%' . $this->countrySearch . '%');
        }

        $this->countries = $query->limit(10)->get();
    }

    /**
     * Load cities based on search term and selected country
     */
    public function loadCities()
    {
        $query = DB::table('cities')
            ->select(['id', 'name'])
            ->orderBy('name');


        if ($this->selectedCountry) {
            $query->where('country_id', $this->selectedCountry->id);
        }
        if ($this->citySearch) {
            $query->where('name', 'like', '%' . $this->citySearch . '%');
        }

        $this->cities = $query->limit(10)->get();
    }

    /**
     * Update countries when search term changes
     */
    public function updatedCountrySearch()
    {
        $this->loadCountries();
        $this->showCountryDropdown = true;
    }

    /**
     * Update cities when search term changes
     */
    public function updatedCitySearch()
    {
        $this->loadCities();
        $this->showCityDropdown = true;
    }

    /**
     * Select a country from the dropdown
     */
    public function selectCountry($id, $name)
    {
        $this->selectedCountry = (object) ['id' => $id, 'name' => $name];
        $this->country = $name;
        $this->countrySearch = $name;
        $this->showCountryDropdown = false;

        // Reset city when country changes
        $this->city = '';
        $this->citySearch = '';
        $this->cities = [];

        // Load cities for the selected country
        $this->loadCities();
    }

    /**
     * Select a city from the dropdown
     */
    public function selectCity($id, $name)
    {
        $this->selectedCity = (object) ['id' => $id, 'name' => $name];
        $this->city = $name;
        $this->citySearch = $name;
        $this->showCityDropdown = false;
    }

    public function save()
    {
        $this->validate();

        if ($this->isEdit) {
            $club = Club::findOrFail($this->clubId);

            // Check if email is changed and validate uniqueness
            if ($club->email !== $this->email) {
                $this->validate([
                    'email' => 'required|email|max:255|unique:clubs,email'
                ]);
            }
            if ($this->password) {
                $this->validate([
                    'password' => 'required|min:6|confirmed'
                ]);
            }

            $club->club_name = $this->club_name;
            $club->email = $this->email;
            $club->phone = $this->phone;
            $club->country_code = $this->country_code;
            $club->contact = $this->contact;
            $club->postal_code = $this->postal_code;
            $club->country = $this->country ?? $this->countrySearch;
            $club->city = $this->city ?? $this->citySearch;
            $club->tax_id = $this->tax_id;
            $club->commission = $this->commission;
            $club->commercial_commission = $this->commercial_commission;
            $club->commercial_id = $this->commercial_id;
            $club->sell_tokens = $this->sell_tokens;
            $club->status = $this->status;
            $club->suspension_reason = $this->status === 'Suspended' ? $this->suspension_reason : null;
            $club->currency = $this->currency;

            if ($this->password) {
                $club->password = Hash::make($this->password);
            }

            $club->save();

            session()->flash('message', 'Club updated successfully.');
        } else {
            $this->validate([
                'email' => 'required|email|max:255|unique:clubs,email'
            ]);
            // Generate a unique club ID
            $club_id = Club::generateClubId();

            $club = new Club();
            $club->club_id = $club_id;
            $club->club_name = $this->club_name;
            $club->email = $this->email;
            $club->phone = $this->phone;
            $club->country_code = $this->country_code;
            $club->contact = $this->contact;
            $club->postal_code = $this->postal_code;
            $club->country = $this->country ?? $this->countrySearch;
            $club->city = $this->city ?? $this->citySearch;
            $club->tax_id = $this->tax_id;
            $club->commission = $this->commission;
            $club->commercial_commission = $this->commercial_commission;
            $club->commercial_id = $this->commercial_id;
            $club->sell_tokens = $this->sell_tokens;
            $club->status = $this->status;
            $club->suspension_reason = $this->status === 'Suspended' ? $this->suspension_reason : null;
            $club->currency = $this->currency;
            $club->password = Hash::make($this->password);
            $club->started = now();

            $club->save();

            // Create currency service
            $currencyService = new CurrencyService();

            // Convert values to EUR
            $value1 = 0.75;
            $value2 = 1.25;
            $valueInEur1 = $currencyService->convertToEur($value1, $this->currency) ?? $value1;
            $valueInEur2 = $currencyService->convertToEur($value2, $this->currency) ?? $value2;

            // create 2 default token types
            TokenType::insert(
                [
                    [
                        'club_id' => $club_id,
                        'value' => $value1,
                        'value_in_eur' => $valueInEur1,
                        'minutes' => 30,
                    ],
                    [
                        'club_id' => $club_id,
                        'value' => $value2,
                        'value_in_eur' => $valueInEur2,
                        'minutes' => 60,
                    ]
                ]
            );

            Stat::incrementClubs();

            session()->flash('message', 'Club created successfully.');
        }

        return redirect()->route('clubs.index');
    }

    /**
     * Load commercials based on search term
     */
    public function loadCommercials()
    {
        $query = Commercial::query()
            ->where('status', 1) // Only active commercials
            ->orderBy('name');

        if ($this->commercialSearch) {
            $query->where(function ($q) {
                $q->where('name', 'like', '%' . $this->commercialSearch . '%')
                    ->orWhere('commercial_id', 'like', '%' . $this->commercialSearch . '%');
            });
        }

        $this->commercials = $query->limit(10)->get();
    }

    /**
     * Update commercials when search term changes
     */
    public function updatedCommercialSearch()
    {
        $this->loadCommercials();
        $this->showCommercialDropdown = true;
    }

    /**
     * Select a commercial from the dropdown
     */
    public function selectCommercial($commercialId, $name)
    {
        $this->selectedCommercial = Commercial::find($commercialId);
        $this->commercial_id = $commercialId;
        $this->commercialSearch = $name;
        $this->showCommercialDropdown = false;
    }

    /**
     * Close dropdowns when clicking outside
     */
    public function closeDropdowns()
    {
        $this->showCountryDropdown = false;
        $this->showCityDropdown = false;
        $this->showCommercialDropdown = false;
    }

    public function render()
    {
        return view('livewire.clubs.club-form');
    }
}
