<?php

namespace App\Livewire\Clubs;

use App\Models\Club;
use App\Models\Stat;
use Livewire\Component;
use Livewire\WithPagination;

class ClubsManagement extends Component
{
    use WithPagination;

    public $search = '';
    public $status = '';
    public $deleteId;

    protected $queryString = [
        'search' => ['except' => ''],
        'status' => ['except' => ''],
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatus()
    {
        $this->resetPage();
    }

    public function render()
    {
        $query = Club::query();

        if ($this->search) {
            $query->where(function ($q) {
                $q->where('club_name', 'like', '%' . $this->search . '%')
                    ->orWhere('club_id', 'like', '%' . $this->search . '%')
                    ->orWhere('email', 'like', '%' . $this->search . '%')
                    ->orWhere('phone', 'like', '%' . $this->search . '%');
            });
        }

        if ($this->status) {
            $query->where('status', $this->status);
        }

        $clubs = $query->latest()->paginate(10);

        return view('livewire.clubs.clubs-management', [
            'clubs' => $clubs
        ]);
    }

    public function confirmDelete($clubId)
    {
        $this->deleteId = $clubId;
        $this->modal('confirm-club-deletion')->show();
    }

    public function deleteClub()
    {
        $club = Club::findOrFail($this->deleteId);
        $club->delete();

        Stat::decrementClubs();
        
        $this->modal('confirm-club-deletion')->close();
    }
}
