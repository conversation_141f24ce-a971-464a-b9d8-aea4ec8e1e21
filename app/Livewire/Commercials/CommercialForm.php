<?php

namespace App\Livewire\Commercials;

use App\Models\Commercial;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Livewire\Component;

class CommercialForm extends Component
{
    public $commercial_id;
    public $name;
    public $email;
    public $password;
    public $password_confirmation;

    public $phone;
    public $tax_id;
    public $postal_code;
    public $country;
    public $status = 1;

    public $isEdit = false;
    public $commercialId = null;

    // For searchable country select
    public $countrySearch = '';
    public $selectedCountry = null;
    public $countries = [];
    public $showCountryDropdown = false;

    protected $rules = [
        'name' => 'required|string|max:50',
        'email' => 'required|email|max:30',
        'phone' => 'nullable|string|max:15',
        'tax_id' => 'nullable|string|max:12',
        'postal_code' => 'nullable|string|max:10',
        'country' => 'nullable|string|max:50',
        'status' => 'required|integer|in:0,1',
    ];

    public function mount($id = null)
    {
        if ($id) {
            $this->isEdit = true;
            $this->commercialId = $id;
            $commercial = Commercial::findOrFail($id);

            $this->commercial_id = $commercial->commercial_id;
            $this->name = $commercial->name;
            $this->email = $commercial->email;
            $this->phone = $commercial->phone;
            $this->tax_id = $commercial->tax_id;
            $this->postal_code = $commercial->postal_code;
            $this->country = $commercial->country;
            $this->status = $commercial->status;

            // Set the search fields to match the current values
            $this->countrySearch = $commercial->country;

            // Update email validation rule for edit
            $this->rules['email'] = 'required|email|max:30|unique:commercials,email,' . $this->commercial_id . ',commercial_id';
        } else {
            // Add email validation rule for new commercials
            $this->rules['email'] = 'required|email|max:30|unique:commercials,email';
        }

        // Initial load of countries
        $this->loadCountries();
    }

    /**
     * Load countries based on search term
     */
    public function loadCountries()
    {
        $query = DB::table('countries')
            ->select(['id', 'name'])
            ->orderBy('name');

        if ($this->countrySearch) {
            $query->where('name', 'like', '%' . $this->countrySearch . '%');
        }

        $this->countries = $query->limit(10)->get();
    }

    public function updatedCountrySearch()
    {
        $this->loadCountries();
        $this->showCountryDropdown = true;
    }

    public function selectCountry($countryName)
    {
        $this->country = $countryName;
        $this->countrySearch = $countryName;
        $this->showCountryDropdown = false;
    }

    public function save()
    {
        $this->validate();

        if ($this->isEdit) {
            $this->validate([
                'email' => 'required|email|max:30|unique:commercials,email,' . $this->commercial_id . ',commercial_id',
                'password' => 'nullable|min:6|confirmed'
            ]);

            $commercial = Commercial::findOrFail($this->commercial_id);

            $commercial->name = $this->name;
            $commercial->email = $this->email;
            $commercial->phone = $this->phone;
            $commercial->tax_id = $this->tax_id;
            $commercial->postal_code = $this->postal_code;
            $commercial->country = $this->country ?? $this->countrySearch;
            $commercial->status = $this->status;

            if ($this->password) {

                $commercial->password = Hash::make($this->password);
            }

            $commercial->save();

            session()->flash('message', 'Commercial updated successfully.');
        } else {

            $this->validate([
                'email' => 'required|email|max:30|unique:commercials,email',
                'password' => 'required|min:6|confirmed'
            ]);
            // Generate a unique commercial ID
            $commercial_id = Commercial::generateCommercialId();

            $commercial = new Commercial();
            $commercial->commercial_id = $commercial_id;
            $commercial->name = $this->name;
            $commercial->email = $this->email;
            $commercial->password = Hash::make($this->password);
            $commercial->phone = $this->phone;
            $commercial->tax_id = $this->tax_id;
            $commercial->postal_code = $this->postal_code;
            $commercial->country = $this->country ?? $this->countrySearch;
            $commercial->status = $this->status;
            $commercial->inserted = now();

            $commercial->save();

            session()->flash('message', 'Commercial created successfully.');
        }

        return redirect()->route('commercials.index');
    }

    public function render()
    {
        return view('livewire.commercials.commercial-form');
    }
}
