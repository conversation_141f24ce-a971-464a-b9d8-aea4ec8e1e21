<?php

namespace App\Livewire\Commercials;

use App\Models\Commercial;
use Livewire\Component;
use Livewire\WithPagination;

class CommercialsManagement extends Component
{
    use WithPagination;

    public $search = '';
    public $status = '';
    public $deleteId;

    protected $queryString = [
        'search' => ['except' => ''],
        'status' => ['except' => ''],
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatus()
    {
        $this->resetPage();
    }

    public function render()
    {
        $query = Commercial::query();

        if ($this->search) {
            $query->where(function ($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                    ->orWhere('commercial_id', 'like', '%' . $this->search . '%')
                    ->orWhere('email', 'like', '%' . $this->search . '%')
                    ->orWhere('phone', 'like', '%' . $this->search . '%')
                    ->orWhere('tax_id', 'like', '%' . $this->search . '%');
            });
        }

        if ($this->status !== '') {
            $query->where('status', $this->status);
        }

        $commercials = $query->latest()->paginate(10);

        return view('livewire.commercials.commercials-management', [
            'commercials' => $commercials
        ]);
    }

    public function confirmDelete($commercialId)
    {
        $this->deleteId = $commercialId;
        $this->modal('confirm-commercial-deletion')->show();
    }

    public function deleteCommercial()
    {
        $commercial = Commercial::findOrFail($this->deleteId);
        $commercial->delete();

        session()->flash('message', 'Commercial deleted successfully.');
        $this->modal('confirm-commercial-deletion')->hide();
    }
}
