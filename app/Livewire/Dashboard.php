<?php

namespace App\Livewire;

use App\Models\Club;
use App\Models\Player;
use App\Models\Recording;
use App\Models\SubscriptionTransaction;
use App\Models\TokenOrder;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Component;

class Dashboard extends Component
{
    public $totalPlayers;
    public $totalClubs;
    public $totalRecordings;
    public $totalRevenue;
    public $subscribedPlayers;
    public $activePlayers;

    // Chart data
    public $revenueChartData;
    public $playersChartData;
    public $recordingsChartData;

    // Time period for charts
    public $period = 'week'; // 'week', 'month', 'year'

    public function mount()
    {
        $this->loadStats();
        $this->loadChartData();
    }

    public function loadStats()
    {
        // Count total players
        $this->totalPlayers = Player::count();

        // Count subscribed players
        $this->subscribedPlayers = Player::where('subscribed', true)->count();

        // Count active players (played at least one game in the last 30 days)
        $this->activePlayers = Player::whereHas('recordings', function ($query) {
            $query->where('created_at', '>=', now()->subDays(30));
        })->count();

        // Count total clubs
        $this->totalClubs = Club::count();

        // Count total recordings
        $this->totalRecordings = Recording::count();

        // Calculate total revenue (from subscriptions and token orders)
        $subscriptionRevenue = SubscriptionTransaction::where('status', 'paid')
            ->sum('amount');

        $tokenRevenue = TokenOrder::where('status', 'completed')
            ->sum('total_amount');

        $this->totalRevenue = $subscriptionRevenue + $tokenRevenue;
    }

    public function loadChartData()
    {
        $this->revenueChartData = $this->getRevenueChartData();
        $this->playersChartData = $this->getPlayersChartData();
        $this->recordingsChartData = $this->getRecordingsChartData();
    }

    public function updatedPeriod()
    {
        $this->loadChartData();
        $this->dispatch('chartDataUpdated');
    }

    private function getRevenueChartData()
    {
        $days = $this->getPeriodDays();
        $startDate = now()->subDays($days);

        // Get subscription revenue by day
        $subscriptionRevenue = SubscriptionTransaction::where('status', 'paid')
            ->where('created_at', '>=', $startDate)
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('SUM(amount) as total')
            )
            ->groupBy('date')
            ->get()
            ->pluck('total', 'date')
            ->toArray();

        // Get token revenue by day
        $tokenRevenue = TokenOrder::where('status', 'completed')
            ->where('created_at', '>=', $startDate)
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('SUM(total_amount) as total')
            )
            ->groupBy('date')
            ->get()
            ->pluck('total', 'date')
            ->toArray();

        // Prepare data for chart
        $labels = [];
        $subscriptionData = [];
        $tokenData = [];

        for ($i = 0; $i < $days; $i++) {
            $date = now()->subDays($days - $i - 1)->format('Y-m-d');
            $labels[] = $this->formatDateLabel($date);
            $subscriptionData[] = $subscriptionRevenue[$date] ?? 0;
            $tokenData[] = $tokenRevenue[$date] ?? 0;
        }

        return [
            'labels' => $labels,
            'series' => [
                [
                    'name' => 'Subscription Revenue',
                    'data' => $subscriptionData
                ],
                [
                    'name' => 'Token Revenue',
                    'data' => $tokenData
                ]
            ]
        ];
    }

    private function getPlayersChartData()
    {
        $days = $this->getPeriodDays();
        $startDate = now()->subDays($days);

        // Get new players by day
        $newPlayers = Player::where('created_at', '>=', $startDate)
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as count')
            )
            ->groupBy('date')
            ->get()
            ->pluck('count', 'date')
            ->toArray();

        // Prepare data for chart
        $labels = [];
        $data = [];

        for ($i = 0; $i < $days; $i++) {
            $date = now()->subDays($days - $i - 1)->format('Y-m-d');
            $labels[] = $this->formatDateLabel($date);
            $data[] = $newPlayers[$date] ?? 0;
        }

        return [
            'labels' => $labels,
            'series' => [
                [
                    'name' => 'New Players',
                    'data' => $data
                ]
            ]
        ];
    }

    private function getRecordingsChartData()
    {
        $days = $this->getPeriodDays();
        $startDate = now()->subDays($days);

        // Get recordings by day
        $recordings = Recording::where('created_at', '>=', $startDate)
            ->select(
                DB::raw('DATE(created_at) as date_created'),
                DB::raw('COUNT(*) as count')
            )
            ->groupBy('date_created')
            ->get()
            ->pluck('count', 'date_created')
            ->toArray();

        // Prepare data for chart
        $labels = [];
        $data = [];

        for ($i = 0; $i < $days; $i++) {
            $date = now()->subDays($days - $i - 1)->format('Y-m-d');
            $labels[] = $this->formatDateLabel($date);
            $data[] = $recordings[$date] ?? 0;
        }

        return [
            'labels' => $labels,
            'series' => [
                [
                    'name' => 'Games Recorded',
                    'data' => $data
                ]
            ]
        ];
    }

    private function getPeriodDays()
    {
        return match($this->period) {
            'week' => 7,
            'month' => 30,
            'year' => 365,
            default => 30
        };
    }

    private function formatDateLabel($date)
    {
        $carbon = Carbon::parse($date);

        return match($this->period) {
            'week' => $carbon->format('D'),
            'month' => $carbon->format('j'),
            'year' => $carbon->format('M'),
            default => $carbon->format('M d')
        };
    }

    public function render()
    {
        return view('livewire.dashboard');
    }
}
