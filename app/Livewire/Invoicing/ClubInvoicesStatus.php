<?php

namespace App\Livewire\Invoicing;

use App\Models\Token;
use App\Models\InvoiceHeader;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\DB;

class ClubInvoicesStatus extends Component
{
    use WithPagination;

    public $search = '';
    public $statusFilter = '';
    public $showPaymentModal = false;
    public $selectedInvoiceId = null;
    public $selectedPaymentType = '';

    protected $queryString = [
        'search' => ['except' => ''],
        'statusFilter' => ['except' => ''],
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatusFilter()
    {
        $this->resetPage();
    }

    public function render()
    {
        $query = InvoiceHeader::with(['club'])
            ->orderBy('status', 'asc') // Outstanding (1) first, then Paid (2)
            ->orderBy('date', 'desc');

        if ($this->search) {
            $query->whereHas('club', function ($q) {
                $q->where('club_name', 'like', '%' . $this->search . '%')
                    ->orWhere('club_id', 'like', '%' . $this->search . '%');
            })->orWhere('invoice_nbr', 'like', '%' . $this->search . '%');
        }

        if ($this->statusFilter !== '') {
            $query->where('status', $this->statusFilter);
        }

        $invoices = $query->paginate(10);

        return view('livewire.invoicing.club-invoices-status', [
            'invoices' => $invoices
        ]);
    }

    public function openPaymentModal($invoiceId)
    {
        $this->selectedInvoiceId = $invoiceId;
        $this->selectedPaymentType = '';
        $this->showPaymentModal = true;
    }

    public function confirmPayment()
    {
        if (!$this->selectedInvoiceId || !$this->selectedPaymentType) {
            $this->dispatch('notify', variant: 'danger', title: 'Invalid Data', message: 'Please select a payment type.');
            return;
        }

        DB::transaction(function () {
            $invoice = InvoiceHeader::findOrFail($this->selectedInvoiceId);
            
            // Map payment type text to integer
            $payTypeMap = [
                'Wire' => 0,
                'Cash' => 1,
                'Other' => 2,
            ];
            
            $payType = $payTypeMap[$this->selectedPaymentType] ?? 0;

            // Update invoice status to Paid (2) and set payment type
            $invoice->update([
                'status' => 2, // Paid
                'pay_type' => $payType
            ]);

            // Update all tokens with this invoice_id to set paid_at
            Token::where('invoice_id', $this->selectedInvoiceId)
                ->whereNull('paid_at')
                ->update([
                    'paid_at' => now()->toDateTimeString()
                ]);

            // Update status for tokens that are already redeemed after payment
            // Any token redeemed after payment should have status change from Paid to Paid-Redeemed
            Token::where('invoice_id', $this->selectedInvoiceId)
                ->where('status', 'Redeemed')
                ->whereNotNull('paid_at')
                ->update([
                    'status' => 'Paid-Redeemed'
                ]);
        });

        $this->showPaymentModal = false;
        $this->selectedInvoiceId = null;
        $this->selectedPaymentType = '';

        $this->dispatch('notify', variant: 'success', title: 'Payment Confirmed', message: 'Invoice has been marked as paid successfully.');
    }

    public function closePaymentModal()
    {
        $this->showPaymentModal = false;
        $this->selectedInvoiceId = null;
        $this->selectedPaymentType = '';
    }
}
