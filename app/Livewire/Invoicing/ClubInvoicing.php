<?php

namespace App\Livewire\Invoicing;

use App\Models\Club;
use App\Models\Token;
use App\Models\InvoiceHeader;
use App\Models\InvoiceLine;
use App\Models\Payment;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\DB;
// use Spatie\LaravelPdf\Facades\Pdf;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use App\Mail\InvoiceMail;


class ClubInvoicing extends Component
{
    use WithPagination;

    public $search = '';
    public $status = '';
    public $sellTokensFilter = '';
    public $selectedClubId = null;
    public $showInvoiceModal = false;
    public $invoicePreview = [];

    // Payment properties
    public $showPaymentModal = false;
    public $selectedClubForPayment = null;
    public $paymentType = 0; // 0 = Wire, 1 = Cash

    protected $queryString = [
        'search' => ['except' => ''],
        'status' => ['except' => ''],
        'sellTokensFilter' => ['except' => ''],
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatus()
    {
        $this->resetPage();
    }

    public function updatingSellTokensFilter()
    {
        $this->resetPage();
    }

    public function render()
    {
        $query = Club::query();

        if ($this->search) {
            $query->where(function ($q) {
                $q->where('club_name', 'like', '%' . $this->search . '%')
                    ->orWhere('club_id', 'like', '%' . $this->search . '%')
                    ->orWhere('email', 'like', '%' . $this->search . '%');
            });
        }

        if ($this->status) {
            $query->where('status', $this->status);
        }

        if ($this->sellTokensFilter !== '') {
            $query->where('sell_tokens', $this->sellTokensFilter);
        }

        $clubs = $query->orderBy('created_at', 'desc')->paginate(10);

        // Calculate statistics for each club
        foreach ($clubs as $club) {
            $club->stats = $this->calculateClubStats($club);
        }

        return view('livewire.invoicing.club-invoicing', [
            'clubs' => $clubs
        ]);
    }

    private function calculateClubStats($club)
    {
        $stats = [
            'purchased' => 0,
            'redeemed' => 0,
            'paid_redeemed' => 0,
            'unpaid_tokens' => 0,
            'balance' => 0,
        ];

        // Get all tokens for this club
        $tokens = Token::where('club_id', $club->club_id)
            ->with('tokenType')
            ->get(['token_id', 'token_type_id', 'club_id', 'status', 'purchased_at', 'invoiced_at']);


        foreach ($tokens as $token) {
            $tokenValue = $token->tokenType ? $token->tokenType->value_in_eur : 0;
            // $commission = 1 - ($club->commission / 100);
            // $adjustedValue = $tokenValue * $commission;
            $adjustedValue = $tokenValue;

            // Purchased tokens (purchased_at is not null)
            if ($token->purchased_at && !$token->invoiced_at) {
                $stats['purchased'] += $adjustedValue;
            }

            // Redeemed tokens (status = 'Redeemed' and not invoiced)
            if ($token->status === 'Redeemed' && !$token->invoiced_at) {
                $stats['redeemed'] += $adjustedValue;
            }

            // Paid-Redeemed tokens (invoiced and paid, then redeemed)
            if ($token->purchased_at && $token->invoiced_at && $token->status === 'Redeemed') {
                $stats['paid_redeemed'] += $adjustedValue;
            }

            // Unpaid tokens (purchased but not paid)
            if (!$token->purchased_at && $token->invoiced_at) {
                $stats['unpaid_tokens'] += $adjustedValue;
            }
        }

        // Calculate balance: Purchased - Redeemed + Paid-Redeemed
        $stats['balance'] = $stats['purchased'] - $stats['redeemed'] + $stats['paid_redeemed'];

        return $stats;
    }

    public function toggleClubStatus($clubId)
    {
        $club = Club::findOrFail($clubId);
        $newStatus = $club->status === 'Active' ? 'Suspended' : 'Active';

        $club->update(['status' => $newStatus]);

        $this->dispatch('notify', variant: 'success', title: 'Club Status Changed', message: "Club status changed to {$newStatus}.");
    }

    public function prepareInvoice($clubId)
    {
        $club = Club::findOrFail($clubId);

        // Check if club can generate invoice
        if (!$club->sell_tokens) {
            $this->dispatch('notify', variant: 'danger', title: 'Cannot Generate Invoice', message: 'This club cannot generate invoices (sell_tokens is false).');
            return;
        }

        // Check for outstanding invoices
        $unpaidInvoices = InvoiceHeader::where('club_id', $clubId)
            ->where('status', 1)
            ->count();

        if ($unpaidInvoices > 0) {
            $this->dispatch('notify', variant: 'danger', title: 'Unpaid Invoices', message: 'Cannot generate invoice. Club has unpaid invoices.');

            return;
        }

        $this->selectedClubId = $clubId;
        $this->generateInvoicePreview($club);
        $this->showInvoiceModal = true;
    }

    private function generateInvoicePreview($club)
    {
        $tokens = Token::where('club_id', $club->club_id)
            ->where('invoiced_at', null)
            ->with('tokenType')
            ->get();

        $lines = [];
        $commission = 1 - ($club->commission / 100);

        // Line 1: Tokens Purchased (and not invoiced)
        $purchasedTokens = $tokens->where('purchased_at', '!=', null);
        if ($purchasedTokens->count() > 0) {
            $totalValue = 0;
            $tokenIds = [];
            foreach ($purchasedTokens as $token) {
                $tokenValue = $token->tokenType ? $token->tokenType->value_in_eur : 0;
                $totalValue += $tokenValue * $commission;
                $tokenIds[] = $token->token_id;
            }

            $lines[] = [
                'description' => 'TOKENS PURCHASED (' . implode(', ', array_slice($tokenIds, 0, 5)) .
                    (count($tokenIds) > 5 ? ', ...)' : ')'),
                'qty' => $purchasedTokens->count(),
                'value' => $totalValue,
                'type' => 'purchased'
            ];
        }

        // Line 2: Tokens Redeemed (and not invoiced) - negative value
        $redeemedTokens = $tokens->where('status', 'Redeemed');
        if ($redeemedTokens->count() > 0) {
            $totalValue = 0;
            $tokenIds = [];
            foreach ($redeemedTokens as $token) {
                $tokenValue = $token->tokenType ? $token->tokenType->value_in_eur : 0;
                $totalValue += $tokenValue * $commission;
                $tokenIds[] = $token->token_id;
            }

            $lines[] = [
                'description' => 'TOKENS REDEEMED (' . implode(', ', array_slice($tokenIds, 0, 5)) .
                    (count($tokenIds) > 5 ? ', ...)' : ')'),
                'qty' => $redeemedTokens->count(),
                'value' => -$totalValue,
                'type' => 'redeemed'
            ];
        }

        // Line 3: Tokens Paid then Redeemed
        $paidRedeemedTokens = Token::where('club_id', $club->club_id)
            ->where('invoiced_at', '!=', null)
            ->where('status', 'Redeemed')
            ->where('purchased_at', '!=', null)
            ->with('tokenType')
            ->get();

        if ($paidRedeemedTokens->count() > 0) {
            $totalValue = 0;
            $tokenIds = [];
            foreach ($paidRedeemedTokens as $token) {
                $tokenValue = $token->tokenType ? $token->tokenType->value_in_eur : 0;
                $totalValue += $tokenValue * $commission;
                $tokenIds[] = $token->token_id;
            }

            $lines[] = [
                'description' => 'TOKENS PAID THEN REDEEMED (' . implode(', ', array_slice($tokenIds, 0, 5)) .
                    (count($tokenIds) > 5 ? ', ...)' : ')'),
                'qty' => $paidRedeemedTokens->count(),
                'value' => $totalValue,
                'type' => 'paid_redeemed'
            ];
        }

        $totalInvoiceValue = collect($lines)->sum('value');

        $this->invoicePreview = [
            'club' => $club,
            'lines' => $lines,
            'total' => $totalInvoiceValue,
            'currency' => $club->currency ?: 'EUR'
        ];
    }

    public function confirmInvoiceGeneration()
    {
        if (!$this->selectedClubId || empty($this->invoicePreview)) {
            $this->dispatch('notify', variant: 'danger', title: 'Invalid Invoice Data', message: 'Invalid invoice data.');
            return;
        }

        DB::transaction(function () {
            $club = Club::findOrFail($this->selectedClubId);

            // Generate invoice
            $invoiceId = InvoiceHeader::generateInvoiceId();
            $invoiceNumber = InvoiceHeader::generateInvoiceNumber();

            // Create invoice header
            $invoice = InvoiceHeader::create([
                'invoice_id' => $invoiceId,
                'invoice_nbr' => $invoiceNumber,
                'club_id' => $club->club_id,
                'date' => now()->toDateString(),
                'value' => $this->invoicePreview['total'],
                'currency' => $this->invoicePreview['currency'],
                'status' => 1, // Outstanding
                'pay_type' => 0, // Wire
            ]);

            // Create invoice lines
            foreach ($this->invoicePreview['lines'] as $index => $line) {
                InvoiceLine::create([
                    'invoice_id' => $invoiceId,
                    'line_nbr' => $index + 1,
                    'description' => $line['description'],
                    'qty' => $line['qty'],
                    'value' => $line['value'],
                ]);
            }

            // Update tokens with invoice_id
            $this->updateTokensWithInvoice($club, $invoiceId);

            // Generate and send PDF
            $this->generateAndSendInvoicePDF($invoice);
        });

        $this->showInvoiceModal = false;
        $this->selectedClubId = null;
        $this->invoicePreview = [];

        $this->dispatch('notify', variant: 'success', title: 'Invoice Generated', message: 'Invoice generated and sent successfully.');
    }

    private function updateTokensWithInvoice($club, $invoiceId)
    {
        // Update all uninvoiced tokens for this club
        Token::where('club_id', $club->club_id)
            ->where('invoiced_at', null)
            ->where(function ($query) {
                $query->where('purchased_at', '!=', null)
                    ->orWhere('status', 'Redeemed')
                    ->orWhere('invoiced_at', '!=', null);
            })
            ->update([
                'invoice_id' => $invoiceId,
                'invoiced_at' => now()->toDateTimeString()
            ]);
    }

    private function generateAndSendInvoicePDF($invoice)
    {
        // Ensure invoices directory exists
        $invoicesDir = storage_path('app/invoices');
        if (!file_exists($invoicesDir)) {
            mkdir($invoicesDir, 0755, true);
        }

        // Generate PDF
        // Pdf::view('pdfs.invoice', ['invoice' => $invoice])
        //     ->format('a4')
        //     ->save(storage_path('app/invoices/invoice_' . $invoice->invoice_nbr . '.pdf'));

        $pdf = Pdf::loadView('pdfs.invoice', ['invoice' => $invoice]);
        $pdf->save(storage_path('app/invoices/invoice_' . $invoice->invoice_nbr . '.pdf'));


        // Send email
        try {
            Mail::to($invoice->club->email)
                ->cc('<EMAIL>')
                ->send(new InvoiceMail($invoice));
        } catch (\Exception $e) {
            // Log error but don't fail the invoice generation
            Log::error('Failed to send invoice email: ' . $e->getMessage());
        }
    }

    public function closeInvoiceModal()
    {
        $this->showInvoiceModal = false;
        $this->selectedClubId = null;
        $this->invoicePreview = [];
    }

    /**
     * Show payment modal for club
     */
    public function payClub($clubId)
    {
        $club = Club::findOrFail($clubId);

        // Check if club can be paid (sell_tokens should be false)
        if ($club->sell_tokens) {
            $this->dispatch('notify', variant: 'danger', title: 'Cannot Pay Club', message: 'This club uses payment gateway (sell_tokens is true).');
            return;
        }

        // Check if there are any purchased tokens to pay
        $purchasedTokensCount = Token::where('club_id', $clubId)
            ->whereNotNull('purchased_at')
            ->whereNull('paid_at')
            ->count();

        if ($purchasedTokensCount === 0) {
            $this->dispatch('notify', variant: 'danger', title: 'No Tokens to Pay', message: 'This club has no purchased tokens to pay.');
            return;
        }

        $this->selectedClubForPayment = $club;
        $this->paymentType = 0; // Default to Wire
        $this->showPaymentModal = true;
    }

    /**
     * Process the payment
     */
    public function processPayment()
    {
        if (!$this->selectedClubForPayment) {
            $this->dispatch('notify', variant: 'danger', title: 'Invalid Payment', message: 'No club selected for payment.');
            return;
        }

        DB::transaction(function () {
            $club = $this->selectedClubForPayment;

            // Get all purchased tokens that haven't been paid yet
            $tokensToUpdate = Token::where('club_id', $club->club_id)
                ->whereNotNull('purchased_at')
                ->whereNull('paid_at')
                ->with('tokenType')
                ->get();

            if ($tokensToUpdate->isEmpty()) {
                $this->dispatch('notify', variant: 'danger', title: 'No Tokens to Pay', message: 'No purchased tokens found to pay.');
                return;
            }

            // Calculate total value with commission
            $totalValue = 0;
            foreach ($tokensToUpdate as $token) {
                $tokenValue = $token->tokenType ? $token->tokenType->value_in_eur : 0;
                $commission = $club->commission / 100;
                $totalValue += $tokenValue * $commission;
            }

            // Generate payment
            $paymentId = Payment::generatePaymentId();
            $paymentNumber = Payment::generatePaymentNumber();

            // Create payment record
            $payment = Payment::create([
                'payment_id' => $paymentId,
                'payment_nbr' => $paymentNumber,
                'type' => 1, // Club
                'type_id' => $club->club_id,
                'date' => now()->toDateString(),
                'value' => $totalValue,
                'currency' => 'EU',
                'pay_type' => $this->paymentType,
            ]);

            // Update all tokens with payment_id and paid_at
            $tokensToUpdate->each(function ($token) use ($paymentId) {
                $token->update([
                    'payment_id' => $paymentId,
                    'paid_at' => now()->toDateTimeString(),
                    'invoiced_at' => now()->toDateTimeString()
                ]);
            });

            $this->dispatch('notify', variant: 'success', title: 'Payment Processed', message: "Payment {$paymentNumber} processed successfully for €" . number_format($totalValue, 2));
        });

        $this->closePaymentModal();
    }

    /**
     * Close payment modal
     */
    public function closePaymentModal()
    {
        $this->showPaymentModal = false;
        $this->selectedClubForPayment = null;
        $this->paymentType = 0;
    }
}
