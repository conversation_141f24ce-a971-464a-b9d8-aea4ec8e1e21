<?php

namespace App\Livewire\Players;

use App\Models\Player;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Livewire\Component;

class PlayerForm extends Component
{
    public $player_id;
    public $first_name;
    public $last_name;
    public $email;
    public $phone;
    public $country_code;
    public $sex;
    public $postal_code;
    public $country;
    public $credits = 0;
    public $trial_credits ;
    public $status = 'Active';
    public $suspension_reason = '';
    public $language;
    public $rating_type;
    public $password;
    public $password_confirmation;
    public $is_phone_verified = false;
    public $is_email_verified = false;

    public $isEdit = false;
    public $playerId = null;

    // For searchable country select
    public $countrySearch = '';
    public $selectedCountry = null;
    public $countries = [];
    public $showCountryDropdown = false;

    protected $rules = [
        'first_name' => 'required|string|max:255',
        'last_name' => 'required|string|max:255',
        'email' => 'required|email|max:255',
        'phone' => 'nullable|string|max:20',
        'country_code' => 'nullable|string|max:5',
        'sex' => 'nullable|string|in:Male,Female,Other',
        'postal_code' => 'nullable|string|max:20',
        'country' => 'nullable|string|max:100',
        'credits' => 'nullable|numeric',
        'trial_credits' => 'nullable|numeric',
        'status' => 'required|in:Active,Suspended',
        'suspension_reason' => 'nullable|string|max:254',
        'language' => 'nullable|string|max:5',
        'rating_type' => 'nullable|string',
    ];

    public function mount($id = null)
    {
        if ($id) {
            $this->isEdit = true;
            $this->playerId = $id;
            $player = Player::findOrFail($id);

            $this->player_id = $player->player_id;
            $this->first_name = $player->first_name;
            $this->last_name = $player->last_name;
            $this->email = $player->email;
            $this->phone = $player->phone;
            $this->country_code = $player->country_code;
            $this->sex = $player->sex;
            $this->postal_code = $player->postal_code;
            $this->country = $player->country;
            $this->credits = $player->credits;
            $this->trial_credits = $player->trial_credits;
            $this->status = $player->status;
            $this->is_email_verified = $player->is_email_verified ? true : false;
            $this->is_phone_verified = $player->is_phone_verified ? true : false;
            $this->suspension_reason = $player->suspension_reason ?? '';
            $this->language = $player->language;
            $this->rating_type = $player->rating_type;

            // Set the search fields to match the current values
            $this->countrySearch = $player->country;

            // Add password validation only for new players
            $this->rules['password'] = 'nullable|min:6|confirmed';
        } else {
            // Add password validation for new players
            $this->rules['password'] = 'required|min:6|confirmed';
            $this->rules['email'] = 'required|email|max:255|unique:players,email';
        }

        // Initial load of countries
        $this->loadCountries();
    }

    /**
     * Load countries based on search term
     */
    public function loadCountries()
    {
        $query = DB::table('countries')
            ->select(['id', 'name'])
            ->orderBy('name');

        if ($this->countrySearch) {
            $query->where('name', 'like', '%' . $this->countrySearch . '%');
        }

        $this->countries = $query->limit(10)->get();
    }

    public function updatedCountrySearch()
    {
        $this->loadCountries();
        $this->showCountryDropdown = true;
    }

    public function selectCountry($countryId, $countryName)
    {
        $this->country = $countryName;
        $this->selectedCountry = (object)['id' => $countryId, 'name' => $countryName];
        $this->countrySearch = $countryName;
        $this->showCountryDropdown = false;
    }

    public function save()
    {
        $this->validate();

        if ($this->isEdit) {
            $player = Player::findOrFail($this->playerId);

            $player->first_name = $this->first_name;
            $player->last_name = $this->last_name;
            $player->email = $this->email;
            $player->phone = $this->phone;
            $player->country_code = $this->country_code;
            $player->sex = $this->sex;
            $player->postal_code = $this->postal_code;
            $player->country = $this->country ?? $this->countrySearch;
            $player->credits = $this->credits;
            $player->trial_credits = $this->trial_credits;
            $player->status = $this->status;
            $player->is_email_verified = $this->is_email_verified;
            $player->is_phone_verified = $this->is_phone_verified;
            $player->suspension_reason = $this->status === 'Suspended' ? $this->suspension_reason : null;
            $player->language = $this->language;
            $player->rating_type = $this->rating_type;

            if ($this->password) {
                $player->password = Hash::make($this->password);
            }

            $player->save();

            session()->flash('message', 'Player updated successfully.');
        } else {
            // Generate a unique player ID
            $player_id = Player::generatePlayerId();

            $player = new Player();
            $player->player_id = $player_id;
            $player->first_name = $this->first_name;
            $player->last_name = $this->last_name;
            $player->email = $this->email;
            $player->phone = $this->phone;
            $player->country_code = $this->country_code;
            $player->sex = $this->sex;
            $player->postal_code = $this->postal_code;
            $player->country = $this->country ?? $this->countrySearch;
            $player->credits = $this->credits ?? 0;
            $player->trial_credits = $this->trial_credits ?? 0;
            $player->status = $this->status;
            $player->is_email_verified = $this->is_email_verified;
            $player->is_phone_verified = $this->is_phone_verified;
            $player->suspension_reason = $this->status === 'Suspended' ? $this->suspension_reason : null;
            $player->language = $this->language;
            $player->rating_type = $this->rating_type;
            $player->password = Hash::make($this->password);
            $player->started = now();
            $player->subscribed = false;
            $player->is_verified = false;
            $player->is_email_verified = false;
            $player->is_phone_verified = false;

            $player->save();

            session()->flash('message', 'Player created successfully.');
        }

        return redirect()->route('players.index');
    }

    public function render()
    {
        return view('livewire.players.player-form');
    }
}
