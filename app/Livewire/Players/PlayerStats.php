<?php

namespace App\Livewire\Players;

use App\Models\Player;
use App\Models\PlayerStat;
use App\Models\PlayerComment;
use App\Models\Game;
use App\Models\StatsParameter;
use App\Services\TimezoneService;
use Livewire\Component;

class PlayerStats extends Component
{
    public $player;
    public $playerId;
    public $activeTab = 'player-stats';
    public $playerStats = [];
    public $gameStats = [];
    public $comments = [];
    public $overallRating = 0;
    public $dailyRatings = [];
    public $totalGames = 0;

    protected $timezoneService;

    public function mount($id, TimezoneService $timezoneService)
    {
        $this->timezoneService = $timezoneService;
        $this->playerId = $id;
        $this->player = Player::where('player_id', $id)->firstOrFail();
        $this->loadPlayerStats();
        $this->loadGameStats();
        $this->loadComments();
    }

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
    }

    public function loadPlayerStats()
    {
        // Get user timezone based on IP
        $userIP = $this->timezoneService->getUserIP();
        $userTimezone = $this->timezoneService->getUserTimezoneByIP($userIP);

        // First, determine how many games in total this player has in the database
        $this->totalGames = PlayerStat::where('player_id', $this->player->player_id)
            ->distinct()
            ->count('game_id');

        if ($this->totalGames === 0) {
            $this->playerStats = [];
            $this->overallRating = 0;
            $this->dailyRatings = [];
            return;
        }

        // Get all games for this player, ordered by date/time (most recent first)
        $playerGameIds = PlayerStat::where('player_id', $this->player->player_id)
            ->distinct()
            ->pluck('game_id')
            ->toArray();

        $games = Game::whereIn('id', $playerGameIds)
            ->orderBy('date', 'desc')
            ->orderBy('end_time', 'desc')
            ->get();

        // Filter ended games and take only last 7 (or less if player has fewer than 7 games)
        $endedGameIds = [];
        foreach ($games as $game) {
            if ($this->timezoneService->hasGameEnded($game, $userTimezone)) {
                $endedGameIds[] = $game->id;
                if (count($endedGameIds) >= 7) {
                    break; // Only take last 7 games
                }
            }
        }

        if (empty($endedGameIds)) {
            $this->playerStats = [];
            $this->overallRating = 0;
            $this->dailyRatings = [];
            return;
        }

        // Get stats for last 7 ended games - only player-specific stats
        $playerStats = PlayerStat::with(['parameter', 'game'])
            ->whereIn('game_id', $endedGameIds)
            ->where('player_id', $this->player->player_id)
            ->get();

        if (!$playerStats->count()) {
            $this->playerStats = [];
            $this->overallRating = 0;
            $this->dailyRatings = [];
            return;
        }

        // Get all stats parameters to ensure we include all types
        $allParameters = StatsParameter::all();

        // Group stats by parameter and calculate totals
        $groupedStats = [];
        $dailyRatings = [];

        foreach ($allParameters as $parameter) {
            $paramStats = $playerStats->where('stat_param_id', $parameter->id);
            
            if ($paramStats->count() > 0) {
                if ($parameter->sum_avg == 1) {
                    // Sum for types 1-18
                    $total = $paramStats->sum('total');
                    $success = $paramStats->sum('success');
                    $winner = $paramStats->sum('winner');
                    $loss = $paramStats->sum('loss');
                } else {
                    // Average for types 19-23
                    $total = $paramStats->avg('total');
                    $success = $paramStats->avg('success');
                    $winner = $paramStats->avg('winner');
                    $loss = $paramStats->avg('loss');
                }
            } else {
                // Set to 0 if no data exists for this parameter
                $total = 0;
                $success = 0;
                $winner = 0;
                $loss = 0;
            }

            $groupedStats[] = [
                'parameter' => $parameter,
                'total' => round($total, 2),
                'success' => round($success, 2),
                'winner' => round($winner, 2),
                'loss' => round($loss, 2),
            ];

            // Collect daily ratings for type ID 22 (RATING)
            if ($parameter->id == 22) {
                foreach ($paramStats as $stat) {
                    $dailyRatings[] = [
                        'date' => $stat->game->date,
                        'rating' => $stat->total
                    ];
                }
            }
        }

        // Calculate overall rating from 7-day 'total' field values for type ID 22 (RATING)
        $ratingStats = $playerStats->where('stat_param_id', 23);
        $this->overallRating = $ratingStats->count() > 0 ? round($ratingStats->avg('total'), 2) : 0;

        // Sort daily ratings by date
        usort($dailyRatings, function($a, $b) {
            return strtotime($a['date']) - strtotime($b['date']);
        });

        $this->playerStats = $groupedStats;
        $this->dailyRatings = $dailyRatings;
    }

    public function loadGameStats()
    {
        // Get user timezone based on IP
        $userIP = $this->timezoneService->getUserIP();
        $userTimezone = $this->timezoneService->getUserTimezoneByIP($userIP);

        $games = Game::orderBy('date', 'desc')
            ->orderBy('end_time', 'desc')
            ->get();

        $endedGameIds = [];
        foreach ($games as $game) {
            if ($this->timezoneService->hasGameEnded($game, $userTimezone)) {
                $endedGameIds[] = $game->id;
            }
        }

        if (empty($endedGameIds)) {
            $this->gameStats = [];
            return;
        }

        $playerStats = PlayerStat::with(['parameter', 'game'])
            ->whereIn('game_id', $endedGameIds)
            ->whereNull('player_id')
            ->get();

        if (!$playerStats->count()) {
            $this->gameStats = [];
            return;
        }

        // Get all stats parameters to ensure we include all types
        $allParameters = StatsParameter::all();

        // Group stats by parameter and calculate totals
        $groupedStats = [];

        foreach ($allParameters as $parameter) {
            $paramStats = $playerStats->where('stat_param_id', $parameter->id);
            
            if ($paramStats->count() > 0) {
                if ($parameter->sum_avg == 1) {
                    // Sum for types 1-18
                    $total = $paramStats->sum('total');
                    $success = $paramStats->sum('success');
                    $winner = $paramStats->sum('winner');
                    $loss = $paramStats->sum('loss');
                } else {
                    // Average for types 19-23
                    $total = $paramStats->avg('total');
                    $success = $paramStats->avg('success');
                    $winner = $paramStats->avg('winner');
                    $loss = $paramStats->avg('loss');
                }
            } else {
                // Set to 0 if no data exists for this parameter
                $total = 0;
                $success = 0;
                $winner = 0;
                $loss = 0;
            }

            $groupedStats[] = [
                'parameter' => $parameter,
                'total' => round($total, 2),
                'success' => round($success, 2),
                'winner' => round($winner, 2),
                'loss' => round($loss, 2),
            ];
        }

        $this->gameStats = $groupedStats;
    }

    public function loadComments()
    {
        // Get user timezone based on IP
        $userIP = $this->timezoneService->getUserIP();
        $userTimezone = $this->timezoneService->getUserTimezoneByIP($userIP);

        // Get all games for this player, ordered by date/time (most recent first)
        $playerGameIds = PlayerStat::where('player_id', $this->player->player_id)
            ->distinct()
            ->pluck('game_id')
            ->toArray();

        if (empty($playerGameIds)) {
            $this->comments = [];
            return;
        }

        $games = Game::whereIn('id', $playerGameIds)
            ->orderBy('date', 'desc')
            ->orderBy('end_time', 'desc')
            ->get();

        // Filter ended games and take only last 7 (or less if player has fewer than 7 games)
        $endedGameIds = [];
        foreach ($games as $game) {
            if ($this->timezoneService->hasGameEnded($game, $userTimezone)) {
                $endedGameIds[] = $game->id;
                if (count($endedGameIds) >= 7) {
                    break; // Only take last 7 games
                }
            }
        }

        if (empty($endedGameIds)) {
            $this->comments = [];
            return;
        }

        // Get comments for last 7 ended games only
        $comments = PlayerComment::with(['coachComment', 'game'])
            ->where('player_id', $this->player->player_id)
            ->whereIn('game_id', $endedGameIds)
            ->get();

        // Format comments for display
        $formattedComments = [];
        foreach ($comments as $comment) {
            $formattedComments[] = [
                'id' => $comment->id,
                'message' => $comment->coachComment->getLocalizedMessageAttribute(),
                'message_id' => $comment->coach_comment_id,
                'date' => $comment->date,
                'time' => $comment->time,
                'game_date' => $comment->game->date ?? null,
            ];
        }

        $this->comments = $formattedComments;
    }

    public function render()
    {
        return view('livewire.players.player-stats');
    }
}
