<?php

namespace App\Livewire\Players;

use App\Models\Player;
use Livewire\Component;
use Livewire\WithPagination;

class PlayersManagement extends Component
{
    use WithPagination;

    public $search = '';
    public $status = '';
    public $subscription = '';

    protected $queryString = [
        'search' => ['except' => ''],
        'status' => ['except' => ''],
        'subscription' => ['except' => ''],
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatus()
    {
        $this->resetPage();
    }

    public function updatingSubscription()
    {
        $this->resetPage();
    }

    public function render()
    {
        $query = Player::query();

        if ($this->search) {
            $query->where(function ($q) {
                $q->where('first_name', 'like', '%' . $this->search . '%')
                  ->orWhere('last_name', 'like', '%' . $this->search . '%')
                  ->orWhere('player_id', 'like', '%' . $this->search . '%')
                  ->orWhere('email', 'like', '%' . $this->search . '%')
                  ->orWhere('phone', 'like', '%' . $this->search . '%');
            });
        }

        if ($this->status) {
            $query->where('status', $this->status);
        }

        if ($this->subscription) {
            $query->where('subscribed', $this->subscription === 'subscribed');
        }

        $players = $query->latest()->paginate(10);

        return view('livewire.players.players-management', [
            'players' => $players
        ]);
    }
}
