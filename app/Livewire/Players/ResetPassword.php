<?php

namespace App\Livewire\Players;

use App\Models\Player;
use Illuminate\Support\Facades\Hash;
use Livewire\Attributes\On;
use Livewire\Component;

class ResetPassword extends Component
{
    public $playerId;
    public $password;
    public $password_confirmation;
    
    protected $rules = [
        'password' => 'required|min:6|confirmed',
    ];
    
    #[On('open-modal')]
    public function openModal($playerId)
    {
        $this->playerId = $playerId;
        $this->modal('confirm-password-reset')->show();
    }

    public function resetPassword()
    {
        $this->validate();
        
        $player = Player::findOrFail($this->playerId);
        $player->password = Hash::make($this->password);
        $player->save();
        
        session()->flash('message', 'Password reset successfully.');
        
        $this->modal('confirm-password-reset')->close();

        $this->reset(['password', 'password_confirmation']);
    }
    
    public function render()
    {
        return view('livewire.players.reset-password');
    }
}
