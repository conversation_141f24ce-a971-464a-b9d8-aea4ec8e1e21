<?php

namespace App\Livewire\Sponsors;

use App\Models\Stat;
use App\Models\Sponsor;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;
use Livewire\WithPagination;

class SponsorsManagement extends Component
{
    use WithPagination;

    public $search = '';
    public $status = '';
    public $deleteId;

    protected $queryString = [
        'search' => ['except' => ''],
        'status' => ['except' => ''],
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatus()
    {
        $this->resetPage();
    }

    public function render()
    {
        $query = Sponsor::with('commercial');

        if ($this->search) {
            $query->where(function ($q) {
                $q->where('company_name', 'like', '%' . $this->search . '%')
                    ->orWhere('sponsor_id', 'like', '%' . $this->search . '%')
                    ->orWhere('email', 'like', '%' . $this->search . '%')
                    ->orWhere('phone', 'like', '%' . $this->search . '%')
                    ->orWhere('tax_id', 'like', '%' . $this->search . '%')
                    ->orWhere('contact', 'like', '%' . $this->search . '%')
                    ->orWhereHas('commercial', function ($q) {
                        $q->where('name', 'like', '%' . $this->search . '%');
                    });
            });
        }

        if ($this->status !== '') {
            $query->where('status', $this->status);
        }

        $sponsors = $query->latest()->paginate(10);

        return view('livewire.sponsors.sponsors-management', [
            'sponsors' => $sponsors
        ]);
    }

    public function confirmDelete($sponsorId)
    {
        $this->deleteId = $sponsorId;
        $this->modal('confirm-sponsor-deletion')->show();
    }

    public function deleteSponsor()
    {
        $sponsor = Sponsor::findOrFail($this->deleteId);
        
        // Delete logo file if exists
        if ($sponsor->logo && Storage::disk('public')->exists($sponsor->logo)) {
            Storage::disk('public')->delete($sponsor->logo);
        }
        
        $sponsor->delete();

        Stat::decrementSponsors();

        session()->flash('message', 'Sponsor deleted successfully.');
        $this->modal('confirm-sponsor-deletion')->close();
    }
}
