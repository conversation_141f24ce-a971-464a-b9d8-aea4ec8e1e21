<?php

namespace App\Livewire\TokenTypes;

use App\Models\Club;
use App\Models\TokenType;
use App\Services\CurrencyService;
use Livewire\Component;

class TokenTypesManagement extends Component
{
    public $clubId;
    public $club;

    // Form fields for adding/editing token types
    public $tokenTypeId;
    public $value;
    public $minutes;
    public $currency;

    public $isEditing = false;
    public $showForm = false;

    protected $currencyService;

    protected $rules = [
        'value' => 'required|numeric|min:0.01|max:99999.99',
        'minutes' => 'required|numeric|min:1',
        'currency' => 'required|string|size:3',
    ];

    public function mount($clubId)
    {
        $this->clubId = $clubId;
        $this->club = Club::with('tokenTypes')->findOrFail($clubId);
        $this->currency = $this->club->currency ?? 'EUR';
        $this->currencyService = new CurrencyService();
    }

    public function showAddForm()
    {
        // Check if the club already has 2 token types
        if ($this->club->tokenTypes->count() >= 2) {
            session()->flash('error', 'A club can have a maximum of 2 token types.');
            return;
        }

        // Ensure currencyService is initialized
        if (!$this->currencyService) {
            $this->currencyService = new CurrencyService();
        }

        $this->resetForm();
        $this->showForm = true;
        $this->isEditing = false;
    }

    public function showEditForm($id)
    {
        $tokenType = TokenType::findOrFail($id);

        $this->tokenTypeId = $tokenType->id;
        $this->value = $tokenType->value;
        $this->minutes = $tokenType->minutes;
        $this->currency = $this->club->currency ?? 'EUR';

        // Ensure currencyService is initialized
        if (!$this->currencyService) {
            $this->currencyService = new CurrencyService();
        }

        $this->showForm = true;
        $this->isEditing = true;
    }

    public function saveTokenType()
    {
        $this->validate();

        // Ensure currencyService is initialized
        if (!$this->currencyService) {
            $this->currencyService = new CurrencyService();
        }

        // Convert value to EUR
        $valueInEur = $this->currencyService->convertToEur($this->value, $this->currency) ?? $this->value;

        if ($this->isEditing) {
            $tokenType = TokenType::findOrFail($this->tokenTypeId);
            $tokenType->update([
                'value' => $this->value,
                'value_in_eur' => $valueInEur,
                'minutes' => $this->minutes,
            ]);

            session()->flash('message', 'Token type updated successfully.');
        } else {
            // Check if the club already has 2 token types
            if ($this->club->tokenTypes->count() >= 2) {
                session()->flash('error', 'A club can have a maximum of 2 token types.');
                return;
            }

            TokenType::create([
                'club_id' => $this->clubId,
                'value' => $this->value,
                'value_in_eur' => $valueInEur,
                'minutes' => $this->minutes,
            ]);

            session()->flash('message', 'Token type added successfully.');
        }

        $this->resetForm();
        $this->showForm = false;

        // Refresh the club data
        $this->club = Club::with('tokenTypes')->findOrFail($this->clubId);
    }

    public function deleteTokenType($id)
    {
        $tokenType = TokenType::findOrFail($id);
        $tokenType->delete();

        session()->flash('message', 'Token type deleted successfully.');

        // Refresh the club data
        $this->club = Club::with('tokenTypes')->findOrFail($this->clubId);
    }

    public function cancelForm()
    {
        $this->resetForm();
        $this->showForm = false;
    }

    private function resetForm()
    {
        $this->tokenTypeId = null;
        $this->value = '';
        $this->minutes = '';
        $this->currency = $this->club->currency ?? 'EUR';
        $this->isEditing = false;
    }

    public function render()
    {
        return view('livewire.token-types.token-types-management');
    }
}
