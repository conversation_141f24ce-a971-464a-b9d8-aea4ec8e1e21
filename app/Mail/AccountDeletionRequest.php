<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class AccountDeletionRequest extends Mailable
{
    use Queueable, SerializesModels;

    public $name;
    public $email;
    public $reasonCode;
    public $reasonText;
    public $additionalFeedback;

    /**
     * Create a new message instance.
     */
    public function __construct(
        string $name,
        string $email,
        string $reasonCode,
        string $reasonText,
        ?string $additionalFeedback
    ) {
        $this->name = $name;
        $this->email = $email;
        $this->reasonCode = $reasonCode;
        $this->reasonText = $reasonText;
        $this->additionalFeedback = $additionalFeedback;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Account Deletion Request - ' . env('APP_NAME'),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.account-deletion-request',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
