<?php

namespace App\Mail;

use App\Models\Player;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class VerificationEmail extends Mailable
{
    use Queueable, SerializesModels;

    public $player;
    public $otp;

    public function __construct(Player $player, string $otp)
    {
        $this->player = $player;
        $this->otp = $otp;
    }

    public function build()
    {
        return $this->subject('Email Verification - ' . env('APP_NAME'))
                    ->view('emails.verification-email');
    }
}