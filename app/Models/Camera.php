<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Camera extends Model
{
    protected $primaryKey = 'camera_id';
    public $incrementing = false;

    protected $fillable = [
        'camera_id',
        'club_id',
        'court_number',
        'ip',
        'port'
    ];

    protected $casts = [
        'id' => 'string',
        'court_number' => 'integer',
        'port' => 'integer'
    ];

    public function club()
    {
        return $this->belongsTo(Club::class, 'club_id', 'club_id');
    }

    /**
     * Generate unique camera ID
     * Format: 6 characters alphanumeric (letters and numbers)
     */
    public static function generateCameraId(): string
    {
        do {
            // Generate 6 random alphanumeric characters
            $cameraId = strtoupper(Str::random(6));

            // Ensure at least one letter and one number
            while (!preg_match('/[A-Z]/', $cameraId) || !preg_match('/[0-9]/', $cameraId)) {
                $cameraId = strtoupper(Str::random(6));
            }
        } while (self::where('camera_id', $cameraId)->exists());

        return $cameraId;
    }
}
