<?php

namespace App\Models;

use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Auth\Authenticatable as AuthenticatableTrait;
use <PERSON><PERSON>\JWTAuth\Contracts\JWTSubject;
use Illuminate\Support\Str;

class Club extends Model implements Authenticatable, JWTSubject
{
    use AuthenticatableTrait;

    // JWT Auth
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    public function getJWTCustomClaims()
    {
        return [];
    }

    protected $guard = 'club';
    protected $primaryKey = 'club_id';
    public $incrementing = false;

    /**
     * Get the name of the unique identifier for the user.
     *
     * @return string
     */
    public function getAuthIdentifierName()
    {
        return 'club_id';
    }

    /**
     * Get the unique identifier for the user.
     *
     * @return mixed
     */
    public function getAuthIdentifier()
    {
        return $this->club_id;
    }

    protected $fillable = [
        'club_id',
        'started',
        'tax_id',
        'commission',
        'commercial_commission',
        'commercial_id',
        'club_name',
        'contact',
        'phone',
        'country_code',
        'email',
        'postal_code',
        'country',
        'sell_tokens',
        'status',
        'suspension_reason',
        'password',
        'fcm_token',
        'city',
        'currency',
        'language',
        'remember_token'
    ];

    protected $hidden = [
        'password',
        'fcm_token',
        'remember_token'
    ];

    protected $casts = [
        'sell_tokens' => 'boolean',
        'id' => 'string',
        'commission' => 'float',
        'commercial_commission' => 'float'
    ];

    /**
     * Generate unique club ID
     * Format: 6 characters alphanumeric (letters and numbers)
     */
    public static function generateClubId(): string
    {
        do {
            // Generate 6 random alphanumeric characters
            $clubId = strtoupper(Str::random(6));

            // Ensure at least one letter and one number
            while (!preg_match('/[A-Z]/', $clubId) || !preg_match('/[0-9]/', $clubId)) {
                $clubId = strtoupper(Str::random(6));
            }
        } while (self::where('club_id', $clubId)->exists());

        return $clubId;
    }


    public function favoriteClubs()
    {
        return $this->hasMany(FavoriteClub::class, 'club_id', 'club_id');
    }

    public function tokenTypes()
    {
        return $this->hasMany(TokenType::class, 'club_id', 'club_id');
    }

    public function cameras()
    {
        return $this->hasMany(Camera::class, 'club_id', 'club_id');
    }

    public function commercial()
    {
        return $this->belongsTo(Commercial::class, 'commercial_id', 'commercial_id');
    }

    public function invoices()
    {
        return $this->hasMany(InvoiceHeader::class, 'club_id', 'club_id');
    }

    public function tokens()
    {
        return $this->hasMany(Token::class, 'club_id', 'club_id');
    }
}
