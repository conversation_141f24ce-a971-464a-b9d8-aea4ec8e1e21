<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CoachComment extends Model
{
    protected $fillable = [
        'message_en',
        'message_fr',
        'message_es',
        'message_it',
    ];

    /**
     * Get the player comments for this coach comment
     */
    public function playerComments()
    {
        return $this->hasMany(PlayerComment::class);
    }

    /**
     * Get the message based on the current locale
     */
    public function getLocalizedMessageAttribute()
    {
        $locale = app()->getLocale();

        return match($locale) {
            'fr' => $this->message_fr,
            'es' => $this->message_es,
            'it' => $this->message_it,
            default => $this->message_en,
        };
    }
}
