<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;


class FavoriteClub extends Model
{
    protected $fillable = [
        'player_id',
        'club_id',
    ];

    /**
     * Get the player that owns the favorite
     */
    public function player()
    {
        return $this->belongsTo(Player::class, 'player_id', 'player_id');
    }

    /**
     * Get the club that is favorited
     */
    public function club()
    {
        return $this->belongsTo(Club::class, 'club_id', 'club_id');
    }

    /**
     * Toggle favorite status for a club
     */
    public static function toggle($playerId, $clubId)
    {
        $favorite = self::where('player_id', $playerId)
            ->where('club_id', $clubId)
            ->first();

        if ($favorite) {
            $favorite->delete();
            return false; // Unfavorited
        }

        self::create([
            'player_id' => $playerId,
            'club_id' => $clubId
        ]);
        return true; // Favorited
    }
}