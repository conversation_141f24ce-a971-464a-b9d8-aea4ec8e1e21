<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class FtpFile extends Model
{
    protected $fillable = [
        'file_name',
        'processed',
    ];

    protected $casts = [
        'processed' => 'boolean',
    ];

    /**
     * Scope a query to only include unprocessed files.
     */
    public function scopeUnprocessed($query)
    {
        return $query->where('processed', false);
    }

    /**
     * Mark the file as processed
     */
    public function markAsProcessed()
    {
        $this->processed = true;
        $this->save();

        return $this;
    }
}
