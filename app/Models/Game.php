<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Game extends Model
{
    protected $fillable = [
        'file_name',
        'camera_id',
        'date',
        'start_time',
        'end_time',
    ];


    /**
     * Get the stats for this game
     */
    public function playerStats()
    {
        return $this->hasMany(PlayerStat::class);
    }

    /**
     * Get the coach comments for this game
     */
    public function playerComments()
    {
        return $this->hasMany(PlayerComment::class);
    }
}
