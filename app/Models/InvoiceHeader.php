<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class InvoiceHeader extends Model
{
    protected $primaryKey = 'invoice_id';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'invoice_id',
        'invoice_nbr',
        'club_id',
        'date',
        'value',
        'currency',
        'status',
        'pay_type',
    ];

    protected $casts = [
        'date' => 'date',
        'value' => 'decimal:2',
        'status' => 'integer',
        'pay_type' => 'integer',
    ];

    /**
     * Generate unique invoice ID
     * Format: 6 characters alphanumeric
     */
    public static function generateInvoiceId(): string
    {
        do {
            $invoiceId = strtoupper(Str::random(6));
        } while (self::where('invoice_id', $invoiceId)->exists());

        return $invoiceId;
    }

    /**
     * Generate invoice number
     * Format: AU + YY + 999999 (incremental per year)
     */
    public static function generateInvoiceNumber(): string
    {
        $currentYear = date('y');
        $prefix = 'AU' . $currentYear;
        
        // Get the last invoice number for this year
        $lastInvoice = self::where('invoice_nbr', 'like', $prefix . '%')
            ->orderBy('invoice_nbr', 'desc')
            ->first();
        
        if ($lastInvoice) {
            // Extract the number part and increment
            $lastNumber = (int) substr($lastInvoice->invoice_nbr, 4);
            $newNumber = $lastNumber + 1;
        } else {
            // First invoice of the year
            $newNumber = 1;
        }
        
        return $prefix . str_pad($newNumber, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Get the club that owns this invoice
     */
    public function club()
    {
        return $this->belongsTo(Club::class, 'club_id', 'club_id');
    }

    /**
     * Get the invoice lines
     */
    public function lines()
    {
        return $this->hasMany(InvoiceLine::class, 'invoice_id', 'invoice_id');
    }

    /**
     * Get the tokens associated with this invoice
     */
    public function tokens()
    {
        return $this->hasMany(Token::class, 'invoice_id', 'invoice_id');
    }

    /**
     * Get formatted value with currency
     */
    public function getFormattedValueAttribute()
    {
        return number_format($this->value, 2) . ' ' . $this->currency;
    }

    /**
     * Get status text
     */
    public function getStatusTextAttribute()
    {
        return $this->status === 2 ? 'Paid' : 'Outstanding';
    }

    /**
     * Get pay type text
     */
    public function getPayTypeTextAttribute()
    {
        $types = [
            0 => 'Wire',
            1 => 'Cash',
            2 => 'Other'
        ];

        return $types[$this->pay_type] ?? 'Unknown';
    }
}
