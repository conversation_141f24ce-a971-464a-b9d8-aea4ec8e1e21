<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class InvoiceLine extends Model
{
    protected $fillable = [
        'invoice_id',
        'line_nbr',
        'description',
        'qty',
        'value',
    ];

    protected $casts = [
        'line_nbr' => 'integer',
        'qty' => 'integer',
        'value' => 'decimal:2',
    ];

    /**
     * Get the invoice header
     */
    public function invoice()
    {
        return $this->belongsTo(InvoiceHeader::class, 'invoice_id', 'invoice_id');
    }

    /**
     * Get formatted value
     */
    public function getFormattedValueAttribute()
    {
        return number_format($this->value, 2);
    }

    /**
     * Get total value (qty * value)
     */
    public function getTotalValueAttribute()
    {
        return $this->qty * $this->value;
    }

    /**
     * Get formatted total value
     */
    public function getFormattedTotalValueAttribute()
    {
        return number_format($this->total_value, 2);
    }
}
