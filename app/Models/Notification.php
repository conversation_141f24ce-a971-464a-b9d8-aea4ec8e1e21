<?php

namespace App\Models;

use App\Services\FirebaseDatabaseService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\App;

class Notification extends Model
{
    protected $appends = ['format_created_at'];

    protected $fillable = [
        'title',
        'body',
        'player_id',
        'club_id',
        'is_read',
        'created_at',
        'updated_at'
    ];

    /**
     * The "booted" method of the model.
     */
    protected static function booted(): void
    {
        static::created(function ($notification) {
            $notification->updateFirebaseCounts();
        });

        static::updated(function ($notification) {
            if ($notification->isDirty('is_read')) {
                $notification->updateFirebaseCounts();
            }
        });

        static::deleted(function ($notification) {
            $notification->updateFirebaseCounts();
        });
    }

    /**
     * Mark notification as read
     */
    public function markAsRead()
    {
        $this->is_read = true;
        $this->save();
    }

    /**
     * Get formatted created at attribute
     */
    public function getFormatCreatedAtAttribute()
    {
        return $this->created_at ? $this->created_at->diffForHumans() : null;
    }

    /**
     * Update Firebase counts
     */
    public function updateFirebaseCounts()
    {
        $firebaseService = App::make(FirebaseDatabaseService::class);

        // Update player notification count if this notification is for a player
        if ($this->player_id) {
            $count = self::where('player_id', $this->player_id)
                ->where('is_read', false)
                ->count();

            $firebaseService->updatePlayerNotificationCount($this->player_id, $count);
        }

        // Update club notification count if this notification is for a club
        if ($this->club_id) {
            $notificationCount = self::where('club_id', $this->club_id)
                ->where('is_read', false)
                ->count();

            // Get token request count
            $tokenRequestCount = Token::where('club_id', $this->club_id)
                ->where('status', 'Requested')
                ->count();

            // Get redeem request count
            $redeemRequestCount = Token::where('club_id', $this->club_id)
                ->where('status', 'Redeem-Requested')
                ->where('is_requested_for_redeem', true)
                ->count();

            $firebaseService->updateClubCounts(
                $this->club_id,
                $notificationCount,
                $tokenRequestCount,
                $redeemRequestCount
            );
        }
    }
}
