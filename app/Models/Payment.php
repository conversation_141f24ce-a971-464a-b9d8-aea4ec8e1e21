<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Payment extends Model
{
    protected $primaryKey = 'payment_id';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'payment_id',
        'payment_nbr',
        'type',
        'type_id',
        'date',
        'value',
        'currency',
        'pay_type',
    ];

    protected $casts = [
        'date' => 'date',
        'value' => 'decimal:2',
        'type' => 'integer',
        'pay_type' => 'integer',
    ];

    /**
     * Generate unique payment ID
     * Format: 6 characters alphanumeric
     */
    public static function generatePaymentId(): string
    {
        do {
            $paymentId = strtoupper(Str::random(6));
        } while (self::where('payment_id', $paymentId)->exists());

        return $paymentId;
    }

    /**
     * Generate payment number
     * Format: PY + YY + 999999 (incremental number resets yearly)
     */
    public static function generatePaymentNumber(): string
    {
        $currentYear = date('y'); // Get 2-digit year
        $prefix = 'PY' . $currentYear;
        
        // Get the highest payment number for current year
        $lastPayment = self::where('payment_nbr', 'like', $prefix . '%')
            ->orderBy('payment_nbr', 'desc')
            ->first();
        
        if ($lastPayment) {
            // Extract the incremental part and increment it
            $lastNumber = (int) substr($lastPayment->payment_nbr, 4);
            $nextNumber = $lastNumber + 1;
        } else {
            // First payment of the year
            $nextNumber = 1;
        }
        
        // Format with leading zeros (6 digits)
        return $prefix . str_pad($nextNumber, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Get the club if this is a club payment
     */
    public function club()
    {
        return $this->belongsTo(Club::class, 'type_id', 'club_id')->where('type', 1);
    }

    /**
     * Get the commercial if this is a commercial payment
     */
    public function commercial()
    {
        return $this->belongsTo(Commercial::class, 'type_id', 'commercial_id')->where('type', 2);
    }

    /**
     * Get the tokens associated with this payment
     */
    public function tokens()
    {
        return $this->hasMany(Token::class, 'payment_id', 'payment_id');
    }

    /**
     * Get formatted value with currency
     */
    public function getFormattedValueAttribute()
    {
        return number_format($this->value, 2) . ' ' . $this->currency;
    }

    /**
     * Get pay type text
     */
    public function getPayTypeTextAttribute()
    {
        $types = [
            0 => 'Wire',
            1 => 'Cash'
        ];

        return $types[$this->pay_type] ?? 'Unknown';
    }

    /**
     * Get type text
     */
    public function getTypeTextAttribute()
    {
        $types = [
            1 => 'Club',
            2 => 'Commercial'
        ];

        return $types[$this->type] ?? 'Unknown';
    }
}
