<?php

namespace App\Models;

use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Auth\Authenticatable as AuthenticatableTrait;
use <PERSON>mon\JWTAuth\Contracts\JWTSubject;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Str;

class Player extends Model implements Authenticatable, JWTSubject
{
    use AuthenticatableTrait;

    // JWT Auth
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    public function getJWTCustomClaims()
    {
        return [];
    }

    protected $guard = 'player';
    protected $primaryKey = 'player_id';
    public $incrementing = false;
    protected $appends = ['photo_url'];

    protected $fillable = [
        'player_id',
        'started',
        'sex',
        'first_name',
        'last_name',
        'phone',
        'country_code',
        'email',
        'postal_code',
        'country',
        'subscribed',
        'credits',
        'trial_credits',
        'status',
        'suspension_reason',
        'rating_type',
        'photo',
        'password',
        'is_verified',
        'is_email_verified',
        'is_phone_verified',
        'fcm_token',
        'language',
    ];

    // Accessors & Mutators
    protected function fullName(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->first_name . ' ' . $this->last_name,
        );
    }


    /**
     * Generate unique player ID
     * Format: 6 characters alphanumeric (letters and numbers)
     */
    public static function generatePlayerId(): string
    {
        do {
            // Generate 6 random alphanumeric characters
            $playerId = strtoupper(Str::random(6));

            // Ensure at least one letter and one number
            while (!preg_match('/[A-Z]/', $playerId) || !preg_match('/[0-9]/', $playerId)) {
                $playerId = strtoupper(Str::random(6));
            }
        } while (self::where('player_id', $playerId)->exists());

        return $playerId;
    }

    // full photo url
    public function getPhotoUrlAttribute(): string
    {
        return $this->photo
            ? asset('storage/' . $this->photo)
            : asset('images/default-player.webp');
    }

    protected $hidden = [
        'password',
        'fcm_token'
    ];

    protected $casts = [
        'subscribed' => 'boolean',
        'is_verified' => 'boolean',
        'is_email_verified' => 'boolean',
        'is_phone_verified' => 'boolean'
    ];

    public function recordings()
    {
        return $this->hasMany(Recording::class, 'player_id', 'player_id');
    }

    /**
     * Get the stats for this player
     */
    public function stats()
    {
        return $this->hasMany(PlayerStat::class, 'player_id', 'player_id');
    }

    /**
     * Get the coach comments for this player
     */
    public function playerComments()
    {
        return $this->hasMany(PlayerComment::class, 'player_id', 'player_id');
    }

    /**
     * Get the subscriptions for this player
     */
    public function subscriptions()
    {
        return $this->hasMany(PlayerSubscription::class, 'player_id', 'player_id');
    }

    /**
     * Get the active subscription for this player
     */
    public function activeSubscription()
    {
        return $this->subscriptions()
            ->where('status', 'active')
            ->where(function ($query) {
                $query->whereNull('ends_at')
                    ->orWhere('ends_at', '>', now());
            })
            ->latest()
            ->first();
    }

    /**
     * Check if the player has an active subscription
     */
    public function hasActiveSubscription()
    {
        return $this->activeSubscription() !== null;
    }

    /**
     * Update the player's subscription status
     */
    public function updateSubscriptionStatus()
    {
        $hasActiveSubscription = $this->hasActiveSubscription();

        if ($this->subscribed != $hasActiveSubscription) {
            $this->update(['subscribed' => $hasActiveSubscription]);

            // Update stats if player is newly subscribed
            if ($hasActiveSubscription && !$this->getOriginal('subscribed')) {
                Stat::incrementSubscribed();
            }
        }

        return $this;
    }
}
