<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PlayerComment extends Model
{
    protected $fillable = [
        'game_id',
        'player_id',
        'coach_comment_id',
        'date',
        'time',
    ];

    /**
     * Get the game that owns the comment
     */
    public function game()
    {
        return $this->belongsTo(Game::class);
    }

    /**
     * Get the player that owns the comment
     */
    public function player()
    {
        return $this->belongsTo(Player::class, 'player_id', 'player_id');
    }

    /**
     * Get the coach comment for this player comment
     */
    public function coachComment()
    {
        return $this->belongsTo(CoachComment::class);
    }

    public function getDateAttribute($value)
    {
        return $value ? \Carbon\Carbon::parse($value)->format('d/m/Y') : null;
    }

    public function getTimeAttribute($value)
    {
        return $value ? \Carbon\Carbon::parse($value)->format('h:i A') : null;
    }
}
