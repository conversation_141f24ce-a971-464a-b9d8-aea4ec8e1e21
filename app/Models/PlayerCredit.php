<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PlayerCredit extends Model
{
    protected $fillable = [
        'player_id',
        'club_id',
        'credits',
    ];


    /**
     * Get the player that owns these credits
     */
    public function player()
    {
        return $this->belongsTo(Player::class, 'player_id', 'player_id');
    }

    /**
     * Get the club associated with these credits
     */
    public function club()
    {
        return $this->belongsTo(Club::class, 'club_id', 'club_id');
    }

    // cast id
    protected $casts = [
        'id' => 'string'
    ];
}
