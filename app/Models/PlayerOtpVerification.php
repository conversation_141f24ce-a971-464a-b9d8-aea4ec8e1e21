<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PlayerOtpVerification extends Model
{
    protected $fillable = [
        'player_id',
        'otp',
        'is_phone_otp',
        'expires_at'
    ];

    protected $casts = [
        'expires_at' => 'datetime'
    ];

    public function player()
    {
        return $this->belongsTo(Player::class, 'player_id', 'player_id');
    }

    public function isValid()
    {
        return $this->expires_at?->isFuture();
    }
}