<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PlayerStat extends Model
{
    protected $fillable = [
        'game_id',
        'player_id',
        'stat_param_id',
        'total',
        'success',
        'winner',
        'loss',
    ];

    protected $casts = [
        'total' => 'decimal:2',
        'success' => 'decimal:2',
        'winner' => 'decimal:2',
        'loss' => 'decimal:2',
    ];

    /**
     * Get the game that owns the stat
     */
    public function game()
    {
        return $this->belongsTo(Game::class);
    }

    /**
     * Get the player that owns the stat
     */
    public function player()
    {
        return $this->belongsTo(Player::class, 'player_id', 'player_id');
    }

    /**
     * Get the parameter for this stat
     */
    public function parameter()
    {
        return $this->belongsTo(StatsParameter::class, 'stat_param_id');
    }
}
