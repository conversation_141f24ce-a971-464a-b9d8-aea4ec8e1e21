<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class PlayerSubscription extends Model
{
    protected $fillable = [
        'player_id',
        'subscription_plan_id',
        'stripe_subscription_id',
        'stripe_customer_id',
        'status',
        'trial_ends_at',
        'current_period_starts_at',
        'current_period_ends_at',
        'canceled_at',
        'ends_at',
    ];

    protected $casts = [
        'trial_ends_at' => 'datetime',
        'current_period_starts_at' => 'datetime',
        'current_period_ends_at' => 'datetime',
        'canceled_at' => 'datetime',
        'ends_at' => 'datetime',
    ];

    /**
     * Get the player that owns the subscription
     */
    public function player()
    {
        return $this->belongsTo(Player::class, 'player_id', 'player_id');
    }

    /**
     * Get the plan for this subscription
     */
    public function plan()
    {
        return $this->belongsTo(SubscriptionPlan::class, 'subscription_plan_id');
    }

    /**
     * Get the transactions for this subscription
     */
    public function transactions()
    {
        return $this->hasMany(SubscriptionTransaction::class);
    }

    /**
     * Check if the subscription is active
     */
    public function isActive()
    {
        return $this->status === 'active' && 
               ($this->ends_at === null || $this->ends_at->isFuture());
    }

    /**
     * Check if the subscription is canceled
     */
    public function isCanceled()
    {
        return $this->canceled_at !== null;
    }

    /**
     * Check if the subscription is on trial
     */
    public function onTrial()
    {
        return $this->trial_ends_at !== null && 
               $this->trial_ends_at->isFuture();
    }

    /**
     * Get active subscription for a player
     */
    public static function getActiveForPlayer($playerId)
    {
        return self::where('player_id', $playerId)
            ->where(function ($query) {
                $query->where('status', 'active')
                    ->where(function ($q) {
                        $q->whereNull('ends_at')
                            ->orWhere('ends_at', '>', Carbon::now());
                    });
            })
            ->first();
    }
}
