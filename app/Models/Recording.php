<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Recording extends Model
{
    protected $fillable = [
        'camera_id',
        'player_id',
        'date',
        'start_time',
        'end_time',
        'warm_area',
        'sex',
        'status',
        'time_debited',
    ];

    protected $casts = [
        'date' => 'date:Y-m-d',
        'start_time' => 'datetime:H:i:s',
        'end_time' => 'datetime:H:i:s',
        'warm_area' => 'integer'
    ];

    public function camera()
    {
        return $this->belongsTo(Camera::class, 'camera_id', 'camera_id');
    }

    public function player()
    {
        return $this->belongsTo(Player::class, 'player_id', 'player_id');
    }

    /**
     * Get the processed player stats for this recording
     */
    public function playerStats()
    {
        return $this->hasMany(PlayerStat::class);
    }

    /**
     * Get the coach comments for this recording
     */
    public function playerComments()
    {
        return $this->hasMany(PlayerComment::class);
    }
}
