<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class RejectedCounter extends Model
{
    protected $fillable = [
        'player_id',
        'club_id',
        'token_id',
        'rejected_at'
    ];

    protected $casts = [
        'rejected_at' => 'datetime'
    ];

    public function player()
    {
        return $this->belongsTo(Player::class, 'player_id', 'player_id');
    }

    public function club()
    {
        return $this->belongsTo(Club::class, 'club_id', 'club_id');
    }

    /**
     * Get the count of rejections for a player in the last 30 days
     *
     * @param string $playerId
     * @return int
     */
    public static function getRejectionsCountInLast30Days(string $playerId): int
    {
        return self::where('player_id', $playerId)
            ->where('rejected_at', '>=', now()->subDays(30))
            ->count();
    }
}
