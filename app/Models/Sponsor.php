<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Sponsor extends Model
{
    protected $primaryKey = 'sponsor_id';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'sponsor_id',
        'tax_id',
        'commercial_id',
        'company_name',
        'contact',
        'phone',
        'email',
        'postal_code',
        'country',
        'status',
        'logo'
    ];

    protected $casts = [
        'status' => 'integer',
    ];

    /**
     * Generate unique sponsor ID
     * Format: 6 characters alphanumeric (letters and numbers)
     */
    public static function generateSponsorId(): string
    {
        do {
            // Generate 6 random alphanumeric characters
            $sponsorId = strtoupper(Str::random(6));

            // Ensure at least one letter and one number
            while (!preg_match('/[A-Z]/', $sponsorId) || !preg_match('/[0-9]/', $sponsorId)) {
                $sponsorId = strtoupper(Str::random(6));
            }
        } while (self::where('sponsor_id', $sponsorId)->exists());

        return $sponsorId;
    }

    /**
     * Get the commercial that owns this sponsor
     */
    public function commercial()
    {
        return $this->belongsTo(Commercial::class, 'commercial_id', 'commercial_id');
    }

    /**
     * Get the country information
     */
    public function countryInfo()
    {
        return $this->belongsTo(Country::class, 'country', 'iso2');
    }

    /**
     * Get the logo URL
     */
    public function getLogoUrlAttribute()
    {
        if ($this->logo) {
            return asset('storage/' . $this->logo);
        }
        return null;
    }

    /**
     * Get status badge
     */
    public function getStatusBadgeAttribute()
    {
        return $this->status === 1 ? 'Active' : 'Inactive';
    }

    /**
     * Boot method to handle model events
     */
    protected static function boot()
    {
        parent::boot();

        static::created(function ($sponsor) {
            // Update sponsor count in stats
            \App\Models\Stat::incrementSponsors();
        });

        static::deleted(function ($sponsor) {
            // Update sponsor count in stats
            \App\Models\Stat::decrementSponsors();
        });
    }
}
