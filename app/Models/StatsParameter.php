<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class StatsParameter extends Model
{
    protected $fillable = [
        'sum_avg',
        'label',
        'label_fr',
        'label_es',
        'label_it',
    ];

    protected $casts = [
        'sum_avg' => 'integer',
    ];

    /**
     * Get the stats for this parameter
     */
    public function stats()
    {
        return $this->hasMany(PlayerStat::class, 'stat_param_id');
    }

    /**
     * Get the label based on the current locale
     */
    public function getLocalizedLabelAttribute()
    {
        $locale = app()->getLocale();

        return match($locale) {
            'fr' => $this->label_fr,
            'es' => $this->label_es,
            'it' => $this->label_it,
            default => $this->label,
        };
    }
}
