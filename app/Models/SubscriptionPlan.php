<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SubscriptionPlan extends Model
{
    protected $fillable = [
        'name',
        'country',
        'price',
        'currency',
        'description',
        'stripe_price_id',
        'is_active',
    ];

    protected $casts = [
        'price' => 'float',
        'is_active' => 'boolean',
    ];

    /**
     * Get the subscriptions for this plan
     */
    public function subscriptions()
    {
        return $this->hasMany(PlayerSubscription::class);
    }

    /**
     * Get formatted price with currency
     */
    public function getFormattedPriceAttribute()
    {
        return $this->price . ' ' . $this->currency;
    }

    /**
     * Get active plans for a specific country
     */
    public static function getActivePlansForCountry($country)
    {
        return self::where('country', $country)
            ->where('is_active', true)
            ->get();
    }

    /**
     * Get default plan for a country or create one if it doesn't exist
     */
    public static function getOrCreateDefaultPlan($country)
    {
        $plan = self::where('country', $country)
            ->where('is_active', true)
            ->first();

        if (!$plan) {
            // Determine price based on country
            $highPriceCountries = ['US', 'AE', 'SE']; // USA, Dubai, Sweden
            $price = in_array($country, $highPriceCountries) ? 15.00 : 6.00;
            $currency = in_array($country, $highPriceCountries) ? 'USD' : 'EUR';

            $plan = self::create([
                'name' => 'Monthly Subscription',
                'country' => $country,
                'price' => $price,
                'currency' => $currency,
                'description' => 'Monthly subscription for Padel Rating',
                'is_active' => true,
            ]);
        }

        return $plan;
    }
}
