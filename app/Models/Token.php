<?php

namespace App\Models;

use App\Services\FirebaseDatabaseService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\App;

class Token extends Model
{
    protected $primaryKey = 'token_id';
    public $incrementing = false;

    protected $fillable = [
        'token_id',
        'token_type_id',
        'club_id',
        'commercial_id',
        'player_id',
        'status',
        'is_requested_for_redeem',
        'purchased_at',
        'requested_at',
        'redeemed_requested_at',
        'generated_at',
        'used_at',
        'redeemed_at',
        'invoiced_at',
        'paid_at',
        'invoice_id',
        'payment_id',
    ];

    /**
     * The "booted" method of the model.
     */
    protected static function booted(): void
    {
        static::created(function ($token) {
            $token->updateFirebaseCounts();
        });

        static::updated(function ($token) {
            if ($token->isDirty('status') || $token->isDirty('is_requested_for_redeem')) {
                $token->updateFirebaseCounts();
            }
        });

        static::deleted(function ($token) {
            $token->updateFirebaseCounts();
        });
    }

    public function club()
    {
        return $this->belongsTo(Club::class, 'club_id', 'club_id');
    }

    public function player()
    {
        return $this->belongsTo(Player::class, 'player_id', 'player_id');
    }

    public function tokenType()
    {
        return $this->belongsTo(TokenType::class, 'token_type_id', 'id');
    }

    public function payment()
    {
        return $this->belongsTo(Payment::class, 'payment_id', 'payment_id');
    }

    public function commercial()
    {
        return $this->belongsTo(Commercial::class, 'commercial_id', 'commercial_id');
    }

    protected $casts = [
        'is_requested_for_redeem' => 'boolean',
        'id' => 'string'
    ];

    /**
     * Update Firebase counts for token requests and redeem requests
     */
    public function updateFirebaseCounts()
    {
        if (!$this->club_id) {
            return;
        }

        $firebaseService = App::make(FirebaseDatabaseService::class);

        // Get notification count
        $notificationCount = Notification::where('club_id', $this->club_id)
            ->where('is_read', false)
            ->count();

        // Get token request count
        $tokenRequestCount = self::where('club_id', $this->club_id)
            ->where('status', 'Requested')
            ->count();

        // Get redeem request count
        $redeemRequestCount = self::where('club_id', $this->club_id)
            ->where('status', 'Redeem-Requested')
            ->where('is_requested_for_redeem', true)
            ->count();

        $firebaseService->updateClubCounts(
            $this->club_id,
            $notificationCount,
            $tokenRequestCount,
            $redeemRequestCount
        );
    }

    /**
     * Get the invoice associated with this token
     */
    public function invoice()
    {
        return $this->belongsTo(InvoiceHeader::class, 'invoice_id', 'invoice_id');
    }
}
