<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TokenOrder extends Model
{
    protected $fillable = [
        'player_id',
        'club_id',
        'total_amount',
        'status',
        'tokens_data',
        'stripe_session_id',
    ];

    protected $casts = [
        'tokens_data' => 'array',
        'id' => 'string'
    ];

    public function player()
    {
        return $this->belongsTo(Player::class, 'player_id', 'player_id');
    }

    public function club()
    {
        return $this->belongsTo(Club::class, 'club_id', 'club_id');
    }
}
