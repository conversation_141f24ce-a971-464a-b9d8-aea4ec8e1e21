<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TokenType extends Model
{
    protected $fillable = [
        'club_id',
        'value',
        'value_in_eur',
        'minutes',
    ];

    protected $casts = [
        'value' => 'float',
        'id' => 'string'
    ];

    public function club()
    {
        return $this->belongsTo(Club::class, 'club_id', 'club_id');
    }
}
