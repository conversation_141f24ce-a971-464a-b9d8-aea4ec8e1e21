<?php

namespace App\Providers;

use App\Services\FirebaseDatabaseService;
use Illuminate\Support\ServiceProvider;
use Kreait\Firebase\Contract\Database;
use Kreait\Firebase\Factory;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->singleton(FirebaseDatabaseService::class, function () {
            return new FirebaseDatabaseService();
        });

        $this->app->singleton(Database::class, function () {
            $factory = (new Factory)
                ->withServiceAccount(env('FIREBASE_CREDENTIALS'));

            return $factory->createDatabase();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}
