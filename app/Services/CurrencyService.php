<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CurrencyService
{
    /**
     * The base URL for the ExchangeRate-API
     * 
     * @var string
     */
    protected $baseUrl = 'https://open.er-api.com/v6/latest/';

    /**
     * Cache time in minutes
     * 
     * @var int
     */
    protected $cacheTime = 60; // Cache for 1 hour

    /**
     * Convert a value from one currency to EUR
     * 
     * @param float $value
     * @param string $fromCurrency
     * @return float|null
     */
    public function convertToEur(float $value, string $fromCurrency): ?float
    {
        if (strtoupper($fromCurrency) === 'EUR') {
            return $value; // Already in EUR, no conversion needed
        }

        $rate = $this->getExchangeRate($fromCurrency, 'EUR');
        
        if ($rate === null) {
            return null;
        }

        return round($value * $rate, 2);
    }

    /**
     * Get the exchange rate from one currency to another
     * 
     * @param string $fromCurrency
     * @param string $toCurrency
     * @return float|null
     */
    public function getExchangeRate(string $fromCurrency, string $toCurrency): ?float
    {
        $fromCurrency = strtoupper($fromCurrency);
        $toCurrency = strtoupper($toCurrency);

        // If currencies are the same, return 1
        if ($fromCurrency === $toCurrency) {
            return 1.0;
        }

        // Try to get rates from cache first
        $cacheKey = "exchange_rates_{$fromCurrency}";
        $rates = Cache::remember($cacheKey, $this->cacheTime, function () use ($fromCurrency) {
            return $this->fetchExchangeRates($fromCurrency);
        });

        if (!$rates || !isset($rates[$toCurrency])) {
            Log::error("Failed to get exchange rate from {$fromCurrency} to {$toCurrency}");
            return null;
        }

        return $rates[$toCurrency];
    }

    /**
     * Fetch exchange rates from the API
     * 
     * @param string $baseCurrency
     * @return array|null
     */
    protected function fetchExchangeRates(string $baseCurrency): ?array
    {
        try {
            $response = Http::get($this->baseUrl . $baseCurrency);
            
            if ($response->successful()) {
                $data = $response->json();
                
                if (isset($data['rates'])) {
                    return $data['rates'];
                }
            }
            
            Log::error("Failed to fetch exchange rates for {$baseCurrency}: " . $response->body());
            return null;
        } catch (\Exception $e) {
            Log::error("Exception when fetching exchange rates: " . $e->getMessage());
            return null;
        }
    }
}
