<?php

namespace App\Services;

use <PERSON>reait\Firebase\Factory;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Notification;
use Kreait\Firebase\Exception\Messaging\NotFound;
use Kreait\Firebase\Exception\MessagingException;

class FCMService
{
    public static function sendNotification($token, $title, $body, $route = null)
    {
        try {
            $factory = (new Factory)->withServiceAccount(env('FIREBASE_CREDENTIALS'));

            $messaging = $factory->createMessaging();

            $notification = Notification::create($title, $body);

            $message = CloudMessage::new()
                ->withNotification($notification)
                ->withData([
                    'click_action' => 'FLUTTER_NOTIFICATION_CLICK',
                    'route' => $route ?? '',
                ])
                ->withChangedTarget('token', $token)
                ->withApnsConfig([
                    'payload' => [
                        'aps' => [
                            'alert' => [
                                'title' => $title,
                                'body'  => $body,
                            ],
                            'sound' => 'default',
                        ],
                    ],
                ]); // correct way to set target

            $response = $messaging->send($message);

            \Log::info('FCM notification sent successfully: ' . json_encode($response));
        } catch (NotFound $e) {
            \Log::error('Invalid FCM token: ' . $token);
        } catch (MessagingException $e) {
            \Log::error('FCM messaging error: ' . $e->getMessage());
        } catch (\Exception $e) {
            \Log::error('FCM general error: ' . $e->getMessage());
        }
    }
}
