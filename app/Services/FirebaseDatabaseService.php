<?php

namespace App\Services;

use Kreait\Firebase\Factory;
use Kreait\Firebase\Contract\Database;
use Illuminate\Support\Facades\Log;

class FirebaseDatabaseService
{
    protected $database;

    /**
     * Constructor with optional dependency injection
     */
    public function __construct(?Database $database = null)
    {
        if ($database) {
            $this->database = $database;
        } else {
            try {
                $factory = (new Factory)
                    ->withServiceAccount(env('FIREBASE_CREDENTIALS'))
                    ->withDatabaseUri(env('FIREBASE_DATABASE_URL'));

                $this->database = $factory->createDatabase();
            } catch (\Exception $e) {
                Log::error('Firebase database initialization error: ' . $e->getMessage());
            }
        }
    }

    /**
     * Update player notification count
     *
     * @param string $playerId
     * @param int $count
     * @return bool
     */
    public function updatePlayerNotificationCount(string $playerId, int $count): bool
    {
        try {
            $reference = $this->database->getReference('players/' . $playerId);
            $reference->set([
                'unread_count' => $count
            ]);

            Log::info('Player notification count updated in Firebase', [
                'player_id' => $playerId,
                'count' => $count
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to update player notification count in Firebase', [
                'player_id' => $playerId,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Update club notification counts
     *
     * @param string $clubId
     * @param int $notificationCount
     * @param int|null $tokenRequestCount
     * @param int|null $redeemRequestCount
     * @return bool
     */
    public function updateClubCounts(
        string $clubId,
        int $notificationCount,
        ?int $tokenRequestCount = null,
        ?int $redeemRequestCount = null
    ): bool {
        try {
            $data = [
                'unread_count' => $notificationCount
            ];

            if ($tokenRequestCount !== null) {
                $data['token_requests_count'] = $tokenRequestCount;
            }

            if ($redeemRequestCount !== null) {
                $data['redeem_requests_count'] = $redeemRequestCount;
            }

            $reference = $this->database->getReference('clubs/' . $clubId);
            $reference->set($data);

            Log::info('Club counts updated in Firebase', [
                'club_id' => $clubId,
                'data' => $data
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to update club counts in Firebase', [
                'club_id' => $clubId,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Get player notification count
     *
     * @param string $playerId
     * @return int
     */
    public function getPlayerNotificationCount(string $playerId): int
    {
        try {
            $reference = $this->database->getReference('players/' . $playerId . '/unread_count');
            $snapshot = $reference->getSnapshot();

            return $snapshot->exists() ? $snapshot->getValue() : 0;
        } catch (\Exception $e) {
            Log::error('Failed to get player notification count from Firebase', [
                'player_id' => $playerId,
                'error' => $e->getMessage()
            ]);

            return 0;
        }
    }

    /**
     * Get club notification counts
     *
     * @param string $clubId
     * @return array
     */
    public function getClubCounts(string $clubId): array
    {
        try {
            $reference = $this->database->getReference('clubs/' . $clubId);
            $snapshot = $reference->getSnapshot();

            if ($snapshot->exists()) {
                $data = $snapshot->getValue();
                return [
                    'unread_count' => $data['unread_count'] ?? 0,
                    'token_requests_count' => $data['token_requests_count'] ?? 0,
                    'redeem_requests_count' => $data['redeem_requests_count'] ?? 0
                ];
            }

            return [
                'unread_count' => 0,
                'token_requests_count' => 0,
                'redeem_requests_count' => 0
            ];
        } catch (\Exception $e) {
            Log::error('Failed to get club counts from Firebase', [
                'club_id' => $clubId,
                'error' => $e->getMessage()
            ]);

            return [
                'unread_count' => 0,
                'token_requests_count' => 0,
                'redeem_requests_count' => 0
            ];
        }
    }

    /**
     * Update player recording in Firebase
     *
     * @param string $playerId
     * @param int $recordingId
     * @param string $startTime
     * @param string $endTime
     * @param int $credits
     * @return bool
     */
    public function updatePlayerRecording(
        string $playerId,
        int $recordingId,
        string $startTime,
        string $endTime,
        int $credits
    ): bool {
        try {
            $reference = $this->database->getReference('players/' . $playerId . '/current_recording/');

            $reference->set([
                'recording_id' => $recordingId,
                'start_time' => $startTime,
                'end_time' => $endTime,
                'credits' => $credits,
                'is_started' => true
            ]);

            Log::info('Player recording updated in Firebase', [
                'player_id' => $playerId,
                'recording_id' => $recordingId
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to update player recording in Firebase', [
                'player_id' => $playerId,
                'recording_id' => $recordingId,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Clear player's current recording in Firebase
     *
     * @param string $playerId
     * @return bool
     */
    public function clearCurrentRecording(string $playerId): bool
    {
        try {
            $reference = $this->database->getReference('players/' . $playerId . '/current_recording');
            $reference->remove();

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to clear player current recording in Firebase', [
                'player_id' => $playerId,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }
}
