<?php

namespace App\Services;

use App\Models\Player;
use App\Models\PlayerSubscription;
use App\Models\SubscriptionPlan;
use App\Models\SubscriptionTransaction;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Stripe\StripeClient;
use Stripe\Exception\ApiErrorException;

class StripeSubscriptionService
{
    protected $stripe;

    public function __construct()
    {
        $this->stripe = new StripeClient(config('services.stripe.secret'));
    }

    /**
     * Create or get a Stripe customer for a player
     */
    public function getOrCreateCustomer(Player $player, $paymentMethodId = null)
    {
        try {
            $subscription = PlayerSubscription::where('player_id', $player->player_id)
                ->whereNotNull('stripe_customer_id')
                ->first();

            if ($subscription && $subscription->stripe_customer_id) {
                $customer = $this->stripe->customers->retrieve($subscription->stripe_customer_id);

                // Attach payment method if provided
                if ($paymentMethodId) {
                    $this->attachPaymentMethod($customer->id, $paymentMethodId);
                }

                return $customer;
            }

            // Create new customer
            $customerData = [
                'email' => $player->email,
                'name' => $player->first_name . ' ' . $player->last_name,
                'metadata' => [
                    'player_id' => $player->player_id
                ]
            ];

            if ($paymentMethodId) {
                $customerData['payment_method'] = $paymentMethodId;
            }

            return $this->stripe->customers->create($customerData);
        } catch (ApiErrorException $e) {
            Log::error('Stripe customer creation failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Attach a payment method to a customer
     */
    public function attachPaymentMethod($customerId, $paymentMethodId)
    {
        try {
            $this->stripe->paymentMethods->attach(
                $paymentMethodId,
                ['customer' => $customerId]
            );

            $this->stripe->customers->update($customerId, [
                'invoice_settings' => [
                    'default_payment_method' => $paymentMethodId
                ]
            ]);

            return true;
        } catch (ApiErrorException $e) {
            Log::error('Failed to attach payment method: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Create or get a Stripe price for a subscription plan
     */
    public function getOrCreatePrice(SubscriptionPlan $plan)
    {
        try {
            if ($plan->stripe_price_id) {
                return $this->stripe->prices->retrieve($plan->stripe_price_id);
            }

            // Create product if needed
            $productName = 'Padel Rating Subscription - ' . $plan->country;
            $product = $this->getOrCreateProduct($productName);

            // Create price
            $price = $this->stripe->prices->create([
                'product' => $product->id,
                'unit_amount' => (int)($plan->price * 100), // Convert to cents
                'currency' => strtolower($plan->currency),
                'recurring' => [
                    'interval' => 'month',
                ],
                'metadata' => [
                    'plan_id' => $plan->id
                ]
            ]);

            // Update plan with price ID
            $plan->update(['stripe_price_id' => $price->id]);

            return $price;
        } catch (ApiErrorException $e) {
            Log::error('Stripe price creation failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get or create a Stripe product
     */
    private function getOrCreateProduct($name)
    {
        try {
            // Try to find existing product
            $products = $this->stripe->products->all(['limit' => 100]);
            foreach ($products->data as $product) {
                if ($product->name === $name) {
                    return $product;
                }
            }

            // Create new product
            return $this->stripe->products->create([
                'name' => $name,
                'description' => 'Monthly subscription for Padel Rating',
            ]);
        } catch (ApiErrorException $e) {
            Log::error('Stripe product creation failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Create a subscription for a player
     */
    public function createSubscription(Player $player, SubscriptionPlan $plan, $paymentMethodId)
    {
        try {
            // Get or create customer
            $customer = $this->getOrCreateCustomer($player, $paymentMethodId);

            // Get or create price
            $price = $this->getOrCreatePrice($plan);

            // Create subscription
            $subscription = $this->stripe->subscriptions->create([
                'customer' => $customer->id,
                'items' => [
                    ['price' => $price->id],
                ],
                'payment_behavior' => 'default_incomplete',
                'payment_settings' => [
                    'save_default_payment_method' => 'on_subscription',
                ],
                'expand' => ['latest_invoice.payment_intent'],
                'metadata' => [
                    'player_id' => $player->player_id,
                    'plan_id' => $plan->id
                ]
            ]);

            // Create subscription record
            $playerSubscription = PlayerSubscription::create([
                'player_id' => $player->player_id,
                'subscription_plan_id' => $plan->id,
                'stripe_subscription_id' => $subscription->id,
                'stripe_customer_id' => $customer->id,
                'status' => $subscription->status,
                'current_period_starts_at' => $subscription->current_period_start ? Carbon::createFromTimestamp($subscription->current_period_start) : null,
                'current_period_ends_at' => $subscription->current_period_end ? Carbon::createFromTimestamp($subscription->current_period_end) : null,
            ]);

            // Create transaction record
            if (isset($subscription->latest_invoice) && isset($subscription->latest_invoice->payment_intent)) {
                SubscriptionTransaction::create([
                    'player_id' => $player->player_id,
                    'player_subscription_id' => $playerSubscription->id,
                    'amount' => $plan->price,
                    'currency' => $plan->currency,
                    'stripe_payment_intent_id' => $subscription->latest_invoice->payment_intent->id,
                    'stripe_invoice_id' => $subscription->latest_invoice->id,
                    'status' => $subscription->latest_invoice->payment_intent->status,
                ]);
            }

            return [
                'subscription' => $subscription,
                'player_subscription' => $playerSubscription,
                'client_secret' => isset($subscription->latest_invoice->payment_intent)
                    ? $subscription->latest_invoice->payment_intent->client_secret
                    : null
            ];
        } catch (ApiErrorException $e) {
            Log::error('Stripe subscription creation failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Cancel a subscription
     */
    public function cancelSubscription(PlayerSubscription $subscription, $cancelAtPeriodEnd = false)
    {
        try {
            if (!$subscription->stripe_subscription_id) {
                throw new \Exception('No Stripe subscription ID found');
            }

            $stripeSubscription = $this->stripe->subscriptions->retrieve($subscription->stripe_subscription_id);

            if ($cancelAtPeriodEnd) {
                // Update the subscription with cancel_at_period_end parameter
                $stripeSubscription = $this->stripe->subscriptions->update(
                    $subscription->stripe_subscription_id,
                    ['cancel_at_period_end' => true]
                );

                $subscription->update([
                    'canceled_at' => Carbon::now(),
                    'ends_at' => $stripeSubscription->current_period_end ? Carbon::createFromTimestamp($stripeSubscription->current_period_end) : null
                ]);
            } else {
                // Cancel the subscription immediately
                $stripeSubscription = $this->stripe->subscriptions->cancel(
                    $subscription->stripe_subscription_id
                );

                $subscription->update([
                    'status' => $stripeSubscription->status,
                    'canceled_at' => Carbon::now(),
                    'ends_at' => Carbon::now()
                ]);
            }

            return $subscription;
        } catch (ApiErrorException $e) {
            Log::error('Stripe subscription cancellation failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update a subscription's payment method
     */
    public function updatePaymentMethod(PlayerSubscription $subscription, $paymentMethodId)
    {
        try {
            if (!$subscription->stripe_customer_id) {
                throw new \Exception('No Stripe customer ID found');
            }

            $this->attachPaymentMethod($subscription->stripe_customer_id, $paymentMethodId);

            return true;
        } catch (ApiErrorException $e) {
            Log::error('Stripe payment method update failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Create a checkout session for subscription
     */
    public function createCheckoutSession(Player $player, SubscriptionPlan $plan, $successUrl, $cancelUrl)
    {
        try {
            // Get or create price
            $price = $this->getOrCreatePrice($plan);

            // Create checkout session
            $session = $this->stripe->checkout->sessions->create([
                'payment_method_types' => ['card'],
                'line_items' => [[
                    'price' => $price->id,
                    'quantity' => 1,
                ]],
                'mode' => 'subscription',
                'success_url' => $successUrl . '?session_id={CHECKOUT_SESSION_ID}',
                'cancel_url' => $cancelUrl,
                'customer_email' => $player->email,
                'metadata' => [
                    'player_id' => $player->player_id,
                    'plan_id' => $plan->id
                ]
            ]);

            return $session;
        } catch (ApiErrorException $e) {
            Log::error('Stripe checkout session creation failed: ' . $e->getMessage());
            throw $e;
        }
    }
}
