<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class TimezoneService
{
    /**
     * Get user timezone by IP address with caching
     *
     * @param string $ip
     * @return string
     */
    public function getUserTimezoneByIP($ip)
    {
        // Create cache key for this IP
        $cacheKey = "timezone_ip_{$ip}";

        // Try to get timezone from cache first (cached for 1 week)
        $cachedTimezone = Cache::get($cacheKey);

        if ($cachedTimezone) {
            return $cachedTimezone;
        }

        // If not in cache, fetch from API
        $timezone = $this->fetchTimezoneFromAPI($ip);

        // Cache the result for 1 week (7 * 24 * 60 minutes)
        Cache::put($cacheKey, $timezone, 7 * 24 * 60);

        return $timezone;
    }

    /**
     * Fetch timezone from IP info API
     *
     * @param string $ip
     * @return string
     */
    private function fetchTimezoneFromAPI($ip)
    {
        try {
            $ch = curl_init();

            // Set the URL
            curl_setopt($ch, CURLOPT_URL, "http://ipinfo.io/{$ip}/json");

            // Return the transfer as a string instead of outputting it directly
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

            // Set a timeout to avoid long waiting periods
            curl_setopt($ch, CURLOPT_TIMEOUT, 8);

            // Execute the request
            $response = curl_exec($ch);

            // Check for errors in the cURL request
            if (curl_errno($ch)) {
                // In case of error, log it if needed
                Log::warning("cURL error fetching timezone for IP {$ip}: " . curl_error($ch));
                curl_close($ch);
                return 'Europe/Madrid'; // Return default timezone if API call fails
            }

            // Close the cURL session
            curl_close($ch);

            // Decode the JSON response
            $data = json_decode($response, true);

            // Return the timezone or 'Europe/Madrid' if not found
            return $data['timezone'] ?? 'Europe/Madrid';
        } catch (\Exception $e) {
            // Log the exception if needed
            Log::error("Error fetching timezone for IP {$ip}: " . $e->getMessage());

            // Return default timezone in case of exception
            return 'Europe/Madrid';
        }
    }

    /**
     * Get user's IP address from request
     *
     * @return string
     */
    public function getUserIP()
    {
        // Check for various headers that might contain the real IP
        $ipKeys = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',            // Proxy
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxy
            'HTTP_FORWARDED',            // Proxy
            'REMOTE_ADDR'                // Standard
        ];

        foreach ($ipKeys as $key) {
            if (array_key_exists($key, $_SERVER) && !empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);

                // Validate IP address
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        // Fallback to REMOTE_ADDR even if it's private (for local development)
        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }

    /**
     * Check if a game has ended based on user's timezone
     *
     * @param \App\Models\Game $game
     * @param string $userTimezone
     * @return bool
     */
    public function hasGameEnded($game, $userTimezone)
    {
        try {
            $userNow = now($userTimezone);
            $gameDate = $game->date;
            $gameEndTime = $game->date . ' ' . $game->end_time;

            
            // If game date is in the past, game has ended
            if ($gameDate < $userNow->format('Y-m-d')) {
                return true;
            }
            
            // If game date is today, check if end time has passed
            if ($gameDate === $userNow->format('Y-m-d')) {
                return $gameEndTime < $userNow;
            }
            
            // If game date is in the future, game hasn't ended
            return false;
        } catch (\Exception $e) {
            Log::error("Error checking if game has ended: " . $e->getMessage());
            // In case of error, assume game has ended to be safe
            return true;
        }
    }

    /**
     * Clear timezone cache for a specific IP
     *
     * @param string $ip
     * @return bool
     */
    public function clearTimezoneCache($ip)
    {
        $cacheKey = "timezone_ip_{$ip}";
        return Cache::forget($cacheKey);
    }
}
