<?php

namespace App\Services;

use Twilio\Rest\Client;
use Illuminate\Support\Facades\Log;
use Twilio\Exceptions\TwilioException;

class TwilioService
{
    public Client $client;
    public string $fromNumber;
    public string $verifySid;

    public function __construct()
    {
        $this->client = new Client(
            config('services.twilio.sid'),
            config('services.twilio.auth_token')
        );
        $this->fromNumber = config('services.twilio.from');
        $this->verifySid = config('services.twilio.verify_sid');
    }

    /**
     * Send SMS message
     *
     * @param string $to
     * @param string $message
     * @return array
     */
    public static function sendSMS(string $to, string $message): array
    {
        $twilio = new TwilioService();

        try {
            // Normalize phone number
            $to = $twilio->normalizePhoneNumber($to);

            // Send message
            $message = $twilio->client->messages->create($to, [
                'from' => $twilio->fromNumber,
                'body' => $message
            ]);

            Log::info('SMS sent successfully', [
                'to' => $to,
                'message_sid' => $message->sid,
                'status' => $message->status
            ]);

            return [
                'success' => true,
                'message' => 'SMS sent successfully',
                'data' => [
                    'message_sid' => $message->sid,
                    'status' => $message->status
                ]
            ];

        } catch (TwilioException $e) {
            Log::error('Twilio SMS sending failed', [
                'error' => $e->getMessage(),
                'to' => $to
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Send verification code
     *
     * @param string $to
     * @param string $channel
     * @return array
     */
    public static function sendVerificationCode(string $to, string $channel = 'sms'): array
    {
        $twilio = new TwilioService();

        try {
            $to = $twilio->normalizePhoneNumber($to);

            $verification = $twilio->client->verify->v2
                ->services($twilio->verifySid)
                ->verifications
                ->create($to, $channel);

            Log::info('Verification code sent', [
                'to' => $to,
                'channel' => $channel,
                'status' => $verification->status
            ]);

            return [
                'success' => true,
                'message' => 'Verification code sent',
                'data' => [
                    'status' => $verification->status
                ]
            ];

        } catch (TwilioException $e) {
            Log::error('Verification code sending failed', [
                'error' => $e->getMessage(),
                'to' => $to
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Verify code
     *
     * @param string $to
     * @param string $code
     * @return array
     */
    public static function verifyCode(string $to, string $code): array
    {
        $twilio = new TwilioService();

        try {
            $to = $twilio->normalizePhoneNumber($to);

            $verification_check = $twilio->client->verify->v2
                ->services($twilio->verifySid)
                ->verificationChecks
                ->create([
                    'to' => $to,
                    'code' => $code
                ]);

            Log::info('Code verification attempt', [
                'to' => $to,
                'status' => $verification_check->status
            ]);

            return [
                'success' => $verification_check->status === 'approved',
                'message' => $verification_check->status === 'approved'
                    ? 'Verification successful'
                    : 'Invalid verification code',
                'data' => [
                    'status' => $verification_check->status
                ]
            ];

        } catch (TwilioException $e) {
            Log::error('Code verification failed', [
                'error' => $e->getMessage(),
                'to' => $to
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Normalize phone number
     *
     * @param string $phoneNumber
     * @return string
     */
    public function normalizePhoneNumber(string $phoneNumber): string
    {
        // Remove any non-numeric characters
        $phoneNumber = preg_replace('/[^0-9]/', '', $phoneNumber);

        // Add + prefix if not present
        if (!str_starts_with($phoneNumber, '+')) {
            $phoneNumber = '+' . $phoneNumber;
        }

        return $phoneNumber;
    }
}