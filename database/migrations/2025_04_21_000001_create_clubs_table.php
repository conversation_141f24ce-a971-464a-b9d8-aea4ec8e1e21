<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('clubs', function (Blueprint $table) {
            $table->string('club_id')->primary();
            $table->string('email')->unique();
            $table->string('club_name');
            $table->string('password');
            $table->string('contact')->nullable();
            $table->string('country_code')->nullable();
            $table->string('phone')->nullable();
            $table->string('started')->nullable();
            $table->string('tax_id')->nullable();
            $table->float('commission')->nullable();
            $table->float('commercial_commission')->nullable();
            $table->string('commercial_id')->nullable();
            $table->string('postal_code')->nullable();
            $table->string('country')->nullable();
            $table->string('city')->nullable();
            $table->boolean('sell_tokens')->default(false);
            $table->string('status')->default('Active');
            $table->string('fcm_token')->nullable();
            $table->string('currency')->default('USD')->nullable();
            $table->string('language')->nullable();
            $table->string('remember_token')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('clubs');
    }
};
