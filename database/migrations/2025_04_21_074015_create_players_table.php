<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('players', function (Blueprint $table) {
            $table->string('player_id')->primary();
            $table->string('email')->unique();
            $table->string('first_name');
            $table->string('last_name');
            $table->string('password');
            $table->string('started')->nullable();
            $table->string('sex')->nullable();
            $table->string('phone')->nullable();
            $table->string('country_code')->nullable();
            $table->string('postal_code')->nullable();
            $table->string('country')->nullable();
            $table->boolean('subscribed')->nullable();
            $table->integer('credits')->default(0);
            $table->integer('trial_credits')->default(0);
            $table->string('status')->default('Active');
            $table->string('rating_type')->nullable()->default('Playtomic');
            $table->string('photo')->nullable();
            $table->boolean('is_verified')->default(false);
            $table->boolean('is_email_verified')->default(false);
            $table->boolean('is_phone_verified')->default(false);
            $table->string('fcm_token')->nullable();
            $table->string('language')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('players');
    }
};
