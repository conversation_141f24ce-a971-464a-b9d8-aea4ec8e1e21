<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tokens', function (Blueprint $table) {
            $table->string('token_id')->primary();
            $table->foreignId('token_type_id')
                ->constrained('token_types')
                ->noActionOnUpdate()
                ->cascadeOnDelete();
            $table->string('club_id');
            $table->string('player_id')->nullable();
            $table->string('status')->default('Generated');
            $table->string('commercial_id')->nullable();
            $table->boolean('is_requested_for_redeem')->default(false);
            $table->string('purchased_at')->nullable();
            $table->string('requested_at')->nullable();
            $table->string('redeemed_requested_at')->nullable();
            $table->string('generated_at')->nullable();
            $table->string('used_at')->nullable();
            $table->string('redeemed_at')->nullable();
            $table->string('invoiced_at')->nullable();
            $table->string('paid_at')->nullable();
            $table->string('invoice_id', 6)->nullable();


            $table->foreign('club_id')
                ->references('club_id')
                ->on('clubs')
                ->noActionOnUpdate()
                ->noActionOnDelete();

            $table->foreign('player_id')
                ->references('player_id')
                ->on('players')
                ->noActionOnUpdate()
                ->noActionOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tokens');
    }
};
