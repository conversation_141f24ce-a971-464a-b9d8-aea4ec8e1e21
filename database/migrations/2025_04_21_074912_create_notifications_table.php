<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->string('title')->nullable();
            $table->string('body')->nullable();
            $table->string('club_id')->nullable();
            $table->string('player_id')->nullable();

            $table->foreign('club_id')
                ->references('club_id')
                ->on('clubs')
                ->noActionOnUpdate()
                ->cascadeOnDelete();
                
            $table->foreign('player_id')
                ->references('player_id')
                ->on('players')
                ->noActionOnUpdate()
                ->cascadeOnDelete();
            $table->boolean('is_read')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};
