<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('player_credits', function (Blueprint $table) {
            $table->id();
            $table->string('player_id');
            $table->string('club_id');
            $table->integer('credits')->default(0);

            $table->foreign('player_id')
                ->references('player_id')
                ->on('players')
                ->noActionOnUpdate()
                ->cascadeOnDelete();
                
            $table->foreign('club_id')
                ->references('club_id')
                ->on('clubs')
                ->noActionOnUpdate()
                ->cascadeOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('player_credits');
    }
};
