<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('player_otp_verifications', function (Blueprint $table) {
            $table->id();
            $table->string('player_id');
            $table->string('otp');
            $table->boolean('is_phone_otp')->default(false);
            $table->timestamp('expires_at');

            $table->foreign('player_id')
                ->references('player_id')
                ->on('players')
                ->noActionOnUpdate()
                ->cascadeOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('player_otp_verifications');
    }
};
