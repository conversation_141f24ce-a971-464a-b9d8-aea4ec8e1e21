<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('token_orders', function (Blueprint $table) {
            $table->id();
            $table->string('player_id');
            $table->string('club_id');
            $table->decimal('total_amount', 10, 2);
            $table->string('status');
            $table->json('tokens_data');
            $table->string('stripe_session_id')->nullable();

            $table->foreign('player_id')
                ->references('player_id')
                ->on('players')
                ->noActionOnUpdate()
                ->cascadeOnDelete();

            $table->foreign('club_id')
                ->references('club_id')
                ->on('clubs')
                ->noActionOnUpdate()
                ->cascadeOnDelete();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('token_orders');
    }
};
