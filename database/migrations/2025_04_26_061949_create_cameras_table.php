<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cameras', function (Blueprint $table) {
            $table->string('camera_id')->primary();
            $table->string('club_id');
            
            $table->foreign('club_id')
                ->references('club_id')
                ->on('clubs')
                ->noActionOnUpdate()
                ->cascadeOnDelete();

            $table->integer('court_number')->nullable();
            $table->string('ip')->nullable();
            $table->integer('port')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cameras');
    }
};
