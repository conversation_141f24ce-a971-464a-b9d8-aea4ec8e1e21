<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('recordings', function (Blueprint $table) {
            $table->id();
            $table->string('camera_id');
            $table->foreign('camera_id')
                ->references('camera_id')
                ->on('cameras')
                ->noActionOnUpdate()
                ->cascadeOnDelete();

            $table->string('player_id');
            $table->foreign('player_id')
                ->references('player_id')
                ->on('players')
                ->noActionOnUpdate()
                ->cascadeOnDelete();

            $table->date('date');
            $table->time('start_time')->nullable();
            $table->time('end_time')->nullable();
            $table->integer('warm_area')->nullable();
            $table->string('sex', 50)->nullable();
            $table->string('status')->default('started')->nullable()->comment('started, completed, failed');
            $table->boolean('time_debited')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('recordings');
    }
};
