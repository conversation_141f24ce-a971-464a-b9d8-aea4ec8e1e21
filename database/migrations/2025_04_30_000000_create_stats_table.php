<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stats', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('nbr_users')->default(0);
            $table->unsignedBigInteger('nbr_subscribed')->default(0);
            $table->unsignedInteger('nbr_clubs')->default(0);
            $table->unsignedSmallInteger('nbr_countries')->default(0);
            $table->unsignedInteger('nbr_sponsors')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stats');
    }
};
