<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('commercials', function (Blueprint $table) {
            $table->string('commercial_id', 6)->primary();
            $table->timestamp('inserted')->useCurrent();
            $table->string('tax_id', 12)->nullable();
            $table->string('name', 50);
            $table->string('phone', 15)->nullable();
            $table->string('email', 30)->unique();
            $table->string('postal_code', 10)->nullable();
            $table->string('country', 50)->nullable();
            $table->tinyInteger('status')->default(1); // 1 = Active, 0 = Inactive
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('commercials');
    }
};
