<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('clubs', function (Blueprint $table) {
            // First drop the existing column if it exists
            if (Schema::hasColumn('clubs', 'commercial_id')) {
                $table->dropColumn('commercial_id');
            }
            
            // Then add it back as a foreign key
            $table->string('commercial_id', 6)->nullable()->after('commission');
            $table->foreign('commercial_id')
                  ->references('commercial_id')
                  ->on('commercials')
                  ->onDelete('set null');
        });
    }

    public function down(): void
    {
        Schema::table('clubs', function (Blueprint $table) {
            // Drop the foreign key constraint
            $table->dropForeign(['commercial_id']);
            
            // Keep the column but remove the foreign key constraint
            $table->string('commercial_id')->nullable()->change();
        });
    }
};
