<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('rejected_counters', function (Blueprint $table) {
            $table->id();
            $table->string('player_id');
            $table->string('club_id');
            $table->string('token_id')->nullable();
            $table->timestamp('rejected_at');
            $table->timestamps();

            $table->foreign('player_id')
                ->references('player_id')
                ->on('players')
                ->noActionOnUpdate()
                ->cascadeOnDelete();

            $table->foreign('club_id')
                ->references('club_id')
                ->on('clubs')
                ->noActionOnUpdate()
                ->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rejected_counters');
    }
};
