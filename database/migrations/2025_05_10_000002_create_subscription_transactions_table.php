<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_transactions', function (Blueprint $table) {
            $table->id();
            $table->string('player_id');
            $table->foreignId('player_subscription_id')->constrained('player_subscriptions');
            $table->decimal('amount', 8, 2);
            $table->string('currency', 3);
            $table->string('stripe_payment_intent_id')->nullable();
            $table->string('stripe_invoice_id')->nullable();
            $table->string('status');
            $table->timestamp('paid_at')->nullable();
            $table->timestamps();

            $table->foreign('player_id')
                ->references('player_id')
                ->on('players')
                ->noActionOnUpdate()
                ->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_transactions');
    }
};
