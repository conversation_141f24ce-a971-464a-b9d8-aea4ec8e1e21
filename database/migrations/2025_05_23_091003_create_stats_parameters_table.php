<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stats_parameters', function (Blueprint $table) {
            $table->id();
            $table->tinyInteger('sum_avg')->comment('1 = sum/avg; 2 = percentage/value only');
            $table->string('label')->comment('English label');
            $table->string('label_fr')->comment('French label');
            $table->string('label_es')->comment('Spanish label');
            $table->string('label_it')->comment('Italian label');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stats_parameters');
    }
};
