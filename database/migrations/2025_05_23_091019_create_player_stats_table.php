<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('player_stats', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('game_id');
            $table->string('player_id')->nullable();
            $table->unsignedBigInteger('stat_param_id');
            $table->decimal('total', 8, 2)->default(0);
            $table->decimal('success', 8, 2)->default(0);
            $table->decimal('winner', 8, 2)->default(0);
            $table->decimal('loss', 8, 2)->default(0);
            $table->timestamps();

            $table->foreign('game_id')->references('id')->on('games')->onDelete('cascade');
            $table->foreign('player_id')->references('player_id')->on('players')->onDelete('set null');
            $table->foreign('stat_param_id')->references('id')->on('stats_parameters')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('player_stats');
    }
};
