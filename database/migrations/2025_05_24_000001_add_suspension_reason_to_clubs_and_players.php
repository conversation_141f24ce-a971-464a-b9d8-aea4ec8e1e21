<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('clubs', function (Blueprint $table) {
            $table->string('suspension_reason', 254)->nullable()->after('status');
        });

        Schema::table('players', function (Blueprint $table) {
            $table->string('suspension_reason', 254)->nullable()->after('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('clubs', function (Blueprint $table) {
            $table->dropColumn('suspension_reason');
        });

        Schema::table('players', function (Blueprint $table) {
            $table->dropColumn('suspension_reason');
        });
    }
};
