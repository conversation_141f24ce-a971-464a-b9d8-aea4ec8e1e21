<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_headers', function (Blueprint $table) {
            $table->string('invoice_id', 6)->primary();
            $table->string('invoice_nbr', 10)->unique();
            $table->string('club_id');
            $table->date('date');
            $table->decimal('value', 10, 2);
            $table->string('currency', 3);
            $table->tinyInteger('status')->default(0); // 0 = Outstanding, 1 = Paid
            $table->tinyInteger('pay_type')->default(0); // 0 = Wire, 1 = Cash, 2 = Other
            $table->timestamps();

            $table->foreign('club_id')
                ->references('club_id')
                ->on('clubs')
                ->noActionOnUpdate()
                ->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_headers');
    }
};
