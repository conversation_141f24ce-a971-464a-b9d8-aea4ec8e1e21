<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_lines', function (Blueprint $table) {
            $table->id();
            $table->string('invoice_id', 6);
            $table->integer('line_nbr');
            $table->text('description');
            $table->integer('qty');
            $table->decimal('value', 10, 2);
            $table->timestamps();

            $table->foreign('invoice_id')
                ->references('invoice_id')
                ->on('invoice_headers')
                ->noActionOnUpdate()
                ->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_lines');
    }
};
