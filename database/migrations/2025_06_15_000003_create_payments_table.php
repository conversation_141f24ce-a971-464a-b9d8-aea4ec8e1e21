<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->string('payment_id', 6)->primary();
            $table->string('payment_nbr', 10)->unique();
            $table->tinyInteger('type')->default(1); // 1 = Club, 2 = Commercial
            $table->string('type_id', 6); // club_id or commercial_id
            $table->date('date');
            $table->decimal('value', 10, 2);
            $table->string('currency', 2)->default('EU');
            $table->tinyInteger('pay_type')->default(0); // 0 = Wire, 1 = Cash
            $table->timestamps();

            // Add index for type_id for better performance
            $table->index(['type', 'type_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
