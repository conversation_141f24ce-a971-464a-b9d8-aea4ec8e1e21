<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sponsors', function (Blueprint $table) {
            $table->string('sponsor_id', 6)->primary();
            $table->timestamps(); // This will create created_at (instead of started)
            $table->string('tax_id', 12)->nullable();
            $table->string('commercial_id', 6);
            $table->string('company_name', 50);
            $table->string('contact', 50)->nullable();
            $table->string('phone', 15)->nullable();
            $table->string('email', 30);
            $table->string('postal_code', 10)->nullable();
            $table->string('country', 2)->nullable(); // ISO2 country code
            $table->tinyInteger('status')->default(1); // 1 = Active, 0 = Inactive
            $table->string('logo')->nullable(); // Image path

            // Foreign key constraint
            $table->foreign('commercial_id')
                  ->references('commercial_id')
                  ->on('commercials')
                  ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sponsors');
    }
};
