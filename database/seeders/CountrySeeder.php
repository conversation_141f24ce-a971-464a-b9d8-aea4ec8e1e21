<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CountrySeeder extends Seeder
{
    public function run(): void
    {
        $sqlPath = database_path('seeders/sql/countries.sql');
        $sql = file_get_contents($sqlPath);

        // Extract values part from SQL
        preg_match_all('/\((.*?)\)(?:,|;)/s', $sql, $matches);

        $columns = [
            'id', 'name', 'iso3', 'iso2', 'phonecode', 'capital', 'currency',
            'currency_name', 'currency_symbol', 'tld', 'timezones', 'translations',
            'latitude', 'longitude', 'emoji', 'emojiU', 'created_at',
            'updated_at', 'flag', 'wikiDataId'
        ];

        $rows = [];

        foreach ($matches[1] as $rowString) {
            $values = str_getcsv($rowString, ',', "'");

            $prepared = [];
            foreach ($columns as $i => $column) {
                $value = isset($values[$i]) ? trim($values[$i]) : null;

                // Strip N'...' or regular '...'
                if (preg_match("/^N?'(.*)'$/s", $value, $valMatch)) {
                    $value = stripslashes($valMatch[1]);
                } elseif (strtoupper($value) === 'NULL') {
                    $value = null;
                }

                $prepared[$column] = $value;
            }

            $rows[] = $prepared;
        }

        // 👇 Chunk insert to stay under SQL Server 2100 param limit
        $maxParams = 2000;
        $columnsCount = count($columns);
        $chunkSize = intdiv($maxParams, $columnsCount); // e.g. 2000 / 20 = 100

        foreach (array_chunk($rows, $chunkSize) as $chunk) {
            DB::table('countries')->insert($chunk);
        }
    }
}
