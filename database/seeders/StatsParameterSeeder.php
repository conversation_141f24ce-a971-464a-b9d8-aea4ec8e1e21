<?php

namespace Database\Seeders;

use App\Models\StatsParameter;
use Illuminate\Database\Seeder;

class StatsParameterSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $statsParameters = [
            [
                'sum_avg' => 1,
                'label' => 'HITS',
                'label_fr' => 'COUPS',
                'label_es' => 'GOLPES',
                'label_it' => 'COLPI',
            ],
            [
                'sum_avg' => 1,
                'label' => 'FOREHAND',
                'label_fr' => 'COUP DROIT',
                'label_es' => 'DERECHA',
                'label_it' => 'DRITTO',
            ],
            [
                'sum_avg' => 1,
                'label' => 'BACKHAND',
                'label_fr' => 'REVERS',
                'label_es' => 'REVÉS',
                'label_it' => 'ROVESCIO',
            ],
            [
                'sum_avg' => 1,
                'label' => 'CROSS COURT',
                'label_fr' => 'TRAVERSÉ',
                'label_es' => 'CRUZADO',
                'label_it' => 'INCROCIATO',
            ],
            [
                'sum_avg' => 1,
                'label' => 'DOWN THE LINE',
                'label_fr' => 'LE LONG DE LA LIGNE',
                'label_es' => 'A LO LARGO DE LA LÍNEA',
                'label_it' => 'LUNGO LA LINEA',
            ],
            [
                'sum_avg' => 1,
                'label' => 'FROM BASE AREA',
                'label_fr' => '→ LA BASE',
                'label_es' => '→E LA BASE',
                'label_it' => '→ AREA DI BASE',
            ],
            [
                'sum_avg' => 1,
                'label' => 'FROM NET AREA',
                'label_fr' => '→ ZONE DU FILET',
                'label_es' => '→ ZONE DE LA RED',
                'label_it' => '→ AREA DI RETE',
            ],
            [
                'sum_avg' => 1,
                'label' => 'FROM TRANSITION AREA',
                'label_fr' => '→ ZONE DE TRANSITION',
                'label_es' => '→ AREA DE TRANSICIÓN',
                'label_it' => '→ AREA DI TRANSIZIONE',
            ],
            [
                'sum_avg' => 1,
                'label' => 'SMACH',
                'label_fr' => 'SMACH',
                'label_es' => 'SMACH',
                'label_it' => 'SMACH',
            ],
            [
                'sum_avg' => 1,
                'label' => 'VOLLEY',
                'label_fr' => 'VOLÉE',
                'label_es' => 'VOLEA',
                'label_it' => 'VOLÉE',
            ],
            [
                'sum_avg' => 1,
                'label' => 'GROUND STROKE',
                'label_fr' => 'COUP DE FOND',
                'label_es' => 'GOLPE DE FONDO',
                'label_it' => 'COLPO DA FONDO',
            ],
            [
                'sum_avg' => 1,
                'label' => 'LOB',
                'label_fr' => 'LOB',
                'label_es' => 'LOB',
                'label_it' => 'LOB',
            ],
            [
                'sum_avg' => 1,
                'label' => 'VIBORA',
                'label_fr' => 'VIBORA',
                'label_es' => 'VIBORA',
                'label_it' => 'VIBORA',
            ],
            [
                'sum_avg' => 1,
                'label' => 'BANDEJA',
                'label_fr' => 'BANDEJA',
                'label_es' => 'BANDEJA',
                'label_it' => 'BANDEJA',
            ],
            [
                'sum_avg' => 1,
                'label' => 'SERVE',
                'label_fr' => 'SERVICE',
                'label_es' => 'SAQUE',
                'label_it' => 'SERVIZIO',
            ],
            [
                'sum_avg' => 1,
                'label' => 'CHIQUITA',
                'label_fr' => 'CHIQUITA',
                'label_es' => 'CHIQUITA',
                'label_it' => 'CHIQUITA',
            ],
            [
                'sum_avg' => 1,
                'label' => 'WALL SHOT',
                'label_fr' => 'REBOND',
                'label_es' => 'PARED',
                'label_it' => 'PARETE',
            ],
            [
                'sum_avg' => 1,
                'label' => 'DROP SHOT',
                'label_fr' => 'AMORTI',
                'label_es' => 'DEJADA',
                'label_it' => 'SMORZATA',
            ],
            [
                'sum_avg' => 1,
                'label' => 'DISTANCE',
                'label_fr' => 'DISTANCE',
                'label_es' => 'DISTANCIA',
                'label_it' => 'DISTANZA',
            ],
            [
                'sum_avg' => 2,
                'label' => 'BASE AREA %',
                'label_fr' => '% ZONE BASE',
                'label_es' => '% EN LA BASE',
                'label_it' => '% AREA DI BASE',
            ],
            [
                'sum_avg' => 2,
                'label' => 'TRANSITION AREA %',
                'label_fr' => 'ZONE DE TRANSITION %',
                'label_es' => 'ÁREA DE TRANSICIÓN %',
                'label_it' => 'AREA DI TRANSIZIONE %',
            ],
            [
                'sum_avg' => 2,
                'label' => 'NET AREA %',
                'label_fr' => 'ZONE DU FILET %',
                'label_es' => '% AREA DE LA RED',
                'label_it' => '% AREA DI RETE %',
            ],
            [
                'sum_avg' => 2,
                'label' => 'RATING',
                'label_fr' => 'CLASSEMENT',
                'label_es' => 'VALUACION',
                'label_it' => 'VALUTAZIONE',
            ],
        ];

        foreach ($statsParameters as $parameter) {
            StatsParameter::create($parameter);
        }
    }
}
