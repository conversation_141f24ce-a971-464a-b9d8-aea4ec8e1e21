<?php

namespace Database\Seeders;

use App\Models\SubscriptionPlan;
use App\Services\StripeSubscriptionService;
use Illuminate\Database\Seeder;

class SubscriptionPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $stripeService = new StripeSubscriptionService();
        
        // Default plans for standard countries (6 EUR)
        $standardCountries = ['ES', 'FR', 'IT', 'PT', 'DE', 'NL', 'BE'];
        
        foreach ($standardCountries as $country) {
            $plan = SubscriptionPlan::create([
                'name' => 'Monthly Subscription',
                'country' => $country,
                'price' => 6.00,
                'currency' => 'EUR',
                'description' => 'Monthly subscription for Padel Rating',
                'is_active' => true,
            ]);
            
            // Create Stripe price
            $stripeService->getOrCreatePrice($plan);
        }
        
        // Premium plans for expensive countries (15 USD)
        $premiumCountries = ['US', 'AE', 'SE']; // USA, Dubai, Sweden
        
        foreach ($premiumCountries as $country) {
            $plan = SubscriptionPlan::create([
                'name' => 'Monthly Subscription',
                'country' => $country,
                'price' => 15.00,
                'currency' => 'USD',
                'description' => 'Monthly subscription for Padel Rating',
                'is_active' => true,
            ]);
            
            // Create Stripe price
            $stripeService->getOrCreatePrice($plan);
        }
    }
}
