<?php

namespace Database\Seeders;

use App\Models\TokenType;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class TokenTypesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Insert Token Types - Use insert() for multiple records
        TokenType::insert([
            [
                'reference' => 1,
                'value' => 1.25,
                'minutes' => 60  // €1.25 for 1 hour
            ],
            [
                'reference' => 2,
                'value' => 0.75,
                'minutes' => 30  // €0.75 for 30 minutes
            ]
        ]);
    }
}
