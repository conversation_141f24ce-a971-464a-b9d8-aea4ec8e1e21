<?php

namespace Database\Seeders;

use App\Models\TokenType;
use Illuminate\Database\Seeder;

class TokenTypesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tokenTypes = [
            // Club 1 (6KS8WO) - USD tokens
            [
                'club_id' => '6KS8WO',
                'value' => 0.75,
                'minutes' => 30,
                'currency' => 'USD',
                'created_at' => '2025-04-15 09:31:29',
                'updated_at' => '2025-04-15 09:31:29'
            ],
            [
                'club_id' => '6KS8WO',
                'value' => 1.25,
                'minutes' => 60,
                'currency' => 'USD',
                'created_at' => '2025-04-15 09:31:29',
                'updated_at' => '2025-04-15 09:31:29'
            ],

            // Club 2 (YACUN1) - EUR tokens
            [
                'club_id' => 'YACUN1',
                'value' => 0.80,
                'minutes' => 30,
                'currency' => 'EUR',
                'created_at' => '2025-04-15 09:32:23',
                'updated_at' => '2025-04-15 09:32:23'
            ],
            [
                'club_id' => 'YACUN1',
                'value' => 1.30,
                'minutes' => 60,
                'currency' => 'EUR',
                'created_at' => '2025-04-15 09:32:23',
                'updated_at' => '2025-04-15 09:32:23'
            ],

            // Club 3 (RDQC4S) - USD tokens
            [
                'club_id' => 'RDQC4S',
                'value' => 0.80,
                'minutes' => 30,
                'currency' => 'USD',
                'created_at' => '2025-04-15 09:32:27',
                'updated_at' => '2025-04-15 09:32:27'
            ],
            [
                'club_id' => 'RDQC4S',
                'value' => 1.45,
                'minutes' => 60,
                'currency' => 'USD',
                'created_at' => '2025-04-15 09:32:27',
                'updated_at' => '2025-04-15 09:32:27'
            ],
        ];

        foreach ($tokenTypes as $tokenType) {
            TokenType::create($tokenType);
        }
    }
}
