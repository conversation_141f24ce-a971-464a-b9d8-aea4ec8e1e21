<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Token;
use Illuminate\Support\Str;
use Carbon\Carbon;

class TokensSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Tokens - Use insert() for multiple records
        Token::insert([
            [
                'token_id' => Str::random(8),
                'value' => 1.25,
                'minutes' => 60,
                'club_id' => 1,
                'commercial_id' => null,
                'status' => "Generated",
                'purchased_at' => null,
                'paid_at' => null,
                'redeemed_at' => null,
                'used_at' => null,
                'invoice' => null,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ],
            [
                'token_id' => Str::random(8),
                'value' => 0.75,
                'minutes' => 30,
                'club_id' => 1,
                'commercial_id' => null,
                'status' => "Generated",
                'purchased_at' => null,
                'paid_at' => null,
                'redeemed_at' => null,
                'used_at' => null,
                'invoice' => null,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]
        ]);

    }
}
