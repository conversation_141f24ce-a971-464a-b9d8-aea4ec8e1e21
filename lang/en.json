{"Email already exists": "Email already exists", "Account created successfully. Please verify your account.": "Account created successfully. Please verify your account.", "Account is not verified. Please verify your account. OTP sent to your email.": "Account is not verified. Please verify your account. OTP sent to your email.", "Login successful": "Login successful", "Failed to login due to": "Failed to login due to", "Failed to reset password due to": "Failed to reset password due to", "Failed to change password": "Failed to change password", "Failed to fetch profile": "Failed to fetch profile", "Failed to send OTP": "Failed to send OTP", "Failed to send OTP due to": "Failed to send OTP due to", "OTP sent successfully": "OTP sent successfully", "Failed to resend OTP due to": "Failed to resend OTP due to", "If an account exists with this email, you will receive a verification code.": "If an account exists with this email, you will receive a verification code.", "Password reset code has been sent to your email": "Password reset code has been sent to your email", "Failed to send verification code due to": "Failed to send verification code due to", "Profile updated successfully": "Profile updated successfully", "Failed to update profile": "Failed to update profile", "Profile image updated successfully": "Profile image updated successfully", "Failed to update profile image": "Failed to update profile image", "Incorrect email or password": "Incorrect email or password", "Account is not active": "Account is not active", "Current password is incorrect": "Current password is incorrect", "Password changed successfully": "Password changed successfully", "Successfully logged out": "Successfully logged out", "Token has expired": "Token has expired", "Token is invalid": "<PERSON><PERSON> is invalid", "FCM token updated successfully": "FCM token updated successfully", "Failed to update FCM token": "Failed to update FCM token", "Failed to fetch notifications": "Failed to fetch notifications", "Club not found": "Club not found", "Club added to favorites": "Club added to favorites", "Club removed from favorites": "Club removed from favorites", "Failed to update favorite status": "Failed to update favorite status", "Failed to fetch favorite clubs": "Failed to fetch favorite clubs", "Tokens payload must be an array": "Tokens payload must be an array", "Tokens must not be empty": "Tokens must not be empty", "Failed to send token request": "Failed to send token request", "Failed to create payment session due to": "Failed to create payment session due to", "Token requests sent successfully": "Token requests sent successfully", "Some token requests failed to send": "Some token requests failed to send", "Failed to process token requests": "Failed to process token requests", "Failed to fetch tokens": "Failed to fetch tokens", "Token not found": "Token not found", "Unauthorized. This token does not belong to you": "Unauthorized. This token does not belong to you", "Token used successfully": "Token used successfully", "Token redeemed requested successfully": "Token redeemed requested successfully", "Payload must be an array": "Payload must be an array", "All tokens created successfully": "All tokens created successfully", "Some tokens failed to create": "Some tokens failed to create", "Failed to create token": "Failed to create token", "Failed to process tokens": "Failed to process tokens", "Token not found or does not belong to your club": "Token not found or does not belong to your club", "Token accepted successfully": "<PERSON><PERSON> accepted successfully", "Failed to update token status": "Failed to update token status", "Token rejected successfully": "Token rejected successfully", "Token redeemed successfully": "To<PERSON> redeemed successfully", "Failed to redeem token": "Failed to redeem token", "Token already used": "Token already used", "Token already redeemed": "Token already redeemed", "Token is not in valid status": "Token is not in valid status", "Token request expired": "Token request expired", "Failed to fetch clubs": "Failed to fetch clubs", "Payment successful": "Payment successful", "Payment cancelled": "Payment cancelled", "Token order processed successfully": "Token order processed successfully", "Failed to fetch token types": "Failed to fetch token types", "Token type not found": "Token type not found", "Token type updated successfully": "Token type updated successfully", "Token type created successfully": "Token type created successfully", "Failed to process token type": "Failed to process token type", "Token Redeemed": "<PERSON><PERSON> Redeemed", "Your token": "Your token", "has been redeemed by": "has been redeemed by", "Token Redeemed Rejected": "<PERSON><PERSON> Redeemed Rejected", "redeem request has been rejected by": "redeem request has been rejected by", "Token Redeem Rejected": "<PERSON><PERSON> Rejected", "Token Accepted": "<PERSON><PERSON> Accepted", "has been accepted by": "has been accepted by", "Token Rejected": "<PERSON><PERSON> Rejected", "has been rejected by": "has been rejected by", "Language updated successfully": "Language updated successfully", "Invalid request": "Invalid request", "Order not found": "Order not found", "Payment is being processed": "Payment is being processed", "Payment failed": "Payment failed", "Invalid order status": "Invalid order status", "Failed to check payment status": "Failed to check payment status", "Token Order Completed": "Token Order Completed", "Your token order has been completed": "Your token order has been completed", "Unauthorized. This token does not belong to you.": "Unauthorized. This token does not belong to you.", "Token already used.": "Token already used.", "Token already redeemed.": "To<PERSON> already redeemed.", "Token is not in valid status.": "Token is not in valid status.", "Token request expired.": "Token request expired.", "Failed to update token status.": "Failed to update token status.", "Failed to redeem token.": "Failed to redeem token.", "Failed to process token order.": "Failed to process token order.", "Token not found.": "To<PERSON> not found.", "Account deleted successfully": "Account deleted successfully", "Failed to delete account ": "Failed to delete account ", "Player not found": "Player not found", "Your Padel Rating APP verification OTP is ": "Your Padel Rating APP verification OTP is ", ". This code will expire in 30 minutes.": ". This code will expire in 30 minutes.", "Failed to send OTP due to ": "Failed to send OTP due to ", "Invalid OTP": "Invalid OTP", "OTP has expired": "OTP has expired", "Phone number verified successfully": "Phone number verified successfully", "Failed to verify OTP due to ": "Failed to verify OTP due to ", "Camera not found": "Camera not found", "Camera added successfully": "Camera added successfully", "Camera updated successfully": "Camera updated successfully", "Failed to add camera ": "Failed to add camera ", "Failed to update camera ": "Failed to update camera ", "Recording added successfully": "Recording added successfully", "Failed to add recording ": "Failed to add recording ", "You can only add 4 cameras": "You can only add 4 cameras", "Recording not found": "Recording not found", "Recording ended successfully": "Recording ended successfully", "Failed to end recording ": "Failed to end recording ", "You do not have enough credits to start recording. Please purchase more credits.": "You do not have enough credits to start recording. Please purchase more credits.", "You do not have enough credits for this club. Please purchase more credits.": "You do not have enough credits for this club. Please purchase more credits.", "This camera is already in use. Please try again later.": "This camera is already in use. Please try again later.", "Failed to check camera ": "Failed to check camera ", "Failed to check recording ": "Failed to check recording ", "Account is already verified. Please login.": "Account is already verified. Please login.", "The phone number is already registered": "The phone number is already registered", "You're unable to create Tokens while you have other requests are outstanding": "You're unable to create Tokens while you have other requests are outstanding", "Your account has been suspended due to multiple token rejections. Please contact support for assistance.": "Your account has been suspended due to multiple token rejections. Please contact support for assistance.", "Recording deleted successfully": "Recording deleted successfully", "Failed to delete recording ": "Failed to delete recording "}