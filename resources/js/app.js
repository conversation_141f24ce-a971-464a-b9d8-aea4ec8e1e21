// Import ApexCharts
import 'https://cdn.jsdelivr.net/npm/apexcharts';

// Dark mode detection for charts
window.updateChartTheme = function() {
    const isDarkMode = document.documentElement.classList.contains('dark');

    // Dispatch event for charts to update
    window.dispatchEvent(new CustomEvent('theme-changed', {
        detail: { isDarkMode }
    }));
};

// Listen for theme changes
document.addEventListener('DOMContentLoaded', () => {
    // Initial theme check
    window.updateChartTheme();

    // Watch for theme changes
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.attributeName === 'class') {
                window.updateChartTheme();
            }
        });
    });

    observer.observe(document.documentElement, { attributes: true });
});