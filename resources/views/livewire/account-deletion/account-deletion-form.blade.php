<div class="max-w-2xl mx-auto">
    <header class="mb-10">
        <h1 class="text-3xl font-bold text-blue-600 mb-2">Account Deletion Request</h1>
        <p class="text-gray-600">Submit a request to delete your account and all associated data</p>
    </header>

    @if (session()->has('message'))
        <div
            class="mb-6 p-4 bg-green-100 border-l-4 border-green-500 text-green-700 dark:bg-green-900/30 dark:text-green-400">
            <p>{{ session('message') }}</p>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="mb-6 p-4 bg-red-100 border-l-4 border-red-500 text-red-700 dark:bg-red-900/30 dark:text-red-400">
            <p>{{ session('error') }}</p>
        </div>
    @endif

    <div class="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
        <form wire:submit="submitDeletionRequest" class="space-y-6">
            <div class="space-y-6">
                <!-- Name -->
                <div class="mb-4">
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                    <input type="text" id="name" wire:model="name" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    @error('name') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                </div>

                <!-- Email -->
                <div class="mb-4">
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                    <input type="email" id="email" wire:model="email" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    @error('email') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                </div>

                <!-- Reason -->
                <div class="mb-4">
                    <label for="reason" class="block text-sm font-medium text-gray-700 mb-1">Reason for Deletion</label>
                    <select id="reason" wire:model="reason" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Select a reason</option>
                        @foreach ($reasons as $value => $label)
                            <option value="{{ $value }}">{{ $label }}</option>
                        @endforeach
                    </select>
                    @error('reason') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                </div>

                <!-- Additional Feedback -->
                <div class="mb-4">
                    <label for="additional_feedback" class="block text-sm font-medium text-gray-700 mb-1">Additional Feedback</label>
                    <textarea id="additional_feedback" wire:model="additional_feedback" rows="4"
                        placeholder="Please provide any additional information that might help us improve our service"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"></textarea>
                    @error('additional_feedback') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                </div>
            </div>

            <div class="pt-4 border-t border-gray-200">
                <p class="text-sm text-gray-500 mb-4">
                    By submitting this form, you are requesting the permanent deletion of your account and all associated data. This action cannot be undone.
                </p>

                <div class="flex justify-end">
                    <button type="submit" class="px-4 py-2 bg-red-600 text-white font-medium rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 flex items-center gap-x-2">
                        <span wire:loading.remove wire:target="submitDeletionRequest">Submit Deletion Request</span>
                        
                        <span wire:loading wire:target="submitDeletionRequest">Submitting...</span>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
