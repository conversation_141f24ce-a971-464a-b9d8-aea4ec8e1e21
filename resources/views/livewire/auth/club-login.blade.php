<div class="flex min-h-full flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
        <div class="flex justify-center">
            <x-app-logo class="h-12 w-auto" />
        </div>
        <h2 class="mt-6 text-center text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
            {{ __('Club Login') }}
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
            {{ __('Sign in to your club account') }}
        </p>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div class="bg-white px-4 py-8 shadow sm:rounded-lg sm:px-10 dark:bg-zinc-800">
            <form wire:submit="login" class="space-y-6">
                <flux:field>
                    <flux:label for="email">{{ __('Email address') }}</flux:label>
                    <flux:input 
                        id="email" 
                        name="email" 
                        type="email" 
                        wire:model="email" 
                        autocomplete="email" 
                        autofocus 
                        required 
                    />
                    <flux:error name="email" />
                </flux:field>

                <flux:field>
                    <flux:label for="password">{{ __('Password') }}</flux:label>
                    <flux:input 
                        id="password" 
                        name="password" 
                        type="password" 
                        wire:model="password" 
                        autocomplete="current-password" 
                        required 
                    />
                    <flux:error name="password" />
                </flux:field>

                <div class="flex items-center justify-between">
                    <flux:checkbox wire:model="remember" id="remember" name="remember">
                        {{ __('Remember me') }}
                    </flux:checkbox>
                </div>

                <div>
                    <flux:button type="submit" variant="primary" class="w-full">
                        {{ __('Sign in') }}
                    </flux:button>
                </div>
            </form>

            <div class="mt-6">
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300 dark:border-gray-600" />
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="bg-white px-2 text-gray-500 dark:bg-zinc-800 dark:text-gray-400">
                            {{ __('Or') }}
                        </span>
                    </div>
                </div>

                <div class="mt-6">
                    <a href="{{ route('login') }}" 
                       class="flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-500 shadow-sm hover:bg-gray-50 dark:border-gray-600 dark:bg-zinc-700 dark:text-gray-300 dark:hover:bg-zinc-600">
                        {{ __('Admin Login') }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
