<div class="w-full">
    <div class="mb-6">
        <flux:breadcrumbs class="flex-wrap">
            <flux:breadcrumbs.item :href="route('dashboard')" wire:navigate>{{ __('Dashboard') }}</flux:breadcrumbs.item>
            <flux:breadcrumbs.item :href="route('cameras.index')" wire:navigate>{{ __('Cameras Management') }}</flux:breadcrumbs.item>
            <flux:breadcrumbs.item current>{{ $isEdit ? __('Edit Camera') : __('Add Camera') }}</flux:breadcrumbs.item>
        </flux:breadcrumbs>
    </div>
    
    <div class="rounded-lg border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
        <form wire:submit="save" class="space-y-6">
            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                <!-- Camera Information Section -->
                <div class="space-y-6">
                    <flux:heading size="sm">{{ __('Camera Information') }}</flux:heading>
                    
                    @if($isEdit)
                        <flux:input 
                            wire:model="camera_id" 
                            :label="__('Camera ID')" 
                            disabled 
                            readonly
                        />
                    @endif
                    
                    <!-- Searchable Club Select -->
                    <div class="relative">
                        <label for="club_search" class="mb-1 block text-sm font-medium text-zinc-700 dark:text-zinc-300">
                            {{ __('Club') }} <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <input
                                type="text"
                                id="club_search"
                                wire:model.live.debounce.300ms="search"
                                wire:click="$set('showDropdown', true)"
                                placeholder="{{ __('Search for a club...') }}"
                                class="block w-full rounded-lg border border-zinc-300 bg-white px-3 py-2 text-zinc-900 placeholder-zinc-500 focus:border-primary-500 focus:ring-primary-500 dark:border-zinc-600 dark:bg-zinc-800 dark:text-white dark:placeholder-zinc-400 dark:focus:border-primary-500 dark:focus:ring-primary-500"
                                autocomplete="off"
                            >
                            @if($selectedClub)
                                <input type="hidden" wire:model="club_id" value="{{ $selectedClub->club_id }}">
                            @endif
                            
                            @if($showDropdown && count($clubs) > 0)
                                <div class="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-zinc-800 sm:text-sm">
                                    <ul class="divide-y divide-zinc-200 dark:divide-zinc-700">
                                        @foreach($clubs as $club)
                                            <li 
                                                wire:key="{{ $club->club_id }}"
                                                wire:click="selectClub('{{ $club->club_id }}', '{{ $club->club_name }}')"
                                                class="cursor-pointer px-4 py-2 hover:bg-zinc-100 dark:hover:bg-zinc-700"
                                            >
                                                <div class="font-medium">{{ $club->club_name }}</div>
                                                <div class="text-xs text-zinc-500 dark:text-zinc-400">{{ $club->club_id }} - {{ $club->email }}</div>
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif
                        </div>
                        @error('club_id')
                            <p class="mt-1 text-xs text-red-500">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <flux:input 
                        wire:model="court_number" 
                        :label="__('Court Number')" 
                        type="number"
                        min="1"
                        required 
                        :error="$errors->first('court_number')"
                    />
                    
                    <flux:input 
                        wire:model="ip" 
                        :label="__('IP Address')" 
                        placeholder="e.g. *************"
                        required 
                        :error="$errors->first('ip')"
                    />
                    
                    <flux:input 
                        wire:model="port" 
                        :label="__('Port')" 
                        type="number"
                        min="1"
                        placeholder="e.g. 8080"
                        required 
                        :error="$errors->first('port')"
                    />
                </div>
                
                <!-- Help Section -->
                <div class="space-y-6">
                    <flux:heading size="sm">{{ __('Camera Setup Guide') }}</flux:heading>
                    
                    <div class="rounded-lg border border-zinc-200 bg-zinc-50 p-4 dark:border-zinc-700 dark:bg-zinc-800">
                        <h3 class="mb-2 font-medium">{{ __('How to set up a camera') }}</h3>
                        <ul class="list-inside list-disc space-y-2 text-sm">
                            <li>{{ __('Ensure the camera is connected to the club\'s network') }}</li>
                            <li>{{ __('Assign a static IP address to the camera') }}</li>
                            <li>{{ __('Configure the camera\'s port forwarding if needed') }}</li>
                            <li>{{ __('Test the connection before saving') }}</li>
                        </ul>
                        
                        <h3 class="mb-2 mt-4 font-medium">{{ __('Camera Requirements') }}</h3>
                        <ul class="list-inside list-disc space-y-2 text-sm">
                            <li>{{ __('IP cameras with RTSP stream support') }}</li>
                            <li>{{ __('Minimum resolution: 720p') }}</li>
                            <li>{{ __('Recommended frame rate: 30fps') }}</li>
                            <li>{{ __('Weatherproof for outdoor courts') }}</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Form Actions -->
            <div class="mt-6 flex justify-end space-x-3">
                <flux:button variant="outline" :href="route('cameras.index')" wire:navigate>
                    {{ __('Cancel') }}
                </flux:button>
                
                <flux:button variant="primary" type="submit">
                    {{ $isEdit ? __('Update Camera') : __('Create Camera') }}
                </flux:button>
            </div>
        </form>
    </div>
</div>
