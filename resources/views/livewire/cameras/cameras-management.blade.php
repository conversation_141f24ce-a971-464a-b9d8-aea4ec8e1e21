<div class="w-full">
    <div class="mb-6 flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <!-- Breadcrumb -->
        <div>
            <flux:breadcrumbs class="flex-wrap">
                <flux:breadcrumbs.item :href="route('dashboard')" wire:navigate>{{ __('Dashboard') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item current>{{ __('Cameras Management') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </div>
        
        <!-- Add Camera Button -->
        <div>
            <flux:button variant="primary" :href="route('cameras.create')" wire:navigate>
                <flux:icon.plus class="me-1" />
                {{ __('Add Camera') }}
            </flux:button>
        </div>
    </div>
    
    <!-- Search and Filters -->
    <div class="mb-6 flex flex-col space-y-4 md:flex-row md:items-center md:space-x-4 md:space-y-0">
        <div class="flex-1">
            <flux:input 
                wire:model.live.debounce.300ms="search" 
                placeholder="{{ __('Search cameras...') }}" 
                icon="magnifying-glass"
            />
        </div>
        <div class="w-full md:w-64">
            <flux:select wire:model.live="clubFilter">
                <option value="">{{ __('All Clubs') }}</option>
                @foreach($clubs as $club)
                    <option value="{{ $club->club_id }}">{{ $club->club_name }}</option>
                @endforeach
            </flux:select>
        </div>
    </div>

    <!-- Cameras Table -->
    <div class="overflow-hidden rounded-lg border border-zinc-200 bg-white dark:border-zinc-700 dark:bg-zinc-900">
        <div class="overflow-x-auto">
            <table class="w-full divide-y divide-zinc-200 dark:divide-zinc-700">
                <thead class="bg-zinc-50 dark:bg-zinc-800">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Camera ID') }}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Club') }}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Court Number') }}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('IP Address') }}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Port') }}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Actions') }}
                        </th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-zinc-200 bg-white dark:divide-zinc-700 dark:bg-zinc-900">
                    @forelse ($cameras as $camera)
                        <tr>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-white">
                                {{ $camera->camera_id }}
                            </td>
                            <td class="px-6 py-4 text-sm text-zinc-900 dark:text-white">
                                {{ $camera->club->club_name }}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-white">
                                {{ $camera->court_number }}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-white">
                                {{ $camera->ip }}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-white">
                                {{ $camera->port }}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                <div class="flex space-x-2">
                                    <flux:modal.trigger name="view-camera-{{ $camera->camera_id }}">
                                        <flux:button variant="ghost" size="xs" icon="eye" />
                                    </flux:modal.trigger>
                                    
                                    <flux:button variant="ghost" size="xs" icon="pencil" :href="route('cameras.edit', $camera->camera_id)" wire:navigate />
                                    
                                    <flux:button variant="ghost" size="xs" icon="trash" wire:click="confirmDelete('{{ $camera->camera_id }}')" />
                                </div>
                                
                                <!-- View Camera Details Modal -->
                                <flux:modal name="view-camera-{{ $camera->camera_id }}" title="{{ __('Camera Details') }}" class="max-w-lg w-full">
                                    <div class="space-y-4">
                                        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                                            <div>
                                                <h3 class="mb-2 font-semibold">{{ __('Camera Information') }}</h3>
                                                <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span class="font-medium dark:text-white text-zinc-800">{{ __('Camera ID') }}:</span> {{ $camera->camera_id }}</div>
                                                <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span class="font-medium dark:text-white text-zinc-800">{{ __('Court Number') }}:</span> {{ $camera->court_number }}</div>
                                                <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span class="font-medium dark:text-white text-zinc-800">{{ __('IP Address') }}:</span> {{ $camera->ip }}</div>
                                                <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span class="font-medium dark:text-white text-zinc-800">{{ __('Port') }}:</span> {{ $camera->port }}</div>
                                                <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span class="font-medium dark:text-white text-zinc-800">{{ __('Created') }}:</span> {{ $camera->created_at->format('M d, Y') }}</div>
                                            </div>
                                            
                                            <div>
                                                <h3 class="mb-2 font-semibold">{{ __('Club Information') }}</h3>
                                                <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span class="font-medium dark:text-white text-zinc-800">{{ __('Club ID') }}:</span> {{ $camera->club->club_id }}</div>
                                                <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span class="font-medium dark:text-white text-zinc-800">{{ __('Club Name') }}:</span> {{ $camera->club->club_name }}</div>
                                                <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span class="font-medium dark:text-white text-zinc-800">{{ __('Email') }}:</span> {{ $camera->club->email }}</div>
                                                <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span class="font-medium dark:text-white text-zinc-800">{{ __('Phone') }}:</span> {{ $camera->club->phone ? ($camera->club->country_code ? '+'.$camera->club->country_code.' ' : '') . $camera->club->phone : '-' }}</div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-6 flex justify-end">
                                        <flux:modal.close>
                                            <flux:button>{{ __('Close') }}</flux:button>
                                        </flux:modal.close>
                                    </div>
                                </flux:modal>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-sm text-zinc-500 dark:text-zinc-400">
                                {{ __('No cameras found.') }}
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <div class="border-t border-zinc-200 px-4 py-3 dark:border-zinc-700">
            {{ $cameras->links() }}
        </div>
    </div>
    
    <!-- Delete Confirmation Modal -->
        <flux:modal name="confirm-camera-deletion"  focusable class="max-w-lg">
            <div class="space-y-6">
                <div>
                    <flux:heading size="lg">{{ __('Are you sure you want to delete this camera?') }}</flux:heading>
                    
                    <flux:subheading>
                        {{ __('Once the camera is deleted, all of its data will be permanently deleted. This action cannot be undone.') }}
                    </flux:subheading>
                </div>
                
                <div class="flex justify-end space-x-2 rtl:space-x-reverse">
                    <flux:modal.close>
                        <flux:button variant="outline">{{ __('Cancel') }}</flux:button>
                    </flux:modal.close>
                    
                    <flux:button variant="danger" wire:click="deleteCamera">{{ __('Delete Camera') }}</flux:button>
                </div>
            </div>
        </flux:modal>
    
</div>
