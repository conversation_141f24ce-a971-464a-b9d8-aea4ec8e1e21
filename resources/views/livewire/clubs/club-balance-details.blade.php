<div class="w-full">
    <div class="mb-6 flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <!-- Breadcrumb -->
        <div>
            <flux:breadcrumbs class="flex-wrap">
                <flux:breadcrumbs.item :href="route('dashboard')" wire:navigate>{{ __('Dashboard') }}
                </flux:breadcrumbs.item>
                @if (Auth::guard('admin')->check())
                    <flux:breadcrumbs.item :href="route('invoicing.index')" wire:navigate>
                        {{ __('Club Balance & Invoicing') }}
                    </flux:breadcrumbs.item>
                @endif
                <flux:breadcrumbs.item current>{{ __('Club Balance Details') }} - {{ $club->club_name }}
                </flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </div>
    </div>

    <!-- Club Information Card -->
    <div
        class="mb-6 rounded-lg bg-gradient-to-r from-blue-50 to-indigo-50 p-6 dark:from-blue-900/20 dark:to-indigo-900/20">
        <div class="flex items-center justify-between gap-2">
            <div class="space-y-1">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">{{ $club->club_name }}</h2>
                <p class="text-sm text-gray-600 dark:text-gray-300">{{ __('Club ID') }}: {{ $club->club_id }}</p>
                <p class="text-sm text-gray-600 dark:text-gray-300">{{ __('Email') }}: {{ $club->email }}</p>
            </div>
            <div class="text-right space-y-1">
                <p class="text-sm text-gray-600 dark:text-gray-300">{{ __('Currency') }}: {{ $club->currency }}</p>
                <p class="text-sm text-gray-600 dark:text-gray-300">{{ __('Status') }}:
                    <span
                        class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium
                        {{ $club->status === 'Active' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' }}">
                        {{ $club->status }}
                    </span>
                </p>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="mb-6 rounded-lg bg-white p-6 shadow-sm dark:bg-zinc-800">
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
            <!-- Date Range -->
            <div>
                <flux:field>
                    <flux:label>{{ __('Start Date') }}</flux:label>
                    <flux:input type="date" wire:model.live="startDate" />
                </flux:field>
            </div>
            <div>
                <flux:field>
                    <flux:label>{{ __('End Date') }}</flux:label>
                    <flux:input type="date" wire:model.live="endDate" />
                </flux:field>
            </div>
            <div class="flex items-end gap-2 w-full lg:col-span-2">
                <flux:field class="flex-1">
                    <flux:label>{{ __('Search') }}</flux:label>
                    <flux:input wire:model.live.debounce.300ms="search"
                        placeholder="{{ __('Search by Token ID, Status, or Player...') }}" />
                </flux:field>

                <flux:button wire:click="clearFilters" variant="outline">
                    {{ __('Clear Filters') }}
                </flux:button>
            </div>
        </div>

        {{-- <div class="mt-4 grid grid-cols-1 gap-4 md:grid-cols-3">
            <!-- Search -->


            <!-- Status Filter -->
            <flux:field>
                <flux:label>{{ __('Status') }}</flux:label>
                <flux:select wire:model.live="statusFilter">
                    <flux:select.option value="">{{ __('All Statuses') }}</flux:select.option>
                    <flux:select.option value="Generated">{{ __('Generated') }}</flux:select.option>
                    <flux:select.option value="Requested">{{ __('Requested') }}</flux:select.option>
                    <flux:select.option value="Purchased">{{ __('Purchased') }}</flux:select.option>
                    <flux:select.option value="Paid">{{ __('Paid') }}</flux:select.option>
                    <flux:select.option value="Paid-Redeemed">{{ __('Paid-Redeemed') }}</flux:select.option>
                    <flux:select.option value="Redeemed">{{ __('Redeemed') }}</flux:select.option>
                </flux:select>
            </flux:field>

            <!-- Clear Filters -->
            <div class="flex items-end">

            </div>
        </div> --}}
    </div>

    <!-- Tokens Table -->
    <div class="overflow-hidden p-6 rounded-lg bg-white shadow-sm dark:bg-zinc-800">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-zinc-200 dark:divide-zinc-700">
                <thead class="bg-zinc-50 dark:bg-zinc-700">
                    <tr>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Token ID') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Value') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Status') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Purchased') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Redeemed') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Paid') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Invoice Nbr') }}
                        </th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-zinc-200 bg-white dark:divide-zinc-700 dark:bg-zinc-800">
                    @forelse($tokens as $token)
                        <tr class="hover:bg-zinc-50 dark:hover:bg-zinc-700">
                            <td
                                class="whitespace-nowrap px-6 py-4 text-sm font-medium text-zinc-900 dark:text-zinc-100">
                                {{ $token->token_id }}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                @if ($token->tokenType)
                                    {{ $club->currency }} {{ number_format($token->tokenType->value, 2) }}
                                @else
                                    -
                                @endif
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                <span
                                    class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium
                                    @switch($token->status)
                                        @case('Generated')
                                            bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200
                                            @break
                                        @case('Requested')
                                            bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                                            @break
                                        @case('Purchased')
                                            bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                            @break
                                        @case('Paid')
                                            bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                            @break
                                        @case('Paid-Redeemed')
                                            bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200
                                            @break
                                        @case('Redeemed')
                                            bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200
                                            @break
                                        @default
                                            bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200
                                    @endswitch">
                                    {{ $token->status }}
                                </span>
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-500 dark:text-zinc-400">
                                {{ $token->purchased_at ? \Carbon\Carbon::parse($token->purchased_at)->format('d/m/Y H:i') : '-' }}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-500 dark:text-zinc-400">
                                {{ $token->redeemed_at ? \Carbon\Carbon::parse($token->redeemed_at)->format('d/m/Y H:i') : '-' }}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-500 dark:text-zinc-400">
                                {{ $token->paid_at ? \Carbon\Carbon::parse($token->paid_at)->format('d/m/Y H:i') : '-' }}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-500 dark:text-zinc-400">
                                @if ($token->invoice)
                                    {{ $token->invoice->invoice_nbr }}
                                @else
                                    -
                                @endif
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center text-sm text-zinc-500 dark:text-zinc-400">
                                {{ __('No tokens found for the selected criteria.') }}
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if ($tokens->hasPages())
            <div class="border-t border-zinc-200 bg-white px-4 py-3 dark:border-zinc-700 dark:bg-zinc-800">
                {{ $tokens->links() }}
            </div>
        @endif
    </div>
</div>
