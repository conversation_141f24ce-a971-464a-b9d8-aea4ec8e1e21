<div class="w-full">
    <div class="mb-6">
        <flux:breadcrumbs class="flex-wrap">
            <flux:breadcrumbs.item :href="route('dashboard')" wire:navigate>{{ __('Dashboard') }}
            </flux:breadcrumbs.item>
            <flux:breadcrumbs.item :href="route('clubs.index')" wire:navigate>{{ __('Clubs Management') }}
            </flux:breadcrumbs.item>
            <flux:breadcrumbs.item current>{{ $isEdit ? __('Edit Club') : __('Add Club') }}</flux:breadcrumbs.item>
        </flux:breadcrumbs>
    </div>

    <div class="rounded-lg border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
        <form wire:submit="save" class="space-y-6">
            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                <!-- Basic Information Section -->
                <div class="space-y-6">
                    <flux:heading size="sm">{{ __('Basic Information') }}</flux:heading>

                    @if ($isEdit)
                        <flux:input wire:model="club_id" :label="__('Club ID')" disabled readonly />
                    @endif

                    <flux:input wire:model="club_name" :label="__('Club Name')" required
                        :error="$errors->first('club_name')" />

                    <flux:input wire:model="email" :label="__('Email')" type="email" required
                        :error="$errors->first('email')" />

                    <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <flux:select wire:model="country_code" :label="__('Country Code')"
                            :error="$errors->first('country_code')">
                            <option value="">{{ __('Select Country Code') }}</option>
                            <option value="93">Afghanistan (+93)</option>
                            <option value="355">Albania (+355)</option>
                            <option value="213">Algeria (+213)</option>
                            <option value="1684">American Samoa (+1684)</option>
                            <option value="376">Andorra (+376)</option>
                            <option value="244">Angola (+244)</option>
                            <option value="1264">Anguilla (+1264)</option>
                            <option value="672">Antarctica (+672)</option>
                            <option value="1268">Antigua and Barbuda (+1268)</option>
                            <option value="54">Argentina (+54)</option>
                            <option value="374">Armenia (+374)</option>
                            <option value="297">Aruba (+297)</option>
                            <option value="61">Australia (+61)</option>
                            <option value="43">Austria (+43)</option>
                            <option value="994">Azerbaijan (+994)</option>
                            <option value="1242">Bahamas (+1242)</option>
                            <option value="973">Bahrain (+973)</option>
                            <option value="880">Bangladesh (+880)</option>
                            <option value="1246">Barbados (+1246)</option>
                            <option value="375">Belarus (+375)</option>
                            <option value="32">Belgium (+32)</option>
                            <option value="501">Belize (+501)</option>
                            <option value="229">Benin (+229)</option>
                            <option value="1441">Bermuda (+1441)</option>
                            <option value="975">Bhutan (+975)</option>
                            <option value="591">Bolivia (+591)</option>
                            <option value="387">Bosnia and Herzegovina (+387)</option>
                            <option value="267">Botswana (+267)</option>
                            <option value="55">Brazil (+55)</option>
                            <option value="246">British Indian Ocean Territory (+246)</option>
                            <option value="1284">British Virgin Islands (+1284)</option>
                            <option value="673">Brunei (+673)</option>
                            <option value="359">Bulgaria (+359)</option>
                            <option value="226">Burkina Faso (+226)</option>
                            <option value="257">Burundi (+257)</option>
                            <option value="855">Cambodia (+855)</option>
                            <option value="237">Cameroon (+237)</option>
                            <option value="1">Canada (+1)</option>
                            <option value="238">Cape Verde (+238)</option>
                            <option value="1345">Cayman Islands (+1345)</option>
                            <option value="236">Central African Republic (+236)</option>
                            <option value="235">Chad (+235)</option>
                            <option value="56">Chile (+56)</option>
                            <option value="86">China (+86)</option>
                            <option value="61">Christmas Island (+61)</option>
                            <option value="61">Cocos Islands (+61)</option>
                            <option value="57">Colombia (+57)</option>
                            <option value="269">Comoros (+269)</option>
                            <option value="682">Cook Islands (+682)</option>
                            <option value="506">Costa Rica (+506)</option>
                            <option value="385">Croatia (+385)</option>
                            <option value="53">Cuba (+53)</option>
                            <option value="599">Curacao (+599)</option>
                            <option value="357">Cyprus (+357)</option>
                            <option value="420">Czech Republic (+420)</option>
                            <option value="243">Democratic Republic of the Congo (+243)</option>
                            <option value="45">Denmark (+45)</option>
                            <option value="253">Djibouti (+253)</option>
                            <option value="1767">Dominica (+1767)</option>
                            <option value="1809">Dominican Republic (+1809)</option>
                            <option value="670">East Timor (+670)</option>
                            <option value="593">Ecuador (+593)</option>
                            <option value="20">Egypt (+20)</option>
                            <option value="503">El Salvador (+503)</option>
                            <option value="240">Equatorial Guinea (+240)</option>
                            <option value="291">Eritrea (+291)</option>
                            <option value="372">Estonia (+372)</option>
                            <option value="251">Ethiopia (+251)</option>
                            <option value="500">Falkland Islands (+500)</option>
                            <option value="298">Faroe Islands (+298)</option>
                            <option value="679">Fiji (+679)</option>
                            <option value="358">Finland (+358)</option>
                            <option value="33">France (+33)</option>
                            <option value="689">French Polynesia (+689)</option>
                            <option value="241">Gabon (+241)</option>
                            <option value="220">Gambia (+220)</option>
                            <option value="995">Georgia (+995)</option>
                            <option value="49">Germany (+49)</option>
                            <option value="233">Ghana (+233)</option>
                            <option value="350">Gibraltar (+350)</option>
                            <option value="30">Greece (+30)</option>
                            <option value="299">Greenland (+299)</option>
                            <option value="1473">Grenada (+1473)</option>
                            <option value="1671">Guam (+1671)</option>
                            <option value="502">Guatemala (+502)</option>
                            <option value="44-1481">Guernsey (+44-1481)</option>
                            <option value="224">Guinea (+224)</option>
                            <option value="245">Guinea-Bissau (+245)</option>
                            <option value="592">Guyana (+592)</option>
                            <option value="509">Haiti (+509)</option>
                            <option value="504">Honduras (+504)</option>
                            <option value="852">Hong Kong (+852)</option>
                            <option value="36">Hungary (+36)</option>
                            <option value="354">Iceland (+354)</option>
                            <option value="91">India (+91)</option>
                            <option value="62">Indonesia (+62)</option>
                            <option value="98">Iran (+98)</option>
                            <option value="964">Iraq (+964)</option>
                            <option value="353">Ireland (+353)</option>
                            <option value="44-1624">Isle of Man (+44-1624)</option>
                            <option value="972">Israel (+972)</option>
                            <option value="39">Italy (+39)</option>
                            <option value="225">Ivory Coast (+225)</option>
                            <option value="1876">Jamaica (+1876)</option>
                            <option value="81">Japan (+81)</option>
                            <option value="44-1534">Jersey (+44-1534)</option>
                            <option value="962">Jordan (+962)</option>
                            <option value="7">Kazakhstan (+7)</option>
                            <option value="254">Kenya (+254)</option>
                            <option value="686">Kiribati (+686)</option>
                            <option value="383">Kosovo (+383)</option>
                            <option value="965">Kuwait (+965)</option>
                            <option value="996">Kyrgyzstan (+996)</option>
                            <option value="856">Laos (+856)</option>
                            <option value="371">Latvia (+371)</option>
                            <option value="961">Lebanon (+961)</option>
                            <option value="266">Lesotho (+266)</option>
                            <option value="231">Liberia (+231)</option>
                            <option value="218">Libya (+218)</option>
                            <option value="423">Liechtenstein (+423)</option>
                            <option value="370">Lithuania (+370)</option>
                            <option value="352">Luxembourg (+352)</option>
                            <option value="853">Macau (+853)</option>
                            <option value="389">Macedonia (+389)</option>
                            <option value="261">Madagascar (+261)</option>
                            <option value="265">Malawi (+265)</option>
                            <option value="60">Malaysia (+60)</option>
                            <option value="960">Maldives (+960)</option>
                            <option value="223">Mali (+223)</option>
                            <option value="356">Malta (+356)</option>
                            <option value="692">Marshall Islands (+692)</option>
                            <option value="222">Mauritania (+222)</option>
                            <option value="230">Mauritius (+230)</option>
                            <option value="262">Mayotte (+262)</option>
                            <option value="52">Mexico (+52)</option>
                            <option value="691">Micronesia (+691)</option>
                            <option value="373">Moldova (+373)</option>
                            <option value="377">Monaco (+377)</option>
                            <option value="976">Mongolia (+976)</option>
                            <option value="382">Montenegro (+382)</option>
                            <option value="1664">Montserrat (+1664)</option>
                            <option value="212">Morocco (+212)</option>
                            <option value="258">Mozambique (+258)</option>
                            <option value="95">Myanmar (+95)</option>
                            <option value="264">Namibia (+264)</option>
                            <option value="674">Nauru (+674)</option>
                            <option value="977">Nepal (+977)</option>
                            <option value="31">Netherlands (+31)</option>
                            <option value="599">Netherlands Antilles (+599)</option>
                            <option value="687">New Caledonia (+687)</option>
                            <option value="64">New Zealand (+64)</option>
                            <option value="505">Nicaragua (+505)</option>
                            <option value="227">Niger (+227)</option>
                            <option value="234">Nigeria (+234)</option>
                            <option value="683">Niue (+683)</option>
                            <option value="850">North Korea (+850)</option>
                            <option value="1670">Northern Mariana Islands (+1670)</option>
                            <option value="47">Norway (+47)</option>
                            <option value="968">Oman (+968)</option>
                            <option value="92">Pakistan (+92)</option>
                            <option value="680">Palau (+680)</option>
                            <option value="970">Palestine (+970)</option>
                            <option value="507">Panama (+507)</option>
                            <option value="675">Papua New Guinea (+675)</option>
                            <option value="595">Paraguay (+595)</option>
                            <option value="51">Peru (+51)</option>
                            <option value="63">Philippines (+63)</option>
                            <option value="64">Pitcairn (+64)</option>
                            <option value="48">Poland (+48)</option>
                            <option value="351">Portugal (+351)</option>
                            <option value="1787">Puerto Rico (+1787)</option>
                            <option value="974">Qatar (+974)</option>
                            <option value="242">Republic of the Congo (+242)</option>
                            <option value="262">Reunion (+262)</option>
                            <option value="40">Romania (+40)</option>
                            <option value="7">Russia (+7)</option>
                            <option value="250">Rwanda (+250)</option>
                            <option value="590">Saint Barthelemy (+590)</option>
                            <option value="290">Saint Helena (+290)</option>
                            <option value="1869">Saint Kitts and Nevis (+1869)</option>
                            <option value="1758">Saint Lucia (+1758)</option>
                            <option value="590">Saint Martin (+590)</option>
                            <option value="508">Saint Pierre and Miquelon (+508)</option>
                            <option value="1784">Saint Vincent and the Grenadines (+1784)</option>
                            <option value="685">Samoa (+685)</option>
                            <option value="378">San Marino (+378)</option>
                            <option value="239">Sao Tome and Principe (+239)</option>
                            <option value="966">Saudi Arabia (+966)</option>
                            <option value="221">Senegal (+221)</option>
                            <option value="381">Serbia (+381)</option>
                            <option value="248">Seychelles (+248)</option>
                            <option value="232">Sierra Leone (+232)</option>
                            <option value="65">Singapore (+65)</option>
                            <option value="1721">Sint Maarten (+1721)</option>
                            <option value="421">Slovakia (+421)</option>
                            <option value="386">Slovenia (+386)</option>
                            <option value="677">Solomon Islands (+677)</option>
                            <option value="252">Somalia (+252)</option>
                            <option value="27">South Africa (+27)</option>
                            <option value="82">South Korea (+82)</option>
                            <option value="211">South Sudan (+211)</option>
                            <option value="34">Spain (+34)</option>
                            <option value="94">Sri Lanka (+94)</option>
                            <option value="249">Sudan (+249)</option>
                            <option value="597">Suriname (+597)</option>
                            <option value="47">Svalbard and Jan Mayen (+47)</option>
                            <option value="268">Swaziland (+268)</option>
                            <option value="46">Sweden (+46)</option>
                            <option value="41">Switzerland (+41)</option>
                            <option value="963">Syria (+963)</option>
                            <option value="886">Taiwan (+886)</option>
                            <option value="992">Tajikistan (+992)</option>
                            <option value="255">Tanzania (+255)</option>
                            <option value="66">Thailand (+66)</option>
                            <option value="228">Togo (+228)</option>
                            <option value="690">Tokelau (+690)</option>
                            <option value="676">Tonga (+676)</option>
                            <option value="1868">Trinidad and Tobago (+1868)</option>
                            <option value="216">Tunisia (+216)</option>
                            <option value="90">Turkey (+90)</option>
                            <option value="993">Turkmenistan (+993)</option>
                            <option value="1649">Turks and Caicos Islands (+1649)</option>
                            <option value="688">Tuvalu (+688)</option>
                            <option value="1340">U.S. Virgin Islands (+1340)</option>
                            <option value="256">Uganda (+256)</option>
                            <option value="380">Ukraine (+380)</option>
                            <option value="971">United Arab Emirates (+971)</option>
                            <option value="44">United Kingdom (+44)</option>
                            <option value="1">United States (+1)</option>
                            <option value="598">Uruguay (+598)</option>
                            <option value="998">Uzbekistan (+998)</option>
                            <option value="678">Vanuatu (+678)</option>
                            <option value="379">Vatican (+379)</option>
                            <option value="58">Venezuela (+58)</option>
                            <option value="84">Vietnam (+84)</option>
                            <option value="681">Wallis and Futuna (+681)</option>
                            <option value="212">Western Sahara (+212)</option>
                            <option value="967">Yemen (+967)</option>
                            <option value="260">Zambia (+260)</option>
                            <option value="263">Zimbabwe (+263)</option>
                        </flux:select>

                        <flux:input wire:model="phone" :label="__('Phone Number')" :error="$errors->first('phone')" />
                    </div>

                    <flux:input wire:model="contact" :label="__('Contact Person')"
                        :error="$errors->first('contact')" />


                    <flux:select wire:model="status" :label="__('Status')" required
                        :error="$errors->first('status')" x-on:change="$wire.status = $event.target.value">
                        <option value="Active">{{ __('Active') }}</option>
                        <option value="Suspended">{{ __('Suspended') }}</option>
                    </flux:select>

                    <!-- Suspension Reason Textarea (shown only when status is Suspended) -->
                    <div x-show="$wire.status === 'Suspended'" x-transition>
                        <flux:textarea wire:model="suspension_reason" :label="__('Reason for Suspension')"
                            :placeholder="__('Enter the reason for suspension (optional)')" rows="3"
                            :error="$errors->first('suspension_reason')" :helper="__('Maximum 254 characters')" />
                    </div>



                    <flux:checkbox wire:model="sell_tokens" :label="__('Sell Tokens')"
                        :error="$errors->first('sell_tokens')" />
                </div>

                <!-- Location & Business Details Section -->
                <div class="space-y-6">
                    <flux:heading size="sm">{{ __('Location & Business Details') }}</flux:heading>

                    <!-- Searchable Country Select -->
                    <div class="relative" x-data @click.outside="$wire.closeDropdowns()">
                        <label for="country_search"
                            class="mb-1 block text-sm font-medium text-zinc-700 dark:text-zinc-300">
                            {{ __('Country') }}
                        </label>
                        <div class="relative">
                            <input type="text" id="country_search" wire:model.live.debounce.300ms="countrySearch"
                                wire:click="$set('showCountryDropdown', true)"
                                placeholder="{{ __('Search for a country...') }}"
                                class="block w-full rounded-lg border border-zinc-300 bg-white px-3 py-2 text-zinc-900 placeholder-zinc-500 focus:border-primary-500 focus:ring-primary-500 dark:border-zinc-600 dark:bg-zinc-800 dark:text-white dark:placeholder-zinc-400 dark:focus:border-primary-500 dark:focus:ring-primary-500"
                                autocomplete="off">

                            @if ($showCountryDropdown)
                                <div
                                    class="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-zinc-800 sm:text-sm">
                                    @if (count($countries) > 0)
                                        <ul class="divide-y divide-zinc-200 dark:divide-zinc-700">
                                            @foreach ($countries as $countryItem)
                                                <li wire:key="country-{{ $countryItem->id }}"
                                                    wire:click="selectCountry('{{ $countryItem->id }}', '{{ $countryItem->name }}')"
                                                    class="cursor-pointer px-4 py-2 hover:bg-zinc-100 dark:hover:bg-zinc-700">
                                                    <div class="font-medium">{{ $countryItem->name }}</div>
                                                </li>
                                            @endforeach
                                        </ul>
                                    @else
                                        <div class="px-4 py-2 text-sm text-zinc-500 dark:text-zinc-400">
                                            {{ __('No countries found') }}</div>
                                    @endif
                                </div>
                            @endif
                        </div>
                        @error('country')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <!-- Searchable City Select -->
                        <div class="relative" x-data @click.outside="$wire.closeDropdowns()">
                            <label for="city_search"
                                class="mb-1 block text-sm font-medium text-zinc-700 dark:text-zinc-300">
                                {{ __('City') }}
                            </label>
                            <div class="relative">
                                <input type="text" id="city_search" wire:model.live.debounce.300ms="citySearch"
                                    wire:click="$set('showCityDropdown', true)"
                                    placeholder="{{ __('Search for a city...') }}"
                                    class="block w-full rounded-lg border border-zinc-300 bg-white px-3 py-2 text-zinc-900 placeholder-zinc-500 focus:border-primary-500 focus:ring-primary-500 dark:border-zinc-600 dark:bg-zinc-800 dark:text-white dark:placeholder-zinc-400 dark:focus:border-primary-500 dark:focus:ring-primary-500"
                                    autocomplete="off">

                                @if ($showCityDropdown)
                                    <div
                                        class="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-zinc-800 sm:text-sm">
                                        @if (count($cities) > 0)
                                            <ul class="divide-y divide-zinc-200 dark:divide-zinc-700">
                                                @foreach ($cities as $cityItem)
                                                    <li wire:key="city-{{ $cityItem->id }}"
                                                        wire:click="selectCity('{{ $cityItem->id }}', '{{ $cityItem->name }}')"
                                                        class="cursor-pointer px-4 py-2 hover:bg-zinc-100 dark:hover:bg-zinc-700">
                                                        <div class="font-medium">{{ $cityItem->name }}</div>
                                                    </li>
                                                @endforeach
                                            </ul>
                                        @else
                                            <div class="px-4 py-2 text-sm text-zinc-500 dark:text-zinc-400">
                                                {{ __('No cities found') }}</div>
                                        @endif
                                    </div>
                                @endif
                            </div>
                            @error('city')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <flux:input wire:model="postal_code" :label="__('Postal Code')"
                            :error="$errors->first('postal_code')" />
                    </div>

                    <flux:input wire:model="tax_id" :label="__('Tax ID')" :error="$errors->first('tax_id')" />

                    <!-- Searchable Commercial Select -->
                    <div class="relative" x-data @click.outside="$wire.closeDropdowns()">
                        <label for="commercial_search"
                            class="mb-1 block text-sm font-medium text-zinc-700 dark:text-zinc-300">
                            {{ __('Commercial') }}
                        </label>
                        <div class="relative">
                            <input type="text" id="commercial_search"
                                wire:model.live.debounce.300ms="commercialSearch"
                                wire:click="$set('showCommercialDropdown', true)"
                                placeholder="{{ __('Search for a commercial...') }}"
                                class="block w-full rounded-lg border border-zinc-300 bg-white px-3 py-2 text-zinc-900 placeholder-zinc-500 focus:border-primary-500 focus:ring-primary-500 dark:border-zinc-600 dark:bg-zinc-800 dark:text-white dark:placeholder-zinc-400 dark:focus:border-primary-500 dark:focus:ring-primary-500"
                                autocomplete="off">

                            @if ($showCommercialDropdown)
                                <div
                                    class="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-zinc-800 sm:text-sm">
                                    @if (count($commercials) > 0)
                                        <ul class="divide-y divide-zinc-200 dark:divide-zinc-700">
                                            @foreach ($commercials as $commercial)
                                                <li wire:key="commercial-{{ $commercial->commercial_id }}"
                                                    wire:click="selectCommercial('{{ $commercial->commercial_id }}', '{{ $commercial->name }}')"
                                                    class="cursor-pointer px-4 py-2 hover:bg-zinc-100 dark:hover:bg-zinc-700">
                                                    <div class="font-medium">{{ $commercial->name }}</div>
                                                    <div class="text-xs text-zinc-500 dark:text-zinc-400">
                                                        {{ $commercial->commercial_id }} -
                                                        {{ $commercial->commission }}%</div>
                                                </li>
                                            @endforeach
                                        </ul>
                                    @else
                                        <div class="px-4 py-2 text-sm text-zinc-500 dark:text-zinc-400">
                                            {{ __('No commercials found') }}</div>
                                    @endif
                                </div>
                            @endif
                        </div>
                        @error('commercial_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="commercial_commission"
                            class="mb-1 block text-sm font-medium text-zinc-700 dark:text-zinc-300">
                            {{ __('Commercial Commission (%)') }}
                        </label>
                        <div class="flex items-center space-x-3">
                            <input type="range" id="commercial_commission"
                                wire:model.live.debounce.100ms="commercial_commission" min="1.5" max="3"
                                step="0.1"
                                class="w-full h-2 bg-zinc-200 rounded-lg appearance-none cursor-pointer dark:bg-zinc-700">
                            <span class="text-sm font-medium w-10 text-center">{{ $commercial_commission }}%</span>
                        </div>
                        @error('commercial_commission')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-zinc-500 dark:text-zinc-400">
                            {{ __('Commission range: 1.5% - 3%') }}</p>
                    </div>
                    <div>
                        <label for="commission"
                            class="mb-1 block text-sm font-medium text-zinc-700 dark:text-zinc-300">
                            {{ __('Club Commission (%)') }}
                        </label>
                        <div class="flex items-center space-x-3">
                            <input type="range" id="commission" wire:model.live.debounce.100ms="commission"
                                min="0" max="100" step="0.1"
                                class="w-full h-2 bg-zinc-200 rounded-lg appearance-none cursor-pointer dark:bg-zinc-700">
                            <span class="text-sm font-medium w-10 text-center">{{ $commission }}%</span>
                        </div>
                        @error('commission')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-zinc-500 dark:text-zinc-400">
                            {{ __('Commission range: 0% - 100%') }}</p>
                    </div>





                    <flux:select wire:model="currency" :label="__('Currency')" :error="$errors->first('currency')">
                        <option value="USD" label="United States dollar">USD</option>
                        <option value="EUR" label="Euro">EUR</option>
                        <option disabled>──────────</option>
                        <option value="AFN" label="Afghan afghani">AFN</option>
                        <option value="ALL" label="Albanian lek">ALL</option>
                        <option value="DZD" label="Algerian dinar">DZD</option>
                        <option value="AOA" label="Angolan kwanza">AOA</option>
                        <option value="ARS" label="Argentine peso">ARS</option>
                        <option value="AMD" label="Armenian dram">AMD</option>
                        <option value="AWG" label="Aruban florin">AWG</option>
                        <option value="AUD" label="Australian dollar">AUD</option>
                        <option value="AZN" label="Azerbaijani manat">AZN</option>
                        <option value="BHD" label="Bahraini dinar">BHD</option>
                        <option value="BSD" label="Bahamian dollar">BSD</option>
                        <option value="BDT" label="Bangladeshi taka">BDT</option>
                        <option value="BBD" label="Barbadian dollar">BBD</option>
                        <option value="BYN" label="Belarusian ruble">BYN</option>
                        <option value="BZD" label="Belize dollar">BZD</option>
                        <option value="BMD" label="Bermudian dollar">BMD</option>
                        <option value="BTN" label="Bhutanese ngultrum">BTN</option>
                        <option value="BOB" label="Bolivian boliviano">BOB</option>
                        <option value="BAM" label="Bosnia and Herzegovina convertible mark">BAM</option>
                        <option value="BWP" label="Botswana pula">BWP</option>
                        <option value="BRL" label="Brazilian real">BRL</option>
                        <option value="GBP" label="British pound">GBP</option>
                        <option value="BND" label="Brunei dollar">BND</option>
                        <option value="MMK" label="Burmese kyat">MMK</option>
                        <option value="BIF" label="Burundian franc">BIF</option>
                        <option value="KHR" label="Cambodian riel">KHR</option>
                        <option value="CAD" label="Canadian dollar">CAD</option>
                        <option value="CVE" label="Cape Verdean escudo">CVE</option>
                        <option value="KYD" label="Cayman Islands dollar">KYD</option>
                        <option value="XAF" label="Central African CFA franc">XAF</option>
                        <option value="XPF" label="CFP franc">XPF</option>
                        <option value="CLP" label="Chilean peso">CLP</option>
                        <option value="CNY" label="Chinese yuan">CNY</option>
                        <option value="COP" label="Colombian peso">COP</option>
                        <option value="KMF" label="Comorian franc">KMF</option>
                        <option value="CDF" label="Congolese franc">CDF</option>
                        <option value="CRC" label="Costa Rican colón">CRC</option>
                        <option value="HRK" label="Croatian kuna">HRK</option>
                        <option value="CUC" label="Cuban convertible peso">CUC</option>
                        <option value="CUP" label="Cuban peso">CUP</option>
                        <option value="CZK" label="Czech koruna">CZK</option>
                        <option value="DKK" label="Danish krone">DKK</option>
                        <option value="DOP" label="Dominican peso">DOP</option>
                        <option value="DJF" label="Djiboutian franc">DJF</option>
                        <option value="XCD" label="Eastern Caribbean dollar">XCD</option>
                        <option value="EGP" label="Egyptian pound">EGP</option>
                        <option value="ERN" label="Eritrean nakfa">ERN</option>
                        <option value="ETB" label="Ethiopian birr">ETB</option>
                        <option value="FKP" label="Falkland Islands pound">FKP</option>
                        <option value="FJD" label="Fijian dollar">FJD</option>
                        <option value="GMD" label="Gambian dalasi">GMD</option>
                        <option value="GEL" label="Georgian lari">GEL</option>
                        <option value="GHS" label="Ghanaian cedi">GHS</option>
                        <option value="GIP" label="Gibraltar pound">GIP</option>
                        <option value="GTQ" label="Guatemalan quetzal">GTQ</option>
                        <option value="GGP" label="Guernsey pound">GGP</option>
                        <option value="GNF" label="Guinean franc">GNF</option>
                        <option value="GYD" label="Guyanese dollar">GYD</option>
                        <option value="HTG" label="Haitian gourde">HTG</option>
                        <option value="HNL" label="Honduran lempira">HNL</option>
                        <option value="HKD" label="Hong Kong dollar">HKD</option>
                        <option value="HUF" label="Hungarian forint">HUF</option>
                        <option value="ISK" label="Icelandic króna">ISK</option>
                        <option value="INR" label="Indian rupee">INR</option>
                        <option value="IDR" label="Indonesian rupiah">IDR</option>
                        <option value="IRR" label="Iranian rial">IRR</option>
                        <option value="IQD" label="Iraqi dinar">IQD</option>
                        <option value="ILS" label="Israeli new shekel">ILS</option>
                        <option value="JMD" label="Jamaican dollar">JMD</option>
                        <option value="JPY" label="Japanese yen">JPY</option>
                        <option value="JEP" label="Jersey pound">JEP</option>
                        <option value="JOD" label="Jordanian dinar">JOD</option>
                        <option value="KZT" label="Kazakhstani tenge">KZT</option>
                        <option value="KES" label="Kenyan shilling">KES</option>
                        <option value="KID" label="Kiribati dollar">KID</option>
                        <option value="KGS" label="Kyrgyzstani som">KGS</option>
                        <option value="KWD" label="Kuwaiti dinar">KWD</option>
                        <option value="LAK" label="Lao kip">LAK</option>
                        <option value="LBP" label="Lebanese pound">LBP</option>
                        <option value="LSL" label="Lesotho loti">LSL</option>
                        <option value="LRD" label="Liberian dollar">LRD</option>
                        <option value="LYD" label="Libyan dinar">LYD</option>
                        <option value="MOP" label="Macanese pataca">MOP</option>
                        <option value="MKD" label="Macedonian denar">MKD</option>
                        <option value="MGA" label="Malagasy ariary">MGA</option>
                        <option value="MWK" label="Malawian kwacha">MWK</option>
                        <option value="MYR" label="Malaysian ringgit">MYR</option>
                        <option value="MVR" label="Maldivian rufiyaa">MVR</option>
                        <option value="IMP" label="Manx pound">IMP</option>
                        <option value="MRU" label="Mauritanian ouguiya">MRU</option>
                        <option value="MUR" label="Mauritian rupee">MUR</option>
                        <option value="MXN" label="Mexican peso">MXN</option>
                        <option value="MDL" label="Moldovan leu">MDL</option>
                        <option value="MNT" label="Mongolian tögrög">MNT</option>
                        <option value="MAD" label="Moroccan dirham">MAD</option>
                        <option value="MZN" label="Mozambican metical">MZN</option>
                        <option value="NAD" label="Namibian dollar">NAD</option>
                        <option value="NPR" label="Nepalese rupee">NPR</option>
                        <option value="ANG" label="Netherlands Antillean guilder">ANG</option>
                        <option value="TWD" label="New Taiwan dollar">TWD</option>
                        <option value="NZD" label="New Zealand dollar">NZD</option>
                        <option value="NIO" label="Nicaraguan córdoba">NIO</option>
                        <option value="NGN" label="Nigerian naira">NGN</option>
                        <option value="KPW" label="North Korean won">KPW</option>
                        <option value="NOK" label="Norwegian krone">NOK</option>
                        <option value="OMR" label="Omani rial">OMR</option>
                        <option value="PKR" label="Pakistani rupee">PKR</option>
                        <option value="PAB" label="Panamanian balboa">PAB</option>
                        <option value="PGK" label="Papua New Guinean kina">PGK</option>
                        <option value="PYG" label="Paraguayan guaraní">PYG</option>
                        <option value="PEN" label="Peruvian sol">PEN</option>
                        <option value="PHP" label="Philippine peso">PHP</option>
                        <option value="PLN" label="Polish złoty">PLN</option>
                        <option value="QAR" label="Qatari riyal">QAR</option>
                        <option value="RON" label="Romanian leu">RON</option>
                        <option value="RUB" label="Russian ruble">RUB</option>
                        <option value="RWF" label="Rwandan franc">RWF</option>
                        <option value="SHP" label="Saint Helena pound">SHP</option>
                        <option value="WST" label="Samoan tālā">WST</option>
                        <option value="STN" label="São Tomé and Príncipe dobra">STN</option>
                        <option value="SAR" label="Saudi riyal">SAR</option>
                        <option value="RSD" label="Serbian dinar">RSD</option>
                        <option value="SLL" label="Sierra Leonean leone">SLL</option>
                        <option value="SGD" label="Singapore dollar">SGD</option>
                        <option value="SOS" label="Somali shilling">SOS</option>
                        <option value="SLS" label="Somaliland shilling">SLS</option>
                        <option value="ZAR" label="South African rand">ZAR</option>
                        <option value="KRW" label="South Korean won">KRW</option>
                        <option value="SSP" label="South Sudanese pound">SSP</option>
                        <option value="SRD" label="Surinamese dollar">SRD</option>
                        <option value="SEK" label="Swedish krona">SEK</option>
                        <option value="CHF" label="Swiss franc">CHF</option>
                        <option value="LKR" label="Sri Lankan rupee">LKR</option>
                        <option value="SZL" label="Swazi lilangeni">SZL</option>
                        <option value="SYP" label="Syrian pound">SYP</option>
                        <option value="TJS" label="Tajikistani somoni">TJS</option>
                        <option value="TZS" label="Tanzanian shilling">TZS</option>
                        <option value="THB" label="Thai baht">THB</option>
                        <option value="TOP" label="Tongan paʻanga">TOP</option>
                        <option value="PRB" label="Transnistrian ruble">PRB</option>
                        <option value="TTD" label="Trinidad and Tobago dollar">TTD</option>
                        <option value="TND" label="Tunisian dinar">TND</option>
                        <option value="TRY" label="Turkish lira">TRY</option>
                        <option value="TMT" label="Turkmenistan manat">TMT</option>
                        <option value="TVD" label="Tuvaluan dollar">TVD</option>
                        <option value="UGX" label="Ugandan shilling">UGX</option>
                        <option value="UAH" label="Ukrainian hryvnia">UAH</option>
                        <option value="AED" label="United Arab Emirates dirham">AED</option>
                        <option value="UYU" label="Uruguayan peso">UYU</option>
                        <option value="UZS" label="Uzbekistani soʻm">UZS</option>
                        <option value="VUV" label="Vanuatu vatu">VUV</option>
                        <option value="VES" label="Venezuelan bolívar soberano">VES</option>
                        <option value="VND" label="Vietnamese đồng">VND</option>
                        <option value="XOF" label="West African CFA franc">XOF</option>
                        <option value="ZMW" label="Zambian kwacha">ZMW</option>
                        <option value="ZWB" label="Zimbabwean bonds">ZWB</option>
                    </flux:select>
                </div>
            </div>

            <!-- Password Section -->
            <div class="mt-6 border-t border-zinc-200 pt-6 dark:border-zinc-700">
                <flux:heading size="sm">{{ __('Authentication') }}</flux:heading>

                <div class="mt-4 grid grid-cols-1 gap-6 md:grid-cols-2">
                    <flux:input wire:model="password" :label="__('Password')" type="password" :required="!$isEdit"
                        :error="$errors->first('password')"
                        :helper="$isEdit ? __('Leave blank to keep current password') : ''" />

                    <flux:input wire:model="password_confirmation" :label="__('Confirm Password')" type="password"
                        :required="!$isEdit" :error="$errors->first('password_confirmation')" />
                </div>
            </div>

            <!-- Form Actions -->
            <div class="mt-6 flex justify-end space-x-3">
                <flux:button variant="outline" :href="route('clubs.index')" wire:navigate>
                    {{ __('Cancel') }}
                </flux:button>

                <flux:button variant="primary" type="submit">
                    {{ $isEdit ? __('Update Club') : __('Create Club') }}
                </flux:button>
            </div>
        </form>
    </div>
</div>
