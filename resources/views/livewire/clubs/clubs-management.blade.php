<div class="w-full">
    <div class="mb-6 flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <!-- Breadcrumb -->
        <div>
            <flux:breadcrumbs class="flex-wrap">

                <flux:breadcrumbs.item :href="route('dashboard')" wire:navigate>{{ __('Dashboard') }}
                </flux:breadcrumbs.item>
                <flux:breadcrumbs.item current>{{ __('Clubs Management') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </div>

        <!-- Add Club Button -->
        <div>
            <flux:button variant="primary" :href="route('clubs.create')" wire:navigate>
                <flux:icon.plus class="me-1" />
                {{ __('Add Club') }}
            </flux:button>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="mb-6 flex flex-col space-y-4 md:flex-row md:items-center md:space-x-4 md:space-y-0">
        <div class="flex-1">
            <flux:input wire:model.live.debounce.300ms="search" placeholder="{{ __('Search clubs...') }}"
                icon="magnifying-glass" />
        </div>
        <div class="w-full md:w-48">
            <flux:select wire:model.live="status">
                <option value="">{{ __('All Status') }}</option>
                <option value="Active">{{ __('Active') }}</option>
                <option value="Suspended">{{ __('Suspended') }}</option>
            </flux:select>
        </div>
    </div>

    <!-- Clubs Table -->
    <div class="overflow-hidden rounded-lg border border-zinc-200 bg-white dark:border-zinc-700 dark:bg-zinc-900">
        <div class="overflow-x-auto">
            <table class="w-full divide-y divide-zinc-200 dark:divide-zinc-700">
                <thead class="bg-zinc-50 dark:bg-zinc-800">
                    <tr>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Club ID') }}
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Club Name') }}
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Contact Info') }}
                        </th>

                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Created At') }}
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Status') }}
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Actions') }}
                        </th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-zinc-200 bg-white dark:divide-zinc-700 dark:bg-zinc-900">
                    @forelse ($clubs as $club)
                        <tr>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-white">
                                {{ $club->club_id }}
                            </td>
                            <td class="px-6 py-4 text-sm text-zinc-900 dark:text-white">
                                {{ $club->club_name }}
                            </td>
                            <td class="px-6 py-4 text-sm text-zinc-900 dark:text-white">
                                <div>{{ $club->email }}</div>
                                <div>
                                    {{ $club->phone ? ($club->country_code ? '+' . $club->country_code . ' ' : '') . $club->phone : '-' }}
                                </div>
                            </td>

                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                {{ $club->created_at?->format('d/m/Y H:i') }}
                            </td>

                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                <div class="flex items-center space-x-2">
                                    @if ($club->status === 'Active')
                                        <flux:badge variant="success">{{ __('Active') }}</flux:badge>
                                    @else
                                        <flux:badge variant="danger">{{ __('Suspended') }}</flux:badge>
                                        @if ($club->status === 'Suspended' && $club->suspension_reason)
                                            <div class="relative" x-data="{ showTooltip: false }">
                                                <button
                                                    @mouseenter="showTooltip = true"
                                                    @mouseleave="showTooltip = false"
                                                    class="text-sky-500 hover:text-sky-700"
                                                >
                                                    <flux:icon.information-circle class="w-4 h-4" />
                                                </button>
                                                <div
                                                    x-show="showTooltip"
                                                    x-transition
                                                    class="absolute z-10 px-3 py-2 text-sm text-white bg-gray-900 rounded-lg shadow-lg bottom-full left-1/2 transform -tranzinc-x-1/2 mb-2 w-64"
                                                    style="display: none;"
                                                >
                                                    <div class="font-semibold mb-1">{{ __('Suspension Reason:') }}</div>
                                                    <div class="whitespace-pre-line">{{ $club->suspension_reason }}</div>
                                                    <div class="absolute top-full left-1/2 transform -tranzinc-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                                                </div>
                                            </div>
                                        @endif
                                    @endif
                                </div>
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                <div class="flex space-x-2">
                                    <flux:modal.trigger name="view-club-{{ $club->club_id }}">
                                        <flux:button variant="ghost" size="xs" icon="eye" />
                                    </flux:modal.trigger>

                                    <flux:button variant="ghost" size="xs" icon="pencil"
                                        :href="route('clubs.edit', $club->club_id)" wire:navigate />

                                    <flux:button variant="ghost" size="xs" icon="coin"
                                        :href="route('clubs.token-types', $club->club_id)" wire:navigate
                                        title="{{ __('Manage Token Types') }}" />

                                    <flux:button variant="ghost" size="xs" icon="trash"
                                        wire:click="confirmDelete('{{ $club->club_id }}')" />
                                </div>

                                <!-- View Club Details Modal -->
                                <flux:modal name="view-club-{{ $club->club_id }}" title="{{ __('Club Details') }}"
                                    class="max-w-2xl w-full">
                                    <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div>
                                            <h3 class="mb-2 font-semibold">{{ __('Basic Information') }}</h3>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Club ID') }}:</span>
                                                {{ $club->club_id }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Club Name') }}:</span>
                                                {{ $club->club_name }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Email') }}:</span>
                                                {{ $club->email }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Phone') }}:</span>
                                                {{ $club->phone ? ($club->country_code ? '+' . $club->country_code . ' ' : '') . $club->phone : '-' }}
                                            </div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Contact') }}:</span>
                                                {{ $club->contact ?? '-' }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Status') }}:</span>
                                                {{ $club->status }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Sell Tokens') }}:</span>
                                                {{ $club->sell_tokens ? 'Yes' : 'No' }}</div>

                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Created At') }}:</span>
                                                {{ $club->created_at?->format('d/m/Y H:i') }}
                                            </div>
                                        </div>

                                        <div>
                                            <h3 class="mb-2 font-semibold">{{ __('Location & Business Details') }}</h3>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Country') }}:</span>
                                                {{ $club->country ?? '-' }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('City') }}:</span>
                                                {{ $club->city ?? '-' }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Postal Code') }}:</span>
                                                {{ $club->postal_code ?? '-' }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Tax ID') }}:</span>
                                                {{ $club->tax_id ?? '-' }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Commercial ID') }}:</span>
                                                {{ $club->commercial_id ?? '-' }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Club Commission') }}:</span>
                                                {{ $club->commission ?? '-' }}%</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Commercial Commission') }}:</span>
                                                {{ $club->commercial_commission ?? '-' }}%</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Currency') }}:</span>
                                                {{ $club->currency ?? 'USD' }}</div>

                                        </div>
                                    </div>

                                    <div class="mt-4">
                                        <h3 class="mb-2 font-semibold">{{ __('Related Information') }}</h3>
                                        <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col">
                                                <span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Token Types') }}:</span>
                                                {{ $club->tokenTypes->count() }}
                                            </div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col">
                                                <span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Cameras') }}:</span>
                                                {{ $club->cameras->count() }}
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-6 flex justify-end">
                                        <flux:modal.close>
                                            <flux:button>{{ __('Close') }}</flux:button>
                                        </flux:modal.close>
                                    </div>
                                </flux:modal>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-sm text-zinc-500 dark:text-zinc-400">
                                {{ __('No clubs found.') }}
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="border-t border-zinc-200 px-4 py-3 dark:border-zinc-700">
            {{ $clubs->links() }}
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <flux:modal name="confirm-club-deletion" class="max-w-lg w-full">
        <div class="space-y-6">
            <div>
                <flux:heading size="lg">{{ __('Are you sure you want to delete this club?') }}</flux:heading>

                <flux:subheading>
                    {{ __('Once the club is deleted, all of its resources and data will be permanently deleted. This action cannot be undone.') }}
                </flux:subheading>
            </div>

            <div class="flex justify-end space-x-2 rtl:space-x-reverse">
                <flux:modal.close>
                    <flux:button variant="outline">{{ __('Cancel') }}</flux:button>
                </flux:modal.close>

                <flux:button variant="danger" wire:click="deleteClub">{{ __('Delete Club') }}</flux:button>
            </div>
        </div>
    </flux:modal>

</div>
