<div class="w-full">
    <div class="mb-6 flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <!-- Breadcrumb -->
        <div>
            <flux:breadcrumbs class="flex-wrap">
                <flux:breadcrumbs.item :href="route('dashboard')" wire:navigate>{{ __('Dashboard') }}
                </flux:breadcrumbs.item>
                <flux:breadcrumbs.item current>{{ __('Commercials Management') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </div>

        <!-- Add Commercial Button -->
        <div>
            <flux:button variant="primary" :href="route('commercials.create')" wire:navigate>
                <flux:icon.plus class="me-1" />
                {{ __('Add Commercial') }}
            </flux:button>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="mb-6 flex flex-col space-y-4 md:flex-row md:items-center md:space-x-4 md:space-y-0">
        <div class="flex-1">
            <flux:input wire:model.live.debounce.300ms="search" placeholder="{{ __('Search commercials...') }}"
                icon="magnifying-glass" />
        </div>
        <div class="w-full md:w-48">
            <flux:select wire:model.live="status">
                <option value="">{{ __('All Status') }}</option>
                <option value="1">{{ __('Active') }}</option>
                <option value="0">{{ __('Inactive') }}</option>
            </flux:select>
        </div>
    </div>

    <!-- Commercials Table -->
    <div class="overflow-hidden rounded-lg border border-zinc-200 bg-white dark:border-zinc-700 dark:bg-zinc-900">
        <div class="overflow-x-auto">
            <table class="w-full divide-y divide-zinc-200 dark:divide-zinc-700">
                <thead class="bg-zinc-50 dark:bg-zinc-800">
                    <tr>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Commercial ID') }}
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Name') }}
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Contact Info') }}
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Created At') }}
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Status') }}
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Actions') }}
                        </th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-zinc-200 bg-white dark:divide-zinc-700 dark:bg-zinc-900">
                    @forelse ($commercials as $commercial)
                        <tr>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-white">
                                {{ $commercial->commercial_id }}
                            </td>
                            <td class="px-6 py-4 text-sm text-zinc-900 dark:text-white">
                                {{ $commercial->name }}
                            </td>
                            <td class="px-6 py-4 text-sm text-zinc-900 dark:text-white">
                                <div>{{ $commercial->email }}</div>
                                <div>{{ $commercial->phone ?? '-' }}</div>
                            </td>

                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                {{ $commercial->created_at?->format('d/m/Y H:i') }}
                            </td>

                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                @if ($commercial->status === 1)
                                    <flux:badge variant="success">{{ __('Active') }}</flux:badge>
                                @else
                                    <flux:badge variant="danger">{{ __('Inactive') }}</flux:badge>
                                @endif
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                <div class="flex space-x-2">
                                    <flux:modal.trigger name="view-commercial-{{ $commercial->commercial_id }}">
                                        <flux:button variant="ghost" size="xs" icon="eye" />
                                    </flux:modal.trigger>

                                    <flux:button variant="ghost" size="xs" icon="pencil"
                                        :href="route('commercials.edit', $commercial->commercial_id)" wire:navigate />

                                    <flux:button variant="ghost" size="xs" icon="trash"
                                        wire:click="confirmDelete('{{ $commercial->commercial_id }}')" />
                                </div>

                                <!-- View Commercial Details Modal -->
                                <flux:modal name="view-commercial-{{ $commercial->commercial_id }}" title="{{ __('Commercial Details') }}"
                                    class="max-w-2xl w-full">
                                    <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div>
                                            <h3 class="mb-2 font-semibold">{{ __('Basic Information') }}</h3>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Commercial ID') }}:</span>
                                                {{ $commercial->commercial_id }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Name') }}:</span>
                                                {{ $commercial->name }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Email') }}:</span>
                                                {{ $commercial->email }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Phone') }}:</span>
                                                {{ $commercial->phone ?? '-' }}</div>
                                        </div>
                                        <div>
                                            <h3 class="mb-2 font-semibold">{{ __('Additional Information') }}</h3>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Tax ID') }}:</span>
                                                {{ $commercial->tax_id ?? '-' }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Postal Code') }}:</span>
                                                {{ $commercial->postal_code ?? '-' }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Country') }}:</span>
                                                {{ $commercial->country ?? '-' }}</div>

                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Status') }}:</span>
                                                {{ $commercial->status ? __('Active') : __('Inactive') }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Created') }}:</span>
                                                {{ $commercial->created_at?->format('d/m/Y H:i') }}</div>
                                        </div>
                                    </div>

                                    <div class="mt-6 flex justify-end">
                                        <flux:modal.close>
                                            <flux:button>{{ __('Close') }}</flux:button>
                                        </flux:modal.close>
                                    </div>
                                </flux:modal>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-sm text-zinc-500 dark:text-zinc-400">
                                {{ __('No commercials found.') }}
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="border-t border-zinc-200 px-4 py-3 dark:border-zinc-700">
            {{ $commercials->links() }}
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <flux:modal name="confirm-commercial-deletion" class="max-w-lg w-full">
        <div class="space-y-6">
            <div>
                <flux:heading size="lg">{{ __('Are you sure you want to delete this commercial?') }}</flux:heading>

                <flux:subheading>
                    {{ __('Once the commercial is deleted, all of its resources and data will be permanently deleted. This action cannot be undone.') }}
                </flux:subheading>
            </div>

            <div class="flex justify-end space-x-2 rtl:space-x-reverse">
                <flux:modal.close>
                    <flux:button variant="outline">{{ __('Cancel') }}</flux:button>
                </flux:modal.close>

                <flux:button variant="danger" wire:click="deleteCommercial">{{ __('Delete Commercial') }}</flux:button>
            </div>
        </div>
    </flux:modal>
</div>
