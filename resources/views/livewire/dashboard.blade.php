<div class="flex h-full w-full flex-1 flex-col gap-4 rounded-xl">
    <!-- Stats Cards -->
    <div class="grid auto-rows-min gap-4 md:grid-cols-2 lg:grid-cols-4">
        <!-- Total Players -->
        <div class="bg-gradient-to-br from-white to-blue-50 dark:from-zinc-800 dark:to-zinc-900 rounded-xl border border-blue-100 dark:border-blue-900/40 shadow-sm hover:shadow-md transition-all duration-200">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-sm font-medium text-blue-600 dark:text-blue-400">{{ __('Total Players') }}</div>
                        <div class="text-3xl font-bold mt-1">
                            {{ number_format($totalPlayers) }}
                        </div>
                    </div>
                    <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900/50 shadow-inner">
                        <svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                    </div>
                </div>
                {{-- <div class="mt-5 grid grid-cols-2 gap-4 pt-4 border-t border-blue-100 dark:border-blue-900/30">
                    <div class="flex flex-col">
                        <span class="text-2xl font-semibold text-purple-600 dark:text-purple-400">
                            {{ number_format($subscribedPlayers) }}
                        </span>
                        <span class="text-sm font-medium text-purple-500 dark:text-purple-400/70">{{ __('Subscribed') }}</span>
                    </div>
                    <div class="flex flex-col">
                        <span class="text-2xl font-semibold text-green-600 dark:text-green-400">
                            {{ number_format($activePlayers) }}
                        </span>
                        <span class="text-sm font-medium text-green-500 dark:text-green-400/70">{{ __('Active') }}</span>
                    </div>
                </div> --}}
            </div>
        </div>

        <!-- Total Clubs -->
        <div class="bg-gradient-to-br from-white to-green-50 dark:from-zinc-800 dark:to-zinc-900 rounded-xl border border-green-100 dark:border-green-900/40 shadow-sm hover:shadow-md transition-all duration-200">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-sm font-medium text-green-600 dark:text-green-400">{{ __('Total Clubs') }}</div>
                        <div class="text-3xl font-bold mt-1">
                            {{ number_format($totalClubs) }}
                        </div>
                    </div>
                    <div class="p-3 rounded-full bg-green-100 dark:bg-green-900/50 shadow-inner">
                        <svg class="h-6 w-6 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Games -->
        <div class="bg-gradient-to-br from-white to-purple-50 dark:from-zinc-800 dark:to-zinc-900 rounded-xl border border-purple-100 dark:border-purple-900/40 shadow-sm hover:shadow-md transition-all duration-200">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-sm font-medium text-purple-600 dark:text-purple-400">{{ __('Total Games') }}</div>
                        <div class="text-3xl font-bold mt-1">
                            {{ number_format($totalRecordings) }}
                        </div>
                    </div>
                    <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900/50 shadow-inner">
                        <svg class="h-6 w-6 text-purple-600 dark:text-purple-400" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Revenue -->
        <div class="bg-gradient-to-br from-white to-amber-50 dark:from-zinc-800 dark:to-zinc-900 rounded-xl border border-amber-100 dark:border-amber-900/40 shadow-sm hover:shadow-md transition-all duration-200">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="text-sm font-medium text-amber-600 dark:text-amber-400">{{ __('Total Revenue') }}</div>
                        <div class="text-3xl font-bold mt-1">
                            €{{ number_format($totalRevenue, 2) }}
                        </div>
                    </div>
                    <div class="p-3 rounded-full bg-amber-100 dark:bg-amber-900/50 shadow-inner">
                        <svg class="h-6 w-6 text-amber-600 dark:text-amber-400" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Time Period Selector -->
    <div class="flex justify-end mb-2">
        {{-- <div class="inline-flex rounded-md shadow-sm" role="group">
            <button type="button" wire:click="$set('period', 'week')" 
                class="px-4 py-2 text-sm font-medium {{ $period === 'week' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50 dark:bg-zinc-800 dark:text-gray-200 dark:hover:bg-zinc-700' }} border border-gray-200 dark:border-gray-700 rounded-l-lg">
                {{ __('Week') }}
            </button>
            <button type="button" wire:click="$set('period', 'month')"
                class="px-4 py-2 text-sm font-medium {{ $period === 'month' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50 dark:bg-zinc-800 dark:text-gray-200 dark:hover:bg-zinc-700' }} border-t border-b border-gray-200 dark:border-gray-700">
                {{ __('Month') }}
            </button>
            <button type="button" wire:click="$set('period', 'year')"
                class="px-4 py-2 text-sm font-medium {{ $period === 'year' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50 dark:bg-zinc-800 dark:text-gray-200 dark:hover:bg-zinc-700' }} border border-gray-200 dark:border-gray-700 rounded-r-lg">
                {{ __('Year') }}
            </button>
        </div> --}}
    </div>

    <!-- Charts -->
    <div class="grid gap-4 md:grid-cols-2">
        <!-- Revenue Chart -->
        <div class="bg-white dark:bg-zinc-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm p-4">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">{{ __('Revenue') }}</h3>
            <div id="revenue-chart" class="h-80"></div>
        </div>

        <!-- Players Chart -->
        <div class="bg-white dark:bg-zinc-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm p-4">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">{{ __('New Players') }}</h3>
            <div id="players-chart" class="h-80"></div>
        </div>
    </div>

    <!-- Games Chart -->
    <div class="bg-white dark:bg-zinc-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm p-4">
        <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">{{ __('Games Recorded') }}</h3>
        <div id="recordings-chart" class="h-80"></div>
    </div>

    @push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script>
        document.addEventListener('livewire:initialized', () => {
            initCharts();
            
            @this.on('chartDataUpdated', () => {
                initCharts();
            });
        });
        
        function initCharts() {
            // Revenue Chart
            const revenueOptions = {
                series: @json($revenueChartData['series']),
                chart: {
                    type: 'area',
                    height: 320,
                    toolbar: {
                        show: false
                    },
                    zoom: {
                        enabled: false
                    }
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    curve: 'smooth',
                    width: 2
                },
                colors: ['#3b82f6', '#f59e0b'],
                fill: {
                    type: 'gradient',
                    gradient: {
                        shadeIntensity: 1,
                        opacityFrom: 0.7,
                        opacityTo: 0.3,
                        stops: [0, 90, 100]
                    }
                },
                xaxis: {
                    categories: @json($revenueChartData['labels']),
                    labels: {
                        style: {
                            colors: document.documentElement.classList.contains('dark') ? '#94a3b8' : '#64748b'
                        }
                    }
                },
                yaxis: {
                    labels: {
                        formatter: function(val) {
                            return '€' + val.toFixed(2);
                        },
                        style: {
                            colors: document.documentElement.classList.contains('dark') ? '#94a3b8' : '#64748b'
                        }
                    }
                },
                tooltip: {
                    theme: document.documentElement.classList.contains('dark') ? 'dark' : 'light',
                    y: {
                        formatter: function(val) {
                            return '€' + val.toFixed(2);
                        }
                    }
                },
                legend: {
                    position: 'top',
                    horizontalAlign: 'right',
                    labels: {
                        colors: document.documentElement.classList.contains('dark') ? '#e2e8f0' : '#334155'
                    }
                }
            };
            
            if (document.getElementById('revenue-chart')) {
                const revenueChart = new ApexCharts(document.getElementById('revenue-chart'), revenueOptions);
                revenueChart.render();
            }
            
            // Players Chart
            const playersOptions = {
                series: @json($playersChartData['series']),
                chart: {
                    type: 'bar',
                    height: 320,
                    toolbar: {
                        show: false
                    }
                },
                plotOptions: {
                    bar: {
                        borderRadius: 4,
                        columnWidth: '60%',
                    }
                },
                dataLabels: {
                    enabled: false
                },
                colors: ['#8b5cf6'],
                xaxis: {
                    categories: @json($playersChartData['labels']),
                    labels: {
                        style: {
                            colors: document.documentElement.classList.contains('dark') ? '#94a3b8' : '#64748b'
                        }
                    }
                },
                yaxis: {
                    labels: {
                        formatter: function(val) {
                            return Math.round(val);
                        },
                        style: {
                            colors: document.documentElement.classList.contains('dark') ? '#94a3b8' : '#64748b'
                        }
                    }
                },
                tooltip: {
                    theme: document.documentElement.classList.contains('dark') ? 'dark' : 'light'
                }
            };
            
            if (document.getElementById('players-chart')) {
                const playersChart = new ApexCharts(document.getElementById('players-chart'), playersOptions);
                playersChart.render();
            }
            
            // Recordings Chart
            const recordingsOptions = {
                series: @json($recordingsChartData['series']),
                chart: {
                    type: 'line',
                    height: 320,
                    toolbar: {
                        show: false
                    }
                },
                stroke: {
                    curve: 'smooth',
                    width: 3
                },
                colors: ['#10b981'],
                markers: {
                    size: 4,
                    colors: ['#10b981'],
                    strokeColors: '#fff',
                    strokeWidth: 2,
                    hover: {
                        size: 6
                    }
                },
                xaxis: {
                    categories: @json($recordingsChartData['labels']),
                    labels: {
                        style: {
                            colors: document.documentElement.classList.contains('dark') ? '#94a3b8' : '#64748b'
                        }
                    }
                },
                yaxis: {
                    labels: {
                        formatter: function(val) {
                            return Math.round(val);
                        },
                        style: {
                            colors: document.documentElement.classList.contains('dark') ? '#94a3b8' : '#64748b'
                        }
                    }
                },
                tooltip: {
                    theme: document.documentElement.classList.contains('dark') ? 'dark' : 'light'
                }
            };
            
            if (document.getElementById('recordings-chart')) {
                const recordingsChart = new ApexCharts(document.getElementById('recordings-chart'), recordingsOptions);
                recordingsChart.render();
            }
        }
    </script>
    @endpush
</div>
