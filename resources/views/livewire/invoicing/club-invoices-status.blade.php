<div class="w-full">
    <div class="mb-6 flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <!-- Breadcrumb -->
        <div>
            <flux:breadcrumbs class="flex-wrap">
                <flux:breadcrumbs.item :href="route('dashboard')" wire:navigate>{{ __('Dashboard') }}
                </flux:breadcrumbs.item>
                <flux:breadcrumbs.item current>{{ __('Club Invoices Status') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="mb-6 flex flex-col space-y-4 md:flex-row md:items-center md:space-x-4 md:space-y-0">
        <div class="flex-1">
            <flux:input wire:model.live.debounce.300ms="search" placeholder="{{ __('Search clubs or invoice number...') }}"
                icon="magnifying-glass" />
        </div>
        <div class="w-full md:w-48">
            <flux:select wire:model.live="statusFilter">
                <option value="">{{ __('All Status') }}</option>
                <option value="1">{{ __('Outstanding') }}</option>
                <option value="2">{{ __('Paid') }}</option>
            </flux:select>
        </div>
    </div>

    <!-- Invoices Table -->
    <div
        class="overflow-hidden rounded-lg border border-zinc-200 bg-white shadow dark:border-zinc-700 dark:bg-zinc-800">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-zinc-200 dark:divide-zinc-700">
                <thead class="bg-zinc-50 dark:bg-zinc-900">
                    <tr>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Club Name') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Invoice Date') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Invoice Number') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Value') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Currency') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Status') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Payment Type') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Change Status to Paid') }}
                        </th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-zinc-200 bg-white dark:divide-zinc-700 dark:bg-zinc-800">
                    @forelse($invoices as $invoice)
                        <tr class="hover:bg-zinc-50 dark:hover:bg-zinc-700">
                            <td
                                class="whitespace-nowrap px-6 py-4 text-sm font-medium text-zinc-900 dark:text-zinc-100">
                                <div>
                                    <div class="font-semibold">{{ $invoice->club->club_name }}</div>
                                    <div class="text-xs text-zinc-500">{{ $invoice->club->club_id }}</div>
                                </div>
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-500 dark:text-zinc-400">
                                {{ $invoice->date ? $invoice->date->format('d/m/Y') : '-' }}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                {{ $invoice->invoice_nbr }}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                {{ number_format($invoice->value, 2) }}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                EUR
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                <span
                                    class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium
                                           {{ $invoice->status === 2
                                               ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                               : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' }}">
                                    {{ $invoice->status === 2 ? 'Paid' : 'Outstanding' }}
                                </span>
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                {{ $invoice->pay_type_text }}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                @if ($invoice->status === 1)
                                    <flux:button variant="primary" size="xs"
                                        wire:click="openPaymentModal('{{ $invoice->invoice_id }}')">
                                        {{ __('Change Status to Paid') }}
                                    </flux:button>
                                @else
                                    <span class="text-zinc-400">-</span>
                                @endif
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="8" class="px-6 py-4 text-center text-sm text-zinc-500 dark:text-zinc-400">
                                {{ __('No invoices found.') }}
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    <div class="mt-6">
        {{ $invoices->links() }}
    </div>

    <!-- Payment Confirmation Modal -->
    <flux:modal name="payment-confirmation" wire:model="showPaymentModal" class="w-full md:max-w-2xl">
        <div class="p-6">
            <h3 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100 mb-4">
                {{ __('Confirm Payment') }}
            </h3>

            <div class="space-y-4">
                <p class="text-sm text-zinc-600 dark:text-zinc-400">
                    {{ __('Please select the payment method for this invoice:') }}
                </p>

                <div class="space-y-2">
                    <flux:select wire:model="selectedPaymentType" placeholder="{{ __('Select payment type...') }}">
                        <option value="Wire">{{ __('Wire') }}</option>
                        <option value="Cash">{{ __('Cash') }}</option>
                        <option value="Other">{{ __('Other') }}</option>
                    </flux:select>
                </div>
            </div>

            <div class="mt-6 flex justify-end space-x-3">
                <flux:button variant="ghost" wire:click="closePaymentModal">
                    {{ __('Cancel') }}
                </flux:button>
                <flux:button variant="primary" wire:click="confirmPayment">
                    {{ __('Confirm Payment') }}
                </flux:button>
            </div>
        </div>
    </flux:modal>
</div>
