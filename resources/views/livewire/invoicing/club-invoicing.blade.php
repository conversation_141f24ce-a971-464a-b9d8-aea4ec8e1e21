<div class="w-full">
    <div class="mb-6 flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <!-- Breadcrumb -->
        <div>
            <flux:breadcrumbs class="flex-wrap">
                <flux:breadcrumbs.item :href="route('dashboard')" wire:navigate>{{ __('Dashboard') }}
                </flux:breadcrumbs.item>
                <flux:breadcrumbs.item current>{{ __('Club Balance & Invoicing') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="mb-6 flex flex-col space-y-4 md:flex-row md:items-center md:space-x-4 md:space-y-0">
        <div class="flex-1">
            <flux:input wire:model.live.debounce.300ms="search" placeholder="{{ __('Search clubs...') }}"
                icon="magnifying-glass" />
        </div>
        <div class="w-full md:w-48">
            <flux:select wire:model.live="status">
                <option value="">{{ __('All Status') }}</option>
                <option value="Active">{{ __('Active') }}</option>
                <option value="Suspended">{{ __('Suspended') }}</option>
            </flux:select>
        </div>
        <div class="w-full md:w-48">
            <flux:select wire:model.live="sellTokensFilter">
                <option value="">{{ __('All Clubs') }}</option>
                <option value="1">{{ __('Sell Tokens: Yes') }}</option>
                <option value="0">{{ __('Sell Tokens: No') }}</option>
            </flux:select>
        </div>
    </div>

    <!-- Clubs Table -->
    <div
        class="overflow-hidden rounded-lg border border-zinc-200 bg-white shadow dark:border-zinc-700 dark:bg-zinc-800">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-zinc-200 dark:divide-zinc-700">
                <thead class="bg-zinc-50 dark:bg-zinc-900">
                    <tr>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Club Name') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Club Status') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Start Date') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Sell Tokens') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Pay Club') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Purchased') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Redeemed') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Paid') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Paid-Redeemed') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Unpaid Tokens') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Balance') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Generate Invoice') }}
                        </th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-zinc-200 bg-white dark:divide-zinc-700 dark:bg-zinc-800">
                    @forelse($clubs as $club)
                        <tr class="hover:bg-zinc-50 dark:hover:bg-zinc-700">
                            <td
                                class="whitespace-nowrap px-6 py-4 text-sm font-medium text-zinc-900 dark:text-zinc-100">
                                <div>
                                    <div class="font-semibold">{{ $club->club_name }}</div>
                                    <div class="text-xs text-zinc-500">{{ $club->club_id }}</div>
                                </div>
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                <flux:tooltip
                                    content="{{ $club->status == 'Active' ? 'Suspended Club' : 'Activate Club' }}">
                                    <button wire:click="toggleClubStatus('{{ $club->club_id }}')"
                                        class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium
                                           {{ $club->status === 'Active'
                                               ? 'bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900 dark:hover:bg-green-800 dark:text-green-200'
                                               : 'bg-red-100 text-red-800 hover:bg-red-200 dark:bg-red-900 dark:hover:bg-red-800 dark:text-red-200' }}
                                           transition-colors duration-200 cursor-pointer">
                                        {{ $club->status }}
                                    </button>
                                </flux:tooltip>
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-500 dark:text-zinc-400">
                                {{ $club->created_at ? $club->created_at->format('d/m/Y') : '-' }}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                <span
                                    class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium
                                           {{ $club->sell_tokens
                                               ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                                               : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200' }}">
                                    {{ $club->sell_tokens ? 'Yes' : 'No' }}
                                </span>
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                @if (!$club->sell_tokens)
                                    @php
                                        $purchasedTokensCount = \App\Models\Token::where('club_id', $club->club_id)
                                            ->whereNotNull('purchased_at')
                                            ->whereNull('paid_at')
                                            ->count();
                                    @endphp
                                    @if ($purchasedTokensCount > 0)
                                        <flux:button variant="primary" size="xs"
                                            wire:click="payClub('{{ $club->club_id }}')">
                                            {{ __('Pay Club') }}
                                        </flux:button>
                                    @else
                                        <span class="text-zinc-400 text-xs">{{ __('No tokens to pay') }}</span>
                                    @endif
                                @else
                                    <span class="text-zinc-400">-</span>
                                @endif
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                €{{ number_format($club->stats['purchased'], 2) }}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                €{{ number_format($club->stats['redeemed'], 2) }}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                €{{ number_format($club->stats['purchased'] - $club->stats['unpaid_tokens'], 2) }}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                €{{ number_format($club->stats['paid_redeemed'], 2) }}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                €{{ number_format($club->stats['unpaid_tokens'], 2) }}
                            </td>
                            <td
                                class="whitespace-nowrap px-6 py-4 text-sm font-semibold
                                     ">
                                <div class="flex items-center space-x-2">
                                    <span
                                        class="{{ $club->stats['balance'] >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                                        €{{ number_format($club->stats['balance'], 2) }}
                                    </span>
                                    <flux:tooltip content="{{ __('View Club Balance Details') }}">
                                        <a href="{{ route('clubs.balance-details', $club->club_id) }}" 
                                            class="text-zinc-400 hover:text-zinc-600 dark:hover:text-zinc-300">
                                            <flux:icon.arrow-right class="w-4 h-4" />
                                        </a>
                                    </flux:tooltip>
                                </div>

                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                @if ($club->sell_tokens)
                                    @php
                                        $unpaidInvoices = \App\Models\InvoiceHeader::where('club_id', $club->club_id)
                                            ->where('status', 1)
                                            ->count();
                                    @endphp
                                    @if ($unpaidInvoices == 0)
                                        <flux:button variant="primary" size="xs"
                                            wire:click="prepareInvoice('{{ $club->club_id }}')">
                                            {{ __('Generate Invoice') }}
                                        </flux:button>
                                    @else
                                        <span
                                            class="text-zinc-400 text-xs">{{ __('Outstanding invoices exist') }}</span>
                                    @endif
                                @else
                                    <span class="text-zinc-400">-</span>
                                @endif
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="12" class="px-6 py-4 text-center text-sm text-zinc-500 dark:text-zinc-400">
                                {{ __('No clubs found.') }}
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    <div class="mt-6">
        {{ $clubs->links() }}
    </div>

    <!-- Invoice Generation Modal -->
    <flux:modal name="invoice-preview" wire:model="showInvoiceModal" class="w-full md:max-w-2xl">
        <div class="p-6">
            <h3 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100 mb-4">
                {{ __('Invoice Preview') }} - {{ $invoicePreview['club']->club_name ?? '' }}
            </h3>

            <div class="space-y-4">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-zinc-200 dark:divide-zinc-700">
                        <thead class="bg-zinc-50 dark:bg-zinc-900">
                            <tr>
                                <th
                                    class="px-4 py-2 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                                    {{ __('Item') }}
                                </th>
                                <th
                                    class="px-4 py-2 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                                    {{ __('Description') }}
                                </th>
                                <th
                                    class="px-4 py-2 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                                    {{ __('Qty') }}
                                </th>
                                <th
                                    class="px-4 py-2 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                                    {{ __('Total Price') }} (EUR)
                                </th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-zinc-200 bg-white dark:divide-zinc-700 dark:bg-zinc-800">
                            @foreach ($invoicePreview['lines'] ?? [] as $index => $line)
                                <tr>
                                    <td class="px-4 py-2 text-sm font-medium text-zinc-900 dark:text-zinc-100">
                                        {{ $index + 1 }}
                                    </td>
                                    <td class="px-4 py-2 text-sm text-zinc-900 dark:text-zinc-100">
                                        {{ $line['description'] }}
                                    </td>
                                    <td class="px-4 py-2 text-sm text-zinc-900 dark:text-zinc-100">
                                        {{ $line['qty'] }}
                                    </td>
                                    <td
                                        class="px-4 py-2 text-sm font-medium 
                                                 {{ $line['value'] >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                                        {{ number_format($line['value'], 2) }}
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                        <tfoot class="bg-zinc-50 dark:bg-zinc-900">
                            <tr>
                                <td colspan="3"
                                    class="px-4 py-2 text-sm font-semibold text-zinc-900 dark:text-zinc-100 text-right">
                                    {{ __('INVOICE VALUE') }} (EUR):
                                </td>
                                <td
                                    class="px-4 py-2 text-sm font-bold 
                                             {{ ($invoicePreview['total'] ?? 0) >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                                    {{ number_format($invoicePreview['total'] ?? 0, 2) }}
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>

            <div class="mt-6 flex justify-end space-x-3">
                <flux:button variant="ghost" wire:click="closeInvoiceModal">
                    {{ __('Cancel') }}
                </flux:button>
                <flux:button variant="primary" wire:click="confirmInvoiceGeneration">
                    {{ __('Generate & Send Invoice') }}
                </flux:button>
            </div>
        </div>
    </flux:modal>

    <!-- Payment Modal -->
    <flux:modal name="payment-modal" wire:model="showPaymentModal" class="w-full md:max-w-2xl">
        <div class="p-6">
            <h3 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100 mb-4">
                {{ __('Pay Club') }} - {{ $selectedClubForPayment->club_name ?? '' }}
            </h3>

            <div class="space-y-4">
                <div>
                    <p class="text-sm text-zinc-600 dark:text-zinc-400 mb-4">
                        {{ __('Select payment type for this club:') }}
                    </p>

                    <flux:select wire:model="paymentType" label="{{ __('Payment Type') }}">
                        <option value="0">{{ __('Wire') }}</option>
                        <option value="1">{{ __('Cash') }}</option>
                    </flux:select>
                </div>

                @if ($selectedClubForPayment)
                    @php
                        $tokensToPayCount = \App\Models\Token::where('club_id', $selectedClubForPayment->club_id)
                            ->whereNotNull('purchased_at')
                            ->whereNull('paid_at')
                            ->count();

                        $tokensValue = \App\Models\Token::where('club_id', $selectedClubForPayment->club_id)
                            ->whereNotNull('purchased_at')
                            ->whereNull('paid_at')
                            ->with('tokenType')
                            ->get()
                            ->sum(function ($token) use ($selectedClubForPayment) {
                                $tokenValue = $token->tokenType ? $token->tokenType->value_in_eur : 0;
                                $commission = $selectedClubForPayment->commission / 100;
                                return $tokenValue * $commission;
                            });
                    @endphp

                    <div class="bg-zinc-50 dark:bg-zinc-900 p-4 rounded-lg">
                        <div class="text-sm text-zinc-600 dark:text-zinc-400">
                            <p><strong>{{ __('Tokens to pay:') }}</strong> {{ $tokensToPayCount }}</p>
                            <p><strong>{{ __('Total amount:') }}</strong> €{{ number_format($tokensValue, 2) }}</p>
                            <p><strong>{{ __('Commission:') }}</strong> {{ $selectedClubForPayment->commission }}%</p>
                        </div>
                    </div>
                @endif
            </div>

            <div class="mt-6 flex justify-end space-x-3">
                <flux:button variant="ghost" wire:click="closePaymentModal">
                    {{ __('Cancel') }}
                </flux:button>
                <flux:button variant="primary" wire:click="processPayment">
                    {{ __('Submit Payment') }}
                </flux:button>
            </div>
        </div>
    </flux:modal>

    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="fixed bottom-4 right-4 z-50">
            <div class="rounded-md bg-green-50 p-4 dark:bg-green-900">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd"
                                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-green-800 dark:text-green-200">
                            {{ session('message') }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="fixed bottom-4 right-4 z-50">
            <div class="rounded-md bg-red-50 p-4 dark:bg-red-900">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd"
                                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-red-800 dark:text-red-200">
                            {{ session('error') }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
