<div class="w-full">
    <div class="mb-6">
        <flux:breadcrumbs class="flex-wrap">
            <flux:breadcrumbs.item :href="route('dashboard')" wire:navigate>{{ __('Dashboard') }}
            </flux:breadcrumbs.item>
            <flux:breadcrumbs.item :href="route('players.index')" wire:navigate>{{ __('Players Management') }}
            </flux:breadcrumbs.item>
            <flux:breadcrumbs.item current>{{ $isEdit ? __('Edit Player') : __('Add Player') }}</flux:breadcrumbs.item>
        </flux:breadcrumbs>
    </div>

    <div class="rounded-lg border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
        <form wire:submit="save" class="space-y-6">
            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                <!-- Basic Information Section -->
                <div class="space-y-6">
                    <flux:heading size="sm">{{ __('Basic Information') }}</flux:heading>

                    @if ($isEdit)
                        <flux:input wire:model="player_id" :label="__('Player ID')" disabled readonly />
                    @endif

                    <flux:input wire:model="first_name" :label="__('First Name')" required
                        :error="$errors->first('first_name')" />

                    <flux:input wire:model="last_name" :label="__('Last Name')" required
                        :error="$errors->first('last_name')" />

                    <flux:input wire:model="email" :label="__('Email')" type="email" required
                        :error="$errors->first('email')" />

                    <div class="flex space-x-3">
                        <div class="w-1/3">
                            <flux:select wire:model="country_code" :label="__('Country Code')"
                                :error="$errors->first('country_code')">
                                <option value="">{{ __('Select') }}</option>
                                <option value="1">United States (+1)</option>
                                <option value="44">United Kingdom (+44)</option>
                                <option value="34">Spain (+34)</option>
                                <option value="33">France (+33)</option>
                                <option value="39">Italy (+39)</option>
                                <option value="49">Germany (+49)</option>
                                <option value="31">Netherlands (+31)</option>
                                <option value="32">Belgium (+32)</option>
                                <option value="351">Portugal (+351)</option>
                                <option value="41">Switzerland (+41)</option>
                                <option value="43">Austria (+43)</option>
                                <option value="46">Sweden (+46)</option>
                                <option value="47">Norway (+47)</option>
                                <option value="45">Denmark (+45)</option>
                                <option value="358">Finland (+358)</option>
                                <option value="48">Poland (+48)</option>
                                <option value="420">Czech Republic (+420)</option>
                                <option value="36">Hungary (+36)</option>
                                <option value="30">Greece (+30)</option>
                                <option value="7">Russia (+7)</option>
                                <option value="971">United Arab Emirates (+971)</option>
                                <option value="966">Saudi Arabia (+966)</option>
                                <option value="974">Qatar (+974)</option>
                                <option value="965">Kuwait (+965)</option>
                                <option value="973">Bahrain (+973)</option>
                                <option value="968">Oman (+968)</option>
                                <option value="961">Lebanon (+961)</option>
                                <option value="962">Jordan (+962)</option>
                                <option value="20">Egypt (+20)</option>
                                <option value="212">Morocco (+212)</option>
                                <option value="216">Tunisia (+216)</option>
                                <option value="213">Algeria (+213)</option>
                                <option value="27">South Africa (+27)</option>
                                <option value="234">Nigeria (+234)</option>
                                <option value="254">Kenya (+254)</option>
                                <option value="91">India (+91)</option>
                                <option value="86">China (+86)</option>
                                <option value="81">Japan (+81)</option>
                                <option value="82">South Korea (+82)</option>
                                <option value="65">Singapore (+65)</option>
                                <option value="60">Malaysia (+60)</option>
                                <option value="66">Thailand (+66)</option>
                                <option value="62">Indonesia (+62)</option>
                                <option value="63">Philippines (+63)</option>
                                <option value="61">Australia (+61)</option>
                                <option value="64">New Zealand (+64)</option>
                                <option value="55">Brazil (+55)</option>
                                <option value="52">Mexico (+52)</option>
                                <option value="54">Argentina (+54)</option>
                                <option value="56">Chile (+56)</option>
                                <option value="57">Colombia (+57)</option>
                                <option value="51">Peru (+51)</option>
                                <option value="58">Venezuela (+58)</option>
                            </flux:select>
                        </div>

                        <div class="w-2/3">
                            <flux:input wire:model="phone" :label="__('Phone Number')"
                                :error="$errors->first('phone')" />
                        </div>
                    </div>



                    <flux:select wire:model="status" :label="__('Status')" required :error="$errors->first('status')"
                        x-on:change="$wire.status = $event.target.value">
                        <option value="Active">{{ __('Active') }}</option>
                        <option value="Suspended">{{ __('Suspended') }}</option>
                    </flux:select>

                    <!-- Suspension Reason Textarea (shown only when status is Suspended) -->
                    <div x-show="$wire.status === 'Suspended'" x-transition>
                        <flux:textarea wire:model="suspension_reason" :label="__('Reason for Suspension')"
                            :placeholder="__('Enter the reason for suspension (optional)')" rows="3"
                            :error="$errors->first('suspension_reason')" :helper="__('Maximum 254 characters')" />
                    </div>

                    <flux:checkbox wire:model="is_email_verified" :label="__('Email Verified')"
                        :error="$errors->first('is_email_verified')" />

                    <flux:checkbox wire:model="is_phone_verified" :label="__('Phone Verified')"
                        :error="$errors->first('is_phone_verified')" />



                </div>

                <!-- Location & Additional Details Section -->
                <div class="space-y-6">
                    <flux:heading size="sm">{{ __('Location & Additional Details') }}</flux:heading>

                    <!-- Searchable Country Select -->
                    <div class="relative">
                        <label for="country_search"
                            class="mb-1 block text-sm font-medium text-zinc-700 dark:text-zinc-300">
                            {{ __('Country') }}
                        </label>
                        <div class="relative">
                            <input type="text" id="country_search" wire:model.live.debounce.300ms="countrySearch"
                                wire:click="$set('showCountryDropdown', true)"
                                placeholder="{{ __('Search for a country...') }}"
                                class="block w-full rounded-lg border border-zinc-300 bg-white px-3 py-2 text-zinc-900 placeholder-zinc-500 focus:border-primary-500 focus:ring-primary-500 dark:border-zinc-600 dark:bg-zinc-800 dark:text-white dark:placeholder-zinc-400 dark:focus:border-primary-500 dark:focus:ring-primary-500"
                                autocomplete="off">
                            @if ($selectedCountry)
                                <div class="absolute right-2 top-2">
                                    <span
                                        class="inline-flex items-center rounded-full bg-primary-100 px-2.5 py-0.5 text-xs font-medium text-primary-800 dark:bg-primary-900 dark:text-primary-200">
                                        {{ $selectedCountry->name }}
                                    </span>
                                </div>
                            @endif

                            @if ($showCountryDropdown && count($countries) > 0)
                                <div class="absolute z-10 mt-1 w-full rounded-md bg-white shadow-lg dark:bg-zinc-800">
                                    <ul class="max-h-60 overflow-auto rounded-md py-1 text-base sm:text-sm">
                                        @foreach ($countries as $country)
                                            <li wire:click="selectCountry({{ $country->id }}, '{{ $country->name }}')"
                                                class="relative cursor-pointer select-none py-2 pl-3 pr-9 hover:bg-zinc-100 dark:hover:bg-zinc-700">
                                                {{ $country->name }}
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif
                        </div>
                        @error('country')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <flux:input wire:model="postal_code" :label="__('Postal Code')"
                        :error="$errors->first('postal_code')" />

                    <flux:input wire:model="credits" :label="__('Credits')" type="number" min="0"
                        :error="$errors->first('credits')" />

                    <flux:input wire:model="trial_credits" :label="__('Free Credits')" type="number" min="0"
                        :error="$errors->first('trial_credits')" />

                    <flux:select wire:model="sex" :label="__('Gender')" :error="$errors->first('sex')">
                        <option value="">{{ __('Select') }}</option>
                        <option value="Male">{{ __('Male') }}</option>
                        <option value="Female">{{ __('Female') }}</option>
                        <option value="Other">{{ __('Other') }}</option>
                    </flux:select>

                    <flux:select wire:model="rating_type" :label="__('Rating Type')"
                        :error="$errors->first('rating_type')">
                        <option value="Playtomic">{{ __('Playtomic') }}</option>
                        <option value="Matchi">{{ __('Matchi') }}</option>
                    </flux:select>
                </div>
            </div>

            <!-- Password Section -->
            <div class="mt-6 border-t border-zinc-200 pt-6 dark:border-zinc-700">
                <flux:heading size="sm">{{ __('Authentication') }}</flux:heading>

                <div class="mt-4 grid grid-cols-1 gap-6 md:grid-cols-2">
                    <flux:input wire:model="password" :label="__('Password')" type="password" :required="!$isEdit"
                        :error="$errors->first('password')"
                        :helper="$isEdit ? __('Leave blank to keep current password') : ''" />

                    <flux:input wire:model="password_confirmation" :label="__('Confirm Password')" type="password"
                        :required="!$isEdit" :error="$errors->first('password_confirmation')" />
                </div>
            </div>

            <!-- Form Actions -->
            <div class="mt-6 flex justify-end space-x-3">
                <flux:button variant="outline" :href="route('players.index')" wire:navigate>
                    {{ __('Cancel') }}
                </flux:button>

                <flux:button variant="primary" type="submit">
                    {{ $isEdit ? __('Update Player') : __('Create Player') }}
                </flux:button>
            </div>
        </form>
    </div>
</div>
