<div class="w-full">
    <!-- Header -->
    <div class="mb-6 flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <!-- Breadcrumb -->
        <div>
            <flux:breadcrumbs class="flex-wrap">
                <flux:breadcrumbs.item :href="route('dashboard')" wire:navigate>{{ __('Dashboard') }}
                </flux:breadcrumbs.item>
                <flux:breadcrumbs.item :href="route('players.index')" wire:navigate>{{ __('Players Management') }}
                </flux:breadcrumbs.item>
                <flux:breadcrumbs.item current>{{ __('Statistics') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </div>

        <!-- Back Button -->
        <div>
            <flux:button variant="ghost" :href="route('players.index')" wire:navigate icon="arrow-left">
                {{ __('Back to Players') }}
            </flux:button>
        </div>
    </div>

    <!-- Player Info -->
    <div class="mb-6 rounded-lg border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
        <div class="flex items-center space-x-4">
            <img src="{{ $player->photo_url }}" alt="{{ $player->first_name }}" class="h-16 w-16 rounded-full object-cover">
            <div>
                <h2 class="text-xl font-semibold text-zinc-900 dark:text-zinc-100">
                    {{ $player->first_name }} {{ $player->last_name }}
                </h2>
                <p class="text-sm text-zinc-500 dark:text-zinc-400">{{ __('Player ID') }}: {{ $player->player_id }}</p>
                <p class="text-sm text-zinc-500 dark:text-zinc-400">{{ __('Total Games') }}: {{ $totalGames }}</p>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="mb-6">
        <div class="flex md:justify-end justify-center space-x-1 rounded-lg bg-zinc-100 p-1 dark:bg-zinc-800">
            <button 
                wire:click="setActiveTab('player-stats')"
                class="max-w-60 w-full rounded-md px-3 py-2 text-sm font-medium transition-colors {{ $activeTab === 'player-stats' ? 'bg-blue-600 text-white shadow-sm' : 'text-zinc-500 hover:text-zinc-700 dark:text-zinc-400 dark:hover:text-zinc-200' }}">
                {{ __('Player Stats') }}
            </button>
            <button 
                wire:click="setActiveTab('game-stats')"
                class="max-w-60 w-full rounded-md px-3 py-2 text-sm font-medium transition-colors {{ $activeTab === 'game-stats' ? 'bg-blue-600 text-white shadow-sm' : 'text-zinc-500 hover:text-zinc-700 dark:text-zinc-400 dark:hover:text-zinc-200' }}">
                {{ __('Game Stats') }}
            </button>
        </div>
    </div>

    <!-- Player Stats Tab -->
    @if($activeTab === 'player-stats')
        <div class="space-y-6">
            <!-- Rating Progress Chart -->
            @if(!empty($dailyRatings))
                <div class="rounded-lg border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                    <div class="mb-4 flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100">
                            {{ __('Rating Progress') }} ({{ count($dailyRatings) }}/7 {{ __('matches') }})
                        </h3>
                        <div class="flex space-x-2">
                            <span class="rounded-full bg-zinc-900 px-3 py-1 text-xs font-medium text-white dark:bg-zinc-100 dark:text-zinc-900">
                                {{ __('Platomic') }}
                            </span>
                            <span class="rounded-full bg-zinc-200 px-3 py-1 text-xs font-medium text-zinc-700 dark:bg-zinc-700 dark:text-zinc-300">
                                {{ __('MATCHi') }}
                            </span>
                        </div>
                    </div>
                    
                    <!-- Chart Container -->
                    <div wire:ignore class="h-64 w-full">
                        <canvas id="ratingChart" width="400" height="200"></canvas>
                    </div>
                    
                    <div class="mt-4 text-center">
                        <p class="text-lg font-semibold text-zinc-900 dark:text-zinc-100">
                            {{ __('Your Overall Rating is') }} {{ $overallRating }}
                        </p>
                    </div>
                </div>
            @endif

            <!-- Stats Cards -->
            <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                @foreach($playerStats as $stat)
                    @if(in_array($stat['parameter']->id, [1, 2, 3, 4])) <!-- HITS, FOREHAND, BACKHAND, CROSS COURT -->
                        <div class="rounded-lg border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <h4 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100">
                                        {{ strtoupper($stat['parameter']->getLocalizedLabelAttribute()) }}
                                    </h4>
                                    <div class="mt-2 space-y-1 text-sm text-zinc-600 dark:text-zinc-400">
                                        <div>{{ __('NBR') }}: {{ $stat['total'] }}</div>
                                        <div>{{ __('Success') }}: {{ $stat['success'] }}</div>
                                        <div>{{ __('Winner') }}: {{ $stat['winner'] }}</div>
                                        <div>{{ __('Loss') }}: {{ $stat['loss'] }}</div>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <!-- Donut Chart -->
                                    <div wire:ignore class="relative h-20 w-20">
                                        <canvas class="stat-chart" 
                                                data-total="{{ $stat['total'] }}" 
                                                data-success="{{ $stat['success'] }}" 
                                                data-loss="{{ $stat['loss'] }}"
                                                width="80" height="80"></canvas>
                                        <div class="absolute inset-0 flex items-center justify-center">
                                            <span class="text-lg font-bold text-zinc-900 dark:text-zinc-100">{{ $stat['total'] }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                @endforeach
            </div>

            <!-- Comments Button -->
            <div class="flex justify-center">
                <flux:modal.trigger name="comments-modal">
                    <flux:button variant="primary" icon="chat-bubble-left-right">
                        {{ __('View Comments') }} ({{ count($comments) }})
                    </flux:button>
                </flux:modal.trigger>
            </div>

            <!-- Comments Modal -->
            <flux:modal name="comments-modal" class="md:w-96">
                <div class="space-y-6">
                    <div>
                        <flux:heading size="lg">{{ __('Coach Comments') }}</flux:heading>
                        <flux:subheading>{{ __('Last 7 matches comments') }}</flux:subheading>
                    </div>

                    <div class="space-y-4 max-h-96 overflow-y-auto">
                        @forelse($comments as $comment)
                            <div class="rounded-lg border border-zinc-200 bg-zinc-50 p-4 dark:border-zinc-700 dark:bg-zinc-800">
                                <p class="text-sm text-zinc-900 dark:text-zinc-100">{{ $comment['message'] }}</p>
                                <div class="mt-2 flex justify-between text-xs text-zinc-500 dark:text-zinc-400">
                                    <span>{{ $comment['date'] }} {{ $comment['time'] }}</span>
                                    @if($comment['game_date'])
                                        <span>{{ __('Game') }}: {{ $comment['game_date'] }}</span>
                                    @endif
                                </div>
                            </div>
                        @empty
                            <div class="text-center text-zinc-500 dark:text-zinc-400">
                                {{ __('No comments available') }}
                            </div>
                        @endforelse
                    </div>

                    <div class="flex justify-end">
                        <flux:modal.close>
                            <flux:button>{{ __('Close') }}</flux:button>
                        </flux:modal.close>
                    </div>
                </div>
            </flux:modal>
        </div>
    @endif

    <!-- Game Stats Tab -->
    @if($activeTab === 'game-stats')
        <div class="space-y-4">
            <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                @foreach($gameStats as $stat)
                    @if(in_array($stat['parameter']->id, [1, 2, 3, 4, 5, 6, 7, 8])) <!-- Main stats -->
                        <div class="rounded-lg border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <h4 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100">
                                        {{ strtoupper($stat['parameter']->getLocalizedLabelAttribute()) }}
                                    </h4>
                                    <div class="mt-2 space-y-1 text-sm text-zinc-600 dark:text-zinc-400">
                                        <div>{{ __('NBR') }}: {{ $stat['total'] }}</div>
                                        <div>{{ __('Success') }}: {{ $stat['success'] }}</div>
                                        <div>{{ __('Winner') }}: {{ $stat['winner'] }}</div>
                                        <div>{{ __('Loss') }}: {{ $stat['loss'] }}</div>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <!-- Donut Chart -->
                                    <div wire:ignore class="relative h-20 w-20">
                                        <canvas class="stat-chart" 
                                                data-total="{{ $stat['total'] }}" 
                                                data-success="{{ $stat['success'] }}" 
                                                data-loss="{{ $stat['loss'] }}"
                                                width="80" height="80"></canvas>
                                        <div class="absolute inset-0 flex items-center justify-center">
                                            <span class="text-lg font-bold text-zinc-900 dark:text-zinc-100">{{ $stat['total'] }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                @endforeach
            </div>
        </div>
    @endif
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Rating Progress Chart
    @if(!empty($dailyRatings))
        const ctx = document.getElementById('ratingChart').getContext('2d');
        const ratingData = @json($dailyRatings);
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ratingData.map((item, index) => index + 1),
                datasets: [{
                    label: 'Rating',
                    data: ratingData.map(item => item.rating),
                    borderColor: '#2563eb',
                    backgroundColor: '#2563eb',
                    borderWidth: 3,
                    fill: false,
                    tension: 0.4,
                    pointBackgroundColor: '#2563eb',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Games'
                        },
                        grid: {
                            color: '#e5e7eb'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        max: 8,
                        title: {
                            display: true,
                            text: 'Rating'
                        },
                        grid: {
                            color: '#e5e7eb'
                        }
                    }
                }
            }
        });
    @endif

    // Donut Charts for Stats
    document.querySelectorAll('.stat-chart').forEach(canvas => {
        const ctx = canvas.getContext('2d');
        const total = parseFloat(canvas.dataset.total) || 0;
        const success = parseFloat(canvas.dataset.success) || 0;
        const loss = parseFloat(canvas.dataset.loss) || 0;
        
        const successPercentage = total > 0 ? (success / total) * 100 : 0;
        const lossPercentage = total > 0 ? (loss / total) * 100 : 0;
        const otherPercentage = 100 - successPercentage - lossPercentage;
        
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                datasets: [{
                    data: [successPercentage, lossPercentage, otherPercentage],
                    backgroundColor: ['#10b981', '#ef4444', '#e5e7eb'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: false,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        enabled: false
                    }
                },
                cutout: '70%'
            }
        });
    });
});
</script>
@endpush
