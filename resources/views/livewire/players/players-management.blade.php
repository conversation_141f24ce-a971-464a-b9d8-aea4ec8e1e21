<div class="w-full">
    <div class="mb-6 flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <!-- Breadcrumb -->
        <div>
            <flux:breadcrumbs class="flex-wrap">
                <flux:breadcrumbs.item :href="route('dashboard')" wire:navigate>{{ __('Dashboard') }}
                </flux:breadcrumbs.item>
                <flux:breadcrumbs.item current>{{ __('Players Management') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </div>

        <!-- Add Player Button -->
        <div>
            <flux:button variant="primary" :href="route('players.create')" wire:navigate>
                {{ __('Add Player') }}
            </flux:button>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="mb-6 flex flex-col space-y-4 md:flex-row md:items-center md:space-x-4 md:space-y-0">
        <div class="flex-1">
            <flux:input wire:model.live.debounce.300ms="search" placeholder="{{ __('Search players...') }}"
                icon="magnifying-glass" />
        </div>
        <div class="w-full md:w-48">
            <flux:select wire:model.live="status">
                <option value="">{{ __('All Status') }}</option>
                <option value="Active">{{ __('Active') }}</option>
                <option value="Suspended">{{ __('Suspended') }}</option>
            </flux:select>
        </div>
        <div class="w-full md:w-48">
            <flux:select wire:model.live="subscription">
                <option value="">{{ __('All Subscriptions') }}</option>
                <option value="subscribed">{{ __('Subscribed') }}</option>
                <option value="not_subscribed">{{ __('Not Subscribed') }}</option>
            </flux:select>
        </div>
    </div>

    <!-- Session Message -->
    {{-- @if (session()->has('message'))
        <div class="mb-4">
            <flux:alert variant="success" dismissable>
                {{ session('message') }}
            </flux:alert>
        </div>
    @endif --}}

    <!-- Players Table -->
    <div class="overflow-hidden rounded-lg border border-zinc-200 bg-white dark:border-zinc-700 dark:bg-zinc-900">
        <div class="overflow-x-auto">
            <table class="w-full divide-y divide-zinc-200 dark:divide-zinc-700">
                <thead class="bg-zinc-50 dark:bg-zinc-800">
                    <tr>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Player ID') }}
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Name') }}
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Contact Info') }}
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Subscription') }}
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Created At') }}
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Status') }}
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Actions') }}
                        </th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-zinc-200 bg-white dark:divide-zinc-700 dark:bg-zinc-900">
                    @forelse ($players as $player)
                        <tr>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-white">
                                {{ $player->player_id }}
                            </td>
                            <td class="px-6 py-4 text-sm text-zinc-900 dark:text-white">
                                <div class="flex items-center">
                                    <div class="h-10 w-10 flex-shrink-0">
                                        <img class="h-10 w-10 rounded-full object-cover"
                                            src="{{ $player->photo ? $player->photo_url : asset('images/default-player.webp') }}"
                                            alt="{{ $player->full_name }}">
                                    </div>
                                    <div class="ml-4">
                                        <div class="font-medium">{{ $player->full_name }}</div>
                                        <div class="text-xs text-zinc-500 dark:text-zinc-400">
                                            {{ $player->sex ?? 'Not specified' }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 text-sm text-zinc-900 dark:text-white">
                                <div>{{ $player->email }}</div>
                                <div>
                                    {{ $player->phone ? ($player->country_code ? '+' . $player->country_code . ' ' : '') . $player->phone : '-' }}
                                </div>
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                @if ($player->subscribed)
                                    <flux:badge variant="success">{{ __('Subscribed') }}</flux:badge>
                                @else
                                    <flux:badge variant="secondary">{{ __('Not Subscribed') }}</flux:badge>
                                @endif
                            </td>

                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                {{ $player->created_at?->format('d/m/Y H:i') }}
                            </td>

                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                <div class="flex items-center space-x-2">
                                    @if ($player->status === 'Active')
                                        <flux:badge variant="success">{{ __('Active') }}</flux:badge>
                                    @else
                                        <flux:badge variant="danger">{{ __('Suspended') }}</flux:badge>
                                        @if ($player->status === 'Suspended' && $player->suspension_reason)
                                            <div class="relative" x-data="{ showTooltip: false }">
                                                <button
                                                    @mouseenter="showTooltip = true"
                                                    @mouseleave="showTooltip = false"
                                                    class="text-sky-500 hover:text-sky-700"
                                                >
                                                    <flux:icon.information-circle class="w-4 h-4" />
                                                </button>
                                                <div
                                                    x-show="showTooltip"
                                                    x-transition
                                                    class="absolute z-10 px-3 py-2 text-sm text-white bg-gray-900 rounded-lg shadow-lg bottom-full left-1/2 transform -tranzinc-x-1/2 mb-2 w-64"
                                                    style="display: none;"
                                                >
                                                    <div class="font-semibold mb-1">{{ __('Suspension Reason:') }}</div>
                                                    <div class="whitespace-pre-line">{{ $player->suspension_reason }}</div>
                                                    <div class="absolute top-full left-1/2 transform -tranzinc-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                                                </div>
                                            </div>
                                        @endif
                                    @endif
                                </div>
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                <div class="flex space-x-2">
                                    <flux:modal.trigger name="view-player-{{ $player->player_id }}">
                                        <flux:button variant="ghost" size="xs" icon="eye" />
                                    </flux:modal.trigger>

                                    <flux:button variant="ghost" size="xs" icon="pencil" :href="route('players.edit', $player->player_id)" wire:navigate />
                                </div>

                                <!-- View Player Details Modal -->
                                <flux:modal name="view-player-{{ $player->player_id }}"
                                    title="{{ __('Player Details') }}" class="max-w-2xl w-full">
                                    <div class="flex items-center mb-6">
                                        <div class="h-16 w-16 flex-shrink-0">
                                            <img class="h-16 w-16 rounded-full object-cover"
                                                src="{{ $player->photo ? $player->photo_url : asset('images/default-player.webp') }}"
                                                alt="{{ $player->full_name }}">
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-xl font-medium">{{ $player->full_name }}</div>
                                            <div class="text-sm text-zinc-500 dark:text-zinc-400">
                                                {{ $player->player_id }}</div>
                                        </div>
                                    </div>

                                    <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div>
                                            <h3 class="mb-2 font-semibold">{{ __('Basic Information') }}</h3>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col">
                                                <span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('First Name') }}:</span>
                                                {{ $player->first_name }}
                                            </div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Last Name') }}:</span>
                                                {{ $player->last_name }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Email') }}:</span>
                                                {{ $player->email }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Phone') }}:</span>
                                                {{ $player->phone ? ($player->country_code ? '+' . $player->country_code . ' ' : '') . $player->phone : '-' }}
                                            </div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Gender') }}:</span>
                                                {{ $player->sex ?? '-' }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Status') }}:</span>
                                                {{ $player->status }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Subscription') }}:</span>
                                                {{ $player->subscribed ? 'Subscribed' : 'Not Subscribed' }}</div>
                                        </div>

                                        <div>
                                            <h3 class="mb-2 font-semibold">{{ __('Additional Details') }}</h3>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Country') }}:</span>
                                                {{ $player->country ?? '-' }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Postal Code') }}:</span>
                                                {{ $player->postal_code ?? '-' }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Credits') }}:</span>
                                                {{ $player->credits }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Email Verified') }}:</span>
                                                {{ $player->is_email_verified ? 'Yes' : 'No' }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Phone Verified') }}:</span>
                                                {{ $player->is_phone_verified ? 'Yes' : 'No' }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Language') }}:</span>
                                                {{ $player->language ?? '-' }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Joined') }}:</span>
                                                {{ $player->created_at?->format('d/m/Y H:i') }}</div>
                                        </div>
                                    </div>

                                    <div class="mt-6 flex justify-end">
                                        <flux:modal.close>
                                            <flux:button>{{ __('Close') }}</flux:button>
                                        </flux:modal.close>
                                    </div>
                                </flux:modal>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-sm text-zinc-500 dark:text-zinc-400">
                                {{ __('No players found.') }}
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="border-t border-zinc-200 px-4 py-3 dark:border-zinc-700">
            {{ $players->links() }}
        </div>
    </div>


</div>
