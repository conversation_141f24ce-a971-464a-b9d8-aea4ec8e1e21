<div>
    <flux:modal name="confirm-password-reset" focusable class="max-w-lg w-full">
        <form wire:submit="resetPassword" class="space-y-6">
            <div>
                <flux:heading size="lg">{{ __('Reset Player Password') }}</flux:heading>
                
                <flux:subheading>
                    {{ __('Enter a new password for this player.') }}
                </flux:subheading>
            </div>
            
            <flux:input 
                wire:model="password" 
                :label="__('New Password')" 
                type="password" 
                required 
                :error="$errors->first('password')"
            />
            
            <flux:input 
                wire:model="password_confirmation" 
                :label="__('Confirm Password')" 
                type="password" 
                required 
                :error="$errors->first('password_confirmation')"
            />
            
            <div class="flex justify-end space-x-2 rtl:space-x-reverse">
                <flux:modal.close>
                    <flux:button variant="outline">{{ __('Cancel') }}</flux:button>
                </flux:modal.close>
                
                <flux:button variant="primary" type="submit">{{ __('Reset Password') }}</flux:button>
            </div>
        </form>
    </flux:modal>
</div>
