<div class="w-full">
    <div class="mb-6 flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <!-- Breadcrumb -->
        <div>
            <flux:breadcrumbs class="flex-wrap">
                <flux:breadcrumbs.item :href="route('dashboard')" wire:navigate>{{ __('Dashboard') }}
                </flux:breadcrumbs.item>
                <flux:breadcrumbs.item current>{{ __('Sponsors Management') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </div>

        <!-- Add Sponsor Button -->
        <div>
            <flux:button variant="primary" :href="route('sponsors.create')" wire:navigate>
                <flux:icon.plus class="me-1" />
                {{ __('Add Sponsor') }}
            </flux:button>
        </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="mb-6 rounded-lg border border-zinc-200 bg-white p-4 dark:border-zinc-700 dark:bg-zinc-900">
        <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
            <flux:input wire:model.live="search" :placeholder="__('Search sponsors...')" />

            <div>
                <flux:select wire:model.live="status" :placeholder="__('Filter by status')">
                    <option value="">{{ __('All Statuses') }}</option>
                    <option value="1">{{ __('Active') }}</option>
                    <option value="0">{{ __('Inactive') }}</option>
                </flux:select>
            </div>
        </div>
    </div>

    <!-- Sponsors Table -->
    <div class="overflow-hidden rounded-lg border border-zinc-200 bg-white dark:border-zinc-700 dark:bg-zinc-900">
        <div class="overflow-x-auto">
            <table class="w-full divide-y divide-zinc-200 dark:divide-zinc-700">
                <thead class="bg-zinc-50 dark:bg-zinc-800">
                    <tr>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Sponsor ID') }}
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Company Name') }}
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Commercial') }}
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Contact Info') }}
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Status') }}
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Actions') }}
                        </th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-zinc-200 bg-white dark:divide-zinc-700 dark:bg-zinc-900">
                    @forelse ($sponsors as $sponsor)
                        <tr class="hover:bg-zinc-50 dark:hover:bg-zinc-800">
                            <td class="whitespace-nowrap px-6 py-4 text-sm font-medium text-zinc-900 dark:text-zinc-100">
                                {{ $sponsor->sponsor_id }}
                            </td>
                            <td class="px-6 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                <div class="flex items-center">
                                    @if($sponsor->logo)
                                        <img src="{{ $sponsor->logo_url }}" alt="{{ $sponsor->company_name }}" 
                                             class="h-8 w-8 rounded-full mr-3 object-cover">
                                    @endif
                                    <div>
                                        <div class="font-medium">{{ $sponsor->company_name }}</div>
                                        @if($sponsor->contact)
                                            <div class="text-zinc-500 dark:text-zinc-400">{{ $sponsor->contact }}</div>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                {{ $sponsor->commercial->name ?? 'N/A' }}
                            </td>
                            <td class="px-6 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                <div>{{ $sponsor->email }}</div>
                                @if($sponsor->phone)
                                    <div class="text-zinc-500 dark:text-zinc-400">{{ $sponsor->phone }}</div>
                                @endif
                            </td>

                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                @if ($sponsor->status === 1)
                                    <flux:badge variant="success">{{ __('Active') }}</flux:badge>
                                @else
                                    <flux:badge variant="danger">{{ __('Inactive') }}</flux:badge>
                                @endif
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                <div class="flex space-x-2">
                                    <flux:modal.trigger name="view-sponsor-{{ $sponsor->sponsor_id }}">
                                        <flux:button variant="ghost" size="xs" icon="eye" />
                                    </flux:modal.trigger>

                                    <flux:button variant="ghost" size="xs" icon="pencil"
                                        :href="route('sponsors.edit', $sponsor->sponsor_id)" wire:navigate />

                                    <flux:button variant="ghost" size="xs" icon="trash"
                                        wire:click="confirmDelete('{{ $sponsor->sponsor_id }}')" />
                                </div>

                                <!-- View Sponsor Details Modal -->
                                <flux:modal name="view-sponsor-{{ $sponsor->sponsor_id }}" title="{{ __('Sponsor Details') }}"
                                    class="max-w-2xl w-full">
                                    <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div>
                                            <h3 class="mb-2 font-semibold">{{ __('Basic Information') }}</h3>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Sponsor ID') }}:</span>
                                                {{ $sponsor->sponsor_id }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Company Name') }}:</span>
                                                {{ $sponsor->company_name }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Contact') }}:</span>
                                                {{ $sponsor->contact ?? 'N/A' }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Email') }}:</span>
                                                {{ $sponsor->email }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Phone') }}:</span>
                                                {{ $sponsor->phone ?? 'N/A' }}</div>
                                        </div>
                                        <div>
                                            <h3 class="mb-2 font-semibold">{{ __('Additional Information') }}</h3>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Tax ID') }}:</span>
                                                {{ $sponsor->tax_id ?? 'N/A' }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Commercial') }}:</span>
                                                {{ $sponsor->commercial->name ?? 'N/A' }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Postal Code') }}:</span>
                                                {{ $sponsor->postal_code ?? 'N/A' }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Country') }}:</span>
                                                {{ $sponsor->country ?? 'N/A' }}</div>
                                            <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                    class="font-medium dark:text-white text-zinc-800">{{ __('Status') }}:</span>
                                                {{ $sponsor->status_badge }}</div>
                                            @if($sponsor->logo)
                                                <div class="mb-1 dark:text-zinc-300 text-zinc-600 flex flex-col"><span
                                                        class="font-medium dark:text-white text-zinc-800">{{ __('Logo') }}:</span>
                                                    <img src="{{ $sponsor->logo_url }}" alt="{{ $sponsor->company_name }}" 
                                                         class="h-16 w-16 rounded object-cover mt-1">
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </flux:modal>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-zinc-500 dark:text-zinc-400">
                                {{ __('No sponsors found.') }}
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if ($sponsors->hasPages())
            <div class="border-t border-zinc-200 bg-white px-4 py-3 dark:border-zinc-700 dark:bg-zinc-900">
                {{ $sponsors->links() }}
            </div>
        @endif
    </div>

    <!-- Delete Confirmation Modal -->
    <flux:modal name="confirm-sponsor-deletion" title="{{ __('Confirm Deletion') }}">
        <div class="space-y-4">
            <p class="text-zinc-600 dark:text-zinc-400">
                {{ __('Are you sure you want to delete this sponsor? This action cannot be undone.') }}
            </p>

            <div class="flex justify-end space-x-3">
                <flux:button variant="outline" x-on:click="$wire.modal('confirm-sponsor-deletion').hide()">
                    {{ __('Cancel') }}
                </flux:button>

                <flux:button variant="danger" wire:click="deleteSponsor">
                    {{ __('Delete Sponsor') }}
                </flux:button>
            </div>
        </div>
    </flux:modal>
</div>
