<div class="w-full">
    <div class="mb-6 flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <!-- Breadcrumb -->
        <div>
            <flux:breadcrumbs class="flex-wrap">
                <flux:breadcrumbs.item :href="route('dashboard')" wire:navigate>{{ __('Dashboard') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item :href="route('clubs.index')" wire:navigate>{{ __('Clubs Management') }}</flux:breadcrumbs.item>
                <flux:breadcrumbs.item current>{{ __('Token Types for') }} {{ $club->club_name }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </div>
        
        <!-- Add Token Type Button -->
        <div>
            {{-- <flux:button variant="primary" wire:click="showAddForm" :disabled="$club->tokenTypes->count() >= 2" icon="plus">
                {{ __('Add Token Type') }}
            </flux:button> --}}
        </div>
    </div>

    
    <!-- Token Type Form -->
    @if ($showForm)
        <div class="mb-6 rounded-lg border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
            <flux:heading size="sm">{{ $isEditing ? __('Edit Token Type') : __('Add Token Type') }}</flux:heading>
            
            <form wire:submit.prevent="saveTokenType" class="mt-4 space-y-4">
                <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div>
                        <flux:input 
                            wire:model="value" 
                            :label="__('Value')" 
                            type="number" 
                            step="0.01" 
                            min="0.01" 
                            required 
                            :error="$errors->first('value')"
                        />
                    </div>
                    
                    <div>
                        <flux:input 
                            wire:model="minutes" 
                            :label="__('Minutes')" 
                            type="number" 
                            min="1" 
                            required 
                            :error="$errors->first('minutes')"
                            disabled
                        />
                    </div>
               
                </div>
                
                <div class="flex justify-end space-x-3">
                    <flux:button variant="outline" wire:click="cancelForm" type="button">
                        {{ __('Cancel') }}
                    </flux:button>
                    
                    <flux:button variant="primary" type="submit">
                        {{ $isEditing ? __('Update Token Type') : __('Create Token Type') }}
                    </flux:button>
                </div>
            </form>
        </div>
    @endif
    
    <!-- Token Types Table -->
    <div class="overflow-hidden rounded-lg border border-zinc-200 bg-white dark:border-zinc-700 dark:bg-zinc-900">
        <div class="overflow-x-auto">
            <table class="w-full divide-y divide-zinc-200 dark:divide-zinc-700">
                <thead class="bg-zinc-50 dark:bg-zinc-800">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('ID') }}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Value') }}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Value in EUR') }}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Minutes') }}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Currency') }}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Actions') }}
                        </th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-zinc-200 bg-white dark:divide-zinc-700 dark:bg-zinc-900">
                    @forelse ($club->tokenTypes as $tokenType)
                        <tr>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-white">
                                {{ $tokenType->id }}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-white">
                                {{ number_format($tokenType->value, 2) }}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-white">
                                {{ number_format($tokenType->value_in_eur, 2) }}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-white">
                                {{ $tokenType->minutes }}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-white">
                                {{ $tokenType->club?->currency ?? '' }}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                <div class="flex space-x-2">
                                    <flux:button variant="ghost" size="xs" icon="pencil" wire:click="showEditForm({{ $tokenType->id }})" />
                                    
                                    {{-- <flux:button 
                                        variant="ghost" 
                                        size="xs" 
                                        icon="trash" 
                                        wire:click="deleteTokenType({{ $tokenType->id }})"
                                        wire:confirm="Are you sure you want to delete this token type?"
                                    /> --}}
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="5" class="px-6 py-4 text-center text-sm text-zinc-500 dark:text-zinc-400">
                                {{ __('No token types found for this club.') }}
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- Back to Clubs Button -->
    <div class="mt-6">
        <flux:button variant="outline" :href="route('clubs.index')" wire:navigate>
            <flux:icon.arrow-left class="me-1" />
            {{ __('Back to Clubs') }}
        </flux:button>
    </div>
</div>
