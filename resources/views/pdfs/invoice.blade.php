<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice {{ $invoice->invoice_nbr }}</title>
    <style>
        @page {
            margin: 1cm;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #ffffff;
            color: #000000;
            font-size: 14px;
            line-height: 1.4;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .invoice-header {
            background-color: #3b82f6;
            color: #ffffff;
            padding: 24px;
            border-radius: 8px 8px 0 0;
        }

        .header-flex {
            display: table;
            width: 100%;
        }

        .header-left {
            display: table-cell;
            vertical-align: top;
            width: 50%;
        }

        .header-right {
            display: table-cell;
            vertical-align: top;
            width: 50%;
            text-align: right;
        }

        .invoice-title {
            font-size: 28px;
            font-weight: bold;
            margin: 0 0 8px 0;
        }

        .company-name {
            color: #bfdbfe;
            margin: 0;
        }

        .invoice-number {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .invoice-date {
            color: #bfdbfe;
            margin: 4px 0 0 0;
        }

        .info-section {
            background-color: #f9fafb;
            padding: 24px;
            border-left: 4px solid #3b82f6;
            border-right: 4px solid #3b82f6;
            border-bottom: 4px solid #3b82f6;
            border-radius: 0 0 8px 8px;
        }

        .info-grid {
            display: table;
            width: 100%;
        }

        .info-column {
            display: table-cell;
            vertical-align: top;
            width: 50%;
            padding-right: 32px;
        }

        .info-column:last-child {
            padding-right: 0;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            margin: 0 0 12px 0;
        }

        .company-info {
            color: #374151;
        }

        .company-info div {
            margin: 2px 0;
        }

        .company-name-bold {
            font-weight: 600;
        }

        .details-section {
            margin-top: 32px;
        }

        .details-grid {
            display: table;
            width: 100%;
            margin-bottom: 24px;
        }

        .detail-box {
            display: table-cell;
            width: 33.333%;
            padding: 16px;
            border-radius: 8px;
            margin-right: 24px;
        }

        .detail-box:last-child {
            margin-right: 0;
        }

        .detail-box-blue {
            background-color: #eff6ff;
        }

        .detail-box-green {
            background-color: #f0fdf4;
        }

        .detail-box-purple {
            background-color: #faf5ff;
        }

        .detail-label {
            font-size: 12px;
            font-weight: 500;
            margin: 0 0 4px 0;
        }

        .detail-label-blue {
            color: #2563eb;
        }

        .detail-label-green {
            color: #16a34a;
        }

        .detail-label-purple {
            color: #9333ea;
        }

        .detail-value {
            font-size: 16px;
            font-weight: bold;
            margin: 0;
        }

        .detail-value-blue {
            color: #1e40af;
        }

        .detail-value-green {
            color: #15803d;
        }

        .detail-value-purple {
            color: #7c2d12;
        }

        .items-section {
            margin-top: 32px;
        }

        .items-table {
            background-color: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
            width: 100%;
        }

        .table-header {
            background-color: #374151;
            color: #ffffff;
        }

        .table-header-row {
            display: table;
            width: 100%;
            padding: 16px;
            font-weight: 600;
        }

        .table-header-cell {
            display: table-cell;
            vertical-align: middle;
        }

        .header-item {
            width: 8.333%;
            text-align: center;
        }

        .header-description {
            width: 58.333%;
        }

        .header-qty {
            width: 16.666%;
            text-align: center;
        }

        .header-price {
            width: 16.666%;
            text-align: right;
        }

        .table-body {
            border-top: 1px solid #e5e7eb;
        }

        .table-row {
            display: table;
            width: 100%;
            padding: 16px;
            border-bottom: 1px solid #e5e7eb;
        }

        .table-row:last-child {
            border-bottom: none;
        }

        .table-row-negative {
            background-color: #fef2f2;
        }

        .table-cell {
            display: table-cell;
            vertical-align: middle;
        }

        .cell-item {
            width: 8.333%;
            text-align: center;
            font-weight: 500;
            color: #111827;
        }

        .cell-description {
            width: 58.333%;
            color: #374151;
        }

        .cell-qty {
            width: 16.666%;
            text-align: center;
            color: #111827;
            font-weight: 500;
        }

        .cell-price {
            width: 16.666%;
            text-align: right;
            font-weight: bold;
        }

        .cell-price-positive {
            color: #059669;
        }

        .cell-price-negative {
            color: #dc2626;
        }

        .table-footer {
            background-color: #374151;
            color: #ffffff;
        }

        .table-footer-row {
            display: table;
            width: 100%;
            padding: 16px;
        }

        .footer-label {
            display: table-cell;
            width: 83.333%;
            text-align: right;
            font-weight: bold;
            font-size: 16px;
        }

        .footer-value {
            display: table-cell;
            width: 16.666%;
            text-align: right;
            font-weight: bold;
            font-size: 18px;
        }

        .payment-section {
            margin-top: 32px;
            display: table;
            width: 100%;
        }

        .payment-column {
            display: table-cell;
            width: 50%;
            vertical-align: top;
            padding-right: 32px;
        }

        .payment-column:last-child {
            padding-right: 0;
        }

        .payment-box {
            padding: 24px;
            border-radius: 8px;
        }

        .payment-box-blue {
            background-color: #eff6ff;
        }

        .payment-box-gray {
            background-color: #f9fafb;
        }

        .payment-title {
            font-size: 16px;
            font-weight: 600;
            margin: 0 0 12px 0;
        }

        .payment-title-blue {
            color: #1e40af;
        }

        .payment-title-gray {
            color: #374151;
        }

        .payment-content {
            font-size: 14px;
        }

        .payment-content-blue {
            color: #1d4ed8;
        }

        .payment-content-gray {
            color: #374151;
        }

        .payment-item {
            margin: 8px 0;
        }

        .footer-section {
            margin-top: 48px;
            padding-top: 24px;
            border-top: 1px solid #d1d5db;
            text-align: center;
            color: #4b5563;
            font-size: 12px;
        }

        .footer-title {
            font-weight: 600;
            margin: 0 0 8px 0;
        }

        .footer-text {
            margin: 0;
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- Header -->
        <div class="invoice-header">
            <div class="header-flex">
                <div class="header-left">
                    <h1 class="invoice-title">INVOICE</h1>
                    <p class="company-name">PadelRating.ai</p>
                </div>
                <div class="header-right">
                    <div class="invoice-number">{{ $invoice->invoice_nbr }}</div>
                    <div class="invoice-date">{{ $invoice->date->format('d/m/Y') }}</div>
                </div>
            </div>
        </div>

        <!-- Company & Client Info -->
        <div class="info-section">
            <div class="info-grid">
                <!-- From -->
                <div class="info-column">
                    <h3 class="section-title">From:</h3>
                    <div class="company-info">
                        <div class="company-name-bold">Padel Rating & Stats</div>
                        <div>1 "Racho Dimchev" Str.</div>
                        <div>1-st floor, office 1, Sofia, 1000, Bulgaria</div>
                        <div>VAT: BG208186217</div>
                    </div>
                </div>

                <!-- To -->
                <div class="info-column">
                    <h3 class="section-title">Bill To:</h3>
                    <div class="company-info">
                        <div class="company-name-bold">{{ $invoice->club->club_name }}</div>
                        <div>{{ $invoice->club->email }}</div>
                        @if ($invoice->club->phone)
                            <div>{{ $invoice->club->country_code . $invoice->club->phone }}</div>
                        @endif
                        @if ($invoice->club->country)
                            <div>{{ $invoice->club->postal_code }} {{ $invoice->club->city }}, {{ $invoice->club->country }}</div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Invoice Details -->
        <div class="details-section">
            <div class="details-grid">
                <div class="detail-box detail-box-blue">
                    <div class="detail-label detail-label-blue">Invoice Number</div>
                    <div class="detail-value detail-value-blue">{{ $invoice->invoice_nbr }}</div>
                </div>
                <div class="detail-box detail-box-green">
                    <div class="detail-label detail-label-green">Invoice Date</div>
                    <div class="detail-value detail-value-green">{{ $invoice->date->format('d/m/Y') }}</div>
                </div>
                <div class="detail-box detail-box-purple">
                    <div class="detail-label detail-label-purple">Status</div>
                    <div class="detail-value detail-value-purple">{{ $invoice->status_text }}</div>
                </div>
            </div>
        </div>

        <!-- Invoice Items -->
        <div class="items-section">
            <div class="items-table">
                <div class="table-header">
                    <div class="table-header-row">
                        <div class="table-header-cell header-item">Item</div>
                        <div class="table-header-cell header-description">Description</div>
                        <div class="table-header-cell header-qty">Qty</div>
                        <div class="table-header-cell header-price">Total Price (EUR)</div>
                    </div>
                </div>

                <div class="table-body">
                    @foreach ($invoice->lines as $line)
                        <div class="table-row {{ $line->value < 0 ? 'table-row-negative' : '' }}">
                            <div class="table-cell cell-item">
                                {{ $line->line_nbr }}
                            </div>
                            <div class="table-cell cell-description">
                                {{ $line->description }}
                            </div>
                            <div class="table-cell cell-qty">
                                {{ $line->qty }}
                            </div>
                            <div class="table-cell cell-price {{ $line->value >= 0 ? 'cell-price-positive' : 'cell-price-negative' }}">
                                {{ number_format($line->value, 2) }}
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Total -->
                <div class="table-footer">
                    <div class="table-footer-row">
                        <div class="footer-label">
                            INVOICE VALUE (EUR):
                        </div>
                        <div class="footer-value">
                            {{ number_format($invoice->value, 2) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Information -->
        <div class="payment-section">
            <div class="payment-column">
                <div class="payment-box payment-box-blue">
                    <h3 class="payment-title payment-title-blue">Payment Information</h3>
                    <div class="payment-content payment-content-blue">
                        <div class="payment-item"><strong>Payment Method:</strong> {{ $invoice->pay_type_text }}</div>
                        <div class="payment-item"><strong>Currency:</strong> EUR</div>
                        <div class="payment-item"><strong>Period from:</strong> {{ $invoice->date->format('d/m/Y') }} to
                            {{ $invoice->date->addDays(7)->format('d/m/Y') }}</div>
                    </div>
                </div>
            </div>

            <div class="payment-column">
                <div class="payment-box payment-box-gray">
                    <h3 class="payment-title payment-title-gray">Notes</h3>
                    <div class="payment-content payment-content-gray">
                        <p class="payment-item">• Payment is due within 7 days of invoice date</p>
                        <p class="payment-item">• All amounts are calculated after club commission</p>
                        <p class="payment-item">• For questions, contact: <EMAIL></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer-section">
            <p class="footer-title">Thank you for your business!</p>
            <p class="footer-text">PadelRating.ai - Advanced AI Sports Analytics Platform</p>
            <p class="footer-text">Email: <EMAIL> | Website: www.padelrating.ai</p>
        </div>
    </div>
</body>

</html>
