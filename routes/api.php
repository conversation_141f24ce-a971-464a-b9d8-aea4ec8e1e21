<?php

use App\Http\Controllers\Api\Club\AuthController;
use App\Http\Controllers\Api\Club\CameraController;
use App\Http\Controllers\Api\Club\TokenTypeController;
use App\Http\Controllers\Api\Club\TokenController;
use App\Http\Controllers\Api\Player\TokenController as PlayerTokenController;
use App\Http\Controllers\Api\Player\AuthController as PlayerAuthController;
use App\Http\Controllers\Api\Player\CoachCommentsController;
use App\Http\Controllers\Api\Player\FavoriteClubController;
use App\Http\Controllers\Api\Player\RecordingController;
use App\Http\Controllers\Api\Player\StatsController;
use App\Http\Controllers\Api\Player\SubscriptionController;
use App\Http\Controllers\Api\StripeWebhookController;
use Illuminate\Support\Facades\Route;

// Club authentication routes
Route::prefix('auth')->controller(AuthController::class)->group(function () {
    Route::post('login', 'login');

    Route::middleware('auth:club')->group(function () {
        Route::post('change-password', 'changePassword');
        Route::post('logout', 'logout');
        Route::get('profile', 'profile');
        Route::post('update-fcm-token', 'updateFcmToken');
        Route::get('notifications', 'notifications');
        Route::get('unread-notifications-count', 'unreadNotificationsCount');
        Route::post('update-language', 'updateLanguage');
    });

    // Player authentication routes
    Route::prefix('player')->controller(PlayerAuthController::class)->group(function () {
        Route::post('register', 'register');
        Route::post('login', 'login');
        Route::post('verify-otp', 'verifyOtp');
        Route::post('resend-otp', 'resendOtp');
        Route::post('send-mobile-otp', 'sendMobileOtp');
        Route::post('verify-mobile-otp', 'verifyMobileOtp');

        Route::post('forgot-password', 'forgotPassword');
        Route::post('verify-forgot-password', 'verifyForgotPasswordOtp');
        Route::post('reset-password', 'resetPassword');

        Route::middleware(['auth:player', 'check.player.status'])->group(function () {
            Route::post('change-password', 'changePassword');
            Route::post('logout', 'logout');
            Route::get('profile', 'profile');
            Route::post('update-fcm-token', 'updateFcmToken');
            Route::get('notifications', 'notifications');
            Route::get('unread-notifications-count', 'unreadNotificationsCount');
            Route::post('update-profile', 'updateProfile');
            Route::post('update-image', 'updateImage');
            Route::post('update-language', 'updateLanguage');
            Route::post('delete-account', 'deleteAccount');
        });
    });
});

// Token types routes
Route::prefix('token-types')->controller(TokenTypeController::class)->group(function () {
    Route::middleware('auth:club')->group(function () {
        Route::get('/club', 'clubTokenTypes');
        Route::post('/', 'store');
    });
    Route::get('/{clubId?}', 'index');
});

// Club routes
Route::middleware('auth:club')->group(function () {
    // Tokens routes
    Route::prefix('tokens')->controller(TokenController::class)->group(function () {
        Route::get('/', 'index');
        Route::get('/requested', 'requested');
        Route::get('/redeem-requests', 'redeemTokensRequests');
        Route::post('/', 'store');
        Route::post('redeem', 'redeem');
        Route::post('accept-token', 'acceptToken');
        Route::post('reject-token', 'rejectToken');
        Route::get('token-requests-count', 'tokenRequestsCount');
    });

    // Cameras routes
    Route::prefix('cameras')->controller(CameraController::class)->group(function () {
        Route::get('/', 'index');
        Route::post('/', 'storeOrUpdate');
    });
});

// Player routes
Route::prefix('player')->group(function () {
    Route::middleware(['auth:player', 'check.player.status'])->group(function () {
        Route::prefix('tokens')->controller(PlayerTokenController::class)->group(function () {
            Route::get('/clubs', 'clubs');
            Route::post('/request', 'requestTokens');
            Route::get('/', 'index');
            Route::post('/redeem', 'redeem');
            Route::post('/use', 'useToken');
            Route::get('/credits', 'credits');
        });

        // Favorite Clubs routes
        Route::controller(FavoriteClubController::class)->group(function () {
            Route::post('favorites/toggle', 'toggle');
            Route::get('favorites', 'index');
        });

        // Recordings routes
        Route::prefix('recordings')->controller(RecordingController::class)->group(function () {
            Route::get('/', 'index');
            Route::post('/', 'storeOrUpdate');
            Route::post('/check-camera', 'checkCameraAvailability');
            Route::post('/check-status', 'checkRecordingStatus');
            Route::post('/delete', 'deleteRecording');
        });

        // Stats routes
        Route::prefix('stats')->controller(StatsController::class)->group(function () {
            Route::get('/', 'getPlayerStats');
            Route::get('/last-game', 'getGamesStats');
        });

        // Coach comments routes
        Route::prefix('coach-comments')->controller(CoachCommentsController::class)->group(function () {
            Route::get('/', 'getCommentsByGameIds');
            Route::get('/latest', 'getLatestComments');
            Route::get('/{gameId}', 'getGameComments');
        });
    });
});

// Player subscription routes
Route::prefix('player/subscriptions')->middleware(['auth:player', 'check.player.status'])->controller(SubscriptionController::class)->group(function () {
    Route::get('/plans', 'getPlans');
    Route::get('/current', 'getCurrentSubscription');
    Route::post('/checkout', 'createCheckoutSession');
    Route::post('/cancel', 'cancelSubscription');
    Route::post('/update-payment', 'updatePaymentMethod');
    Route::get('/transactions', 'getTransactionHistory');
});

// Subscription webhook routes
Route::get('subscription/success', [SubscriptionController::class, 'success'])->name('subscription.success');
Route::get('subscription/cancel', [SubscriptionController::class, 'cancel'])->name('subscription.cancel');

// Stripe webhook routes
Route::any('stripe/webhook', [StripeWebhookController::class, 'handleWebhook']);
Route::get('stripe/success', [StripeWebhookController::class, 'success'])->name('stripe.success');
Route::get('stripe/cancel', [StripeWebhookController::class, 'cancel'])->name('stripe.cancel');