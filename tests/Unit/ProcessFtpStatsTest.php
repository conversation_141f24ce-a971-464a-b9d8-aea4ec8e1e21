<?php

namespace Tests\Unit;

use App\Console\Commands\ProcessFtpStats;
use App\Models\CoachComment;
use App\Models\FtpFile;
use App\Models\Player;
use App\Models\PlayerComment;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class ProcessFtpStatsTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a mock storage disk
        Storage::fake('ftp');
    }

    /** @test */
    public function it_processes_coach_comment_file_without_corresponding_stats_file()
    {
        // Create a player
        $player = Player::factory()->create([
            'player_id' => 'TEST123'
        ]);

        // Create a coach comment
        $coachComment = CoachComment::factory()->create([
            'id' => 1,
            'message' => 'Test coach comment'
        ]);

        // Create a coach file
        $coachFile = FtpFile::create([
            'file_name' => '230525GHY6FD11001130C.CSV',
            'processed' => false
        ]);

        // Create CSV content
        $csvContent = "date,hour,player,mes_id\n";
        $csvContent .= "23/05/2023,10:30,TEST123,1\n";

        // Put the file in the fake storage
        Storage::disk('ftp')->put('230525GHY6FD11001130C.CSV', $csvContent);

        // Create the command instance
        $command = $this->app->make(ProcessFtpStats::class);

        // Call the processCoachFile method using reflection
        $reflection = new \ReflectionClass($command);
        $method = $reflection->getMethod('processCoachFile');
        $method->setAccessible(true);

        // Parse CSV data
        $data = array_map('str_getcsv', explode("\n", $csvContent));
        $headers = array_map('trim', $data[0]);

        // Call the method
        $method->invokeArgs($command, [$coachFile, $headers, $data]);

        // Assert that a player comment was created
        $this->assertDatabaseHas('player_comments', [
            'file_id' => $coachFile->id,
            'player_id' => 'TEST123',
            'coach_comment_id' => $coachComment->id,
        ]);
    }
}
